package com.trainticket.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name = "trajets")
public class Trajet {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true, length = 50)
    private String numeroTrajet;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "gare_depart_id", nullable = false)
    private Gare gareDepart;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "gare_arrivee_id", nullable = false)
    private Gare gareArrivee;

    @Column(nullable = false)
    private Integer dureeMinutes;

    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal prixBase;

    @Column(nullable = false)
    private Integer capaciteTotal = 200;

    @Column(nullable = false)
    private Boolean active = true;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TypeTrajet type = TypeTrajet.NORMAL;

    @Column(length = 500)
    private String description;

    @OneToMany(mappedBy = "trajet", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Voyage> voyages;

    // Constructeurs
    public Trajet() {}

    public Trajet(String numeroTrajet, Gare gareDepart, Gare gareArrivee,
                  Integer dureeMinutes, BigDecimal prixBase) {
        this.numeroTrajet = numeroTrajet;
        this.gareDepart = gareDepart;
        this.gareArrivee = gareArrivee;
        this.dureeMinutes = dureeMinutes;
        this.prixBase = prixBase;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNumeroTrajet() {
        return numeroTrajet;
    }

    public void setNumeroTrajet(String numeroTrajet) {
        this.numeroTrajet = numeroTrajet;
    }

    public Gare getGareDepart() {
        return gareDepart;
    }

    public void setGareDepart(Gare gareDepart) {
        this.gareDepart = gareDepart;
    }

    public Gare getGareArrivee() {
        return gareArrivee;
    }

    public void setGareArrivee(Gare gareArrivee) {
        this.gareArrivee = gareArrivee;
    }

    public Integer getDureeMinutes() {
        return dureeMinutes;
    }

    public void setDureeMinutes(Integer dureeMinutes) {
        this.dureeMinutes = dureeMinutes;
    }

    public BigDecimal getPrixBase() {
        return prixBase;
    }

    public void setPrixBase(BigDecimal prixBase) {
        this.prixBase = prixBase;
    }

    public Integer getCapaciteTotal() {
        return capaciteTotal;
    }

    public void setCapaciteTotal(Integer capaciteTotal) {
        this.capaciteTotal = capaciteTotal;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public TypeTrajet getType() {
        return type;
    }

    public void setType(TypeTrajet type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<Voyage> getVoyages() {
        return voyages;
    }

    public void setVoyages(List<Voyage> voyages) {
        this.voyages = voyages;
    }

    // Méthodes utilitaires
    public Duration getDuree() {
        return Duration.ofMinutes(dureeMinutes);
    }

    public String getDureeFormatee() {
        long heures = dureeMinutes / 60;
        long minutes = dureeMinutes % 60;
        return String.format("%dh%02d", heures, minutes);
    }

    public String getItineraire() {
        return gareDepart.getVille() + " → " + gareArrivee.getVille();
    }

    public boolean isDirect() {
        return type == TypeTrajet.DIRECT;
    }

    // Enums
    public enum TypeTrajet {
        NORMAL, DIRECT, EXPRESS
    }

    @Override
    public String toString() {
        return "Trajet{" +
                "id=" + id +
                ", numeroTrajet='" + numeroTrajet + '\'' +
                ", gareDepart=" + gareDepart.getNom() +
                ", gareArrivee=" + gareArrivee.getNom() +
                ", dureeMinutes=" + dureeMinutes +
                ", prixBase=" + prixBase +
                ", type=" + type +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Trajet trajet = (Trajet) o;
        return Objects.equals(id, trajet.id) && Objects.equals(numeroTrajet, trajet.numeroTrajet);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, numeroTrajet);
    }
}
