package com.trainticket.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trainticket.model.Gare;
import com.trainticket.model.Trajet;
import com.trainticket.model.Voyage;
import com.trainticket.service.RechercheService;
import com.trainticket.service.impl.RechercheServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RechercheServlet extends HttpServlet {

    private static final Logger logger = LoggerFactory.getLogger(RechercheServlet.class);
    private RechercheService rechercheService;
    private ObjectMapper objectMapper;

    @Override
    public void init() throws ServletException {
        super.init();
        this.rechercheService = new RechercheServiceImpl();
        this.objectMapper = new ObjectMapper();
        logger.info("RechercheServlet initialisé");
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String pathInfo = request.getPathInfo();

        if (pathInfo == null) {
            response.sendRedirect(request.getContextPath() + "/");
            return;
        }

        switch (pathInfo) {
            case "/search":
                showSearchPage(request, response);
                break;
            case "/results":
                showSearchResults(request, response);
                break;
            case "/voyage-details":
                showVoyageDetails(request, response);
                break;
            case "/api/villes":
                getVillesAPI(request, response);
                break;
            case "/api/gares":
                getGaresAPI(request, response);
                break;
            case "/api/suggestions":
                getSuggestionsAPI(request, response);
                break;
            case "/api/trajets-populaires":
                getTrajetsPopulairesAPI(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String pathInfo = request.getPathInfo();

        if (pathInfo == null) {
            response.sendRedirect(request.getContextPath() + "/");
            return;
        }

        switch (pathInfo) {
            case "/search":
                handleSearch(request, response);
                break;
            case "/advanced-search":
                handleAdvancedSearch(request, response);
                break;
            case "/api/verify-availability":
                verifyAvailabilityAPI(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    private void showSearchPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            // Charger les villes disponibles
            List<String> villes = rechercheService.getVillesDisponibles();
            request.setAttribute("villes", villes);

            // Charger les trajets populaires
            List<Trajet> trajetsPopulaires = rechercheService.getTrajetsPopulaires(5);
            request.setAttribute("trajetsPopulaires", trajetsPopulaires);

            request.getRequestDispatcher("/WEB-INF/views/search/search-form.jsp").forward(request, response);

        } catch (RechercheService.SearchException e) {
            logger.error("Erreur lors du chargement de la page de recherche", e);
            request.setAttribute("errorMessage", "Erreur lors du chargement de la page");
            request.getRequestDispatcher("/WEB-INF/views/error/500.jsp").forward(request, response);
        }
    }

    private void handleSearch(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String villeDepart = request.getParameter("villeDepart");
        String villeArrivee = request.getParameter("villeArrivee");
        String dateVoyageStr = request.getParameter("dateVoyage");
        String nombrePassagersStr = request.getParameter("nombrePassagers");
        boolean voyagesDirects = "on".equals(request.getParameter("voyagesDirects"));

        try {
            // Validation et conversion des paramètres
            if (dateVoyageStr == null || dateVoyageStr.trim().isEmpty()) {
                throw new IllegalArgumentException("Date de voyage requise");
            }
            LocalDate dateVoyage = LocalDate.parse(dateVoyageStr, DateTimeFormatter.ISO_LOCAL_DATE);
            int nombrePassagers = Integer.parseInt(nombrePassagersStr != null ? nombrePassagersStr : "1");

            // Effectuer la recherche
            List<Voyage> voyages = rechercheService.rechercherVoyages(
                villeDepart, villeArrivee, dateVoyage, nombrePassagers, voyagesDirects
            );

            // Rechercher aussi les voyages avec correspondance si demandé
            List<List<Voyage>> correspondances = null;
            if (!voyagesDirects) {
                correspondances = rechercheService.rechercherVoyagesAvecCorrespondance(
                    villeDepart, villeArrivee, dateVoyage
                );
            }

            // Préparer les données pour la vue
            request.setAttribute("voyages", voyages);
            request.setAttribute("correspondances", correspondances);
            request.setAttribute("villeDepart", villeDepart);
            request.setAttribute("villeArrivee", villeArrivee);
            request.setAttribute("dateVoyage", dateVoyage);
            request.setAttribute("nombrePassagers", nombrePassagers);
            request.setAttribute("voyagesDirects", voyagesDirects);

            // Ajouter des informations contextuelles si aucun résultat
            if (voyages.isEmpty() && (correspondances == null || correspondances.isEmpty())) {
                logger.info("Aucun voyage trouvé pour {} -> {} le {}", villeDepart, villeArrivee, dateVoyage);
                addNoResultsContext(request, villeDepart, villeArrivee, dateVoyage);
            }

            logger.info("Recherche effectuée: {} -> {} le {} pour {} passager(s), {} résultat(s)",
                       villeDepart, villeArrivee, dateVoyage, nombrePassagers, voyages.size());

            request.getRequestDispatcher("/WEB-INF/views/search/search-results.jsp").forward(request, response);

        } catch (DateTimeParseException e) {
            logger.warn("Format de date invalide: {}", dateVoyageStr);
            request.setAttribute("errorMessage", "Format de date invalide");
            showSearchPage(request, response);
        } catch (NumberFormatException e) {
            logger.warn("Nombre de passagers invalide: {}", nombrePassagersStr);
            request.setAttribute("errorMessage", "Nombre de passagers invalide");
            showSearchPage(request, response);
        } catch (IllegalArgumentException e) {
            logger.warn("Paramètre invalide: {}", e.getMessage());
            request.setAttribute("errorMessage", e.getMessage());
            showSearchPage(request, response);
        } catch (RechercheService.SearchException e) {
            logger.warn("Erreur de recherche: {}", e.getMessage());
            request.setAttribute("errorMessage", e.getMessage());
            showSearchPage(request, response);
        }
    }

    private void handleAdvancedSearch(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            // Créer les critères de recherche avancée
            RechercheService.CriteresRecherche criteres = new RechercheService.CriteresRecherche();

            criteres.setVilleDepart(request.getParameter("villeDepart"));
            criteres.setVilleArrivee(request.getParameter("villeArrivee"));

            String dateVoyageStr = request.getParameter("dateVoyage");
            if (dateVoyageStr != null && !dateVoyageStr.trim().isEmpty()) {
                criteres.setDateVoyage(LocalDate.parse(dateVoyageStr));
            }

            String nombrePassagersStr = request.getParameter("nombrePassagers");
            criteres.setNombrePassagers(Integer.parseInt(nombrePassagersStr != null ? nombrePassagersStr : "1"));

            criteres.setVoyagesDirectsUniquement("on".equals(request.getParameter("voyagesDirects")));

            // Critères avancés
            String dureeMaxStr = request.getParameter("dureeMax");
            if (dureeMaxStr != null && !dureeMaxStr.trim().isEmpty()) {
                criteres.setDureeMaxMinutes(Integer.parseInt(dureeMaxStr));
            }

            String prixMaxStr = request.getParameter("prixMax");
            if (prixMaxStr != null && !prixMaxStr.trim().isEmpty()) {
                criteres.setPrixMax(Double.parseDouble(prixMaxStr));
            }

            String typeTrajetStr = request.getParameter("typeTrajet");
            if (typeTrajetStr != null && !typeTrajetStr.trim().isEmpty()) {
                criteres.setTypeTrajet(Trajet.TypeTrajet.valueOf(typeTrajetStr));
            }

            String classeStr = request.getParameter("classe");
            if (classeStr != null && !classeStr.trim().isEmpty()) {
                criteres.setClassePreferee(Voyage.ClasseBillet.valueOf(classeStr));
            }

            // Effectuer la recherche avancée
            List<Voyage> voyages = rechercheService.rechercheAvancee(criteres);

            // Préparer les données pour la vue
            request.setAttribute("voyages", voyages);
            request.setAttribute("criteres", criteres);

            logger.info("Recherche avancée effectuée: {} résultat(s)", voyages.size());

            request.getRequestDispatcher("/WEB-INF/views/search/search-results.jsp").forward(request, response);

        } catch (Exception e) {
            logger.error("Erreur lors de la recherche avancée", e);
            request.setAttribute("errorMessage", "Erreur lors de la recherche avancée");
            showSearchPage(request, response);
        }
    }

    private void showSearchResults(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Cette méthode peut être utilisée pour afficher des résultats pré-calculés
        // ou rediriger vers la recherche si aucun paramètre n'est fourni

        String villeDepart = request.getParameter("villeDepart");
        String villeArrivee = request.getParameter("villeArrivee");
        String dateVoyage = request.getParameter("dateVoyage");

        if (villeDepart == null || villeArrivee == null || dateVoyage == null) {
            response.sendRedirect(request.getContextPath() + "/recherche/search");
            return;
        }

        // Rediriger vers la recherche avec les paramètres
        handleSearch(request, response);
    }

    private void showVoyageDetails(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String voyageIdStr = request.getParameter("id");

        if (voyageIdStr == null || voyageIdStr.trim().isEmpty()) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID du voyage requis");
            return;
        }

        try {
            Long voyageId = Long.parseLong(voyageIdStr);
            Voyage voyage = rechercheService.getVoyageDetails(voyageId);

            request.setAttribute("voyage", voyage);
            request.getRequestDispatcher("/WEB-INF/views/search/voyage-details.jsp").forward(request, response);

        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID du voyage invalide");
        } catch (RechercheService.SearchException e) {
            logger.error("Erreur lors de la récupération des détails du voyage {}", voyageIdStr, e);
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "Voyage non trouvé");
        }
    }

    // API endpoints

    private void getVillesAPI(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try {
            List<String> villes = rechercheService.getVillesDisponibles();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", villes);

            objectMapper.writeValue(response.getWriter(), result);

        } catch (RechercheService.SearchException e) {
            logger.error("Erreur lors de la récupération des villes", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Erreur lors de la récupération des villes");

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            objectMapper.writeValue(response.getWriter(), result);
        }
    }

    private void getGaresAPI(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        String searchTerm = request.getParameter("q");

        try {
            List<Gare> gares = rechercheService.rechercherGares(searchTerm);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", gares);

            objectMapper.writeValue(response.getWriter(), result);

        } catch (RechercheService.SearchException e) {
            logger.error("Erreur lors de la recherche de gares", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Erreur lors de la recherche de gares");

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            objectMapper.writeValue(response.getWriter(), result);
        }
    }

    private void getSuggestionsAPI(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        String villeDepart = request.getParameter("villeDepart");

        try {
            List<String> suggestions = rechercheService.getSuggestionsDestinations(villeDepart);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", suggestions);

            objectMapper.writeValue(response.getWriter(), result);

        } catch (RechercheService.SearchException e) {
            logger.error("Erreur lors de la récupération des suggestions", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Erreur lors de la récupération des suggestions");

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            objectMapper.writeValue(response.getWriter(), result);
        }
    }

    private void getTrajetsPopulairesAPI(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        String limitStr = request.getParameter("limit");
        int limit = limitStr != null ? Integer.parseInt(limitStr) : 10;

        try {
            List<Trajet> trajets = rechercheService.getTrajetsPopulaires(limit);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", trajets);

            objectMapper.writeValue(response.getWriter(), result);

        } catch (RechercheService.SearchException e) {
            logger.error("Erreur lors de la récupération des trajets populaires", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Erreur lors de la récupération des trajets populaires");

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            objectMapper.writeValue(response.getWriter(), result);
        }
    }

    private void verifyAvailabilityAPI(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try {
            Long voyageId = Long.parseLong(request.getParameter("voyageId"));
            int nombrePassagers = Integer.parseInt(request.getParameter("nombrePassagers"));
            Voyage.ClasseBillet classe = Voyage.ClasseBillet.valueOf(request.getParameter("classe"));

            boolean disponible = rechercheService.verifierDisponibilite(voyageId, nombrePassagers, classe);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("available", disponible);

            objectMapper.writeValue(response.getWriter(), result);

        } catch (Exception e) {
            logger.error("Erreur lors de la vérification de disponibilité", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Erreur lors de la vérification");

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            objectMapper.writeValue(response.getWriter(), result);
        }
    }

    /**
     * Ajoute des informations contextuelles lorsqu'aucun résultat n'est trouvé
     */
    private void addNoResultsContext(HttpServletRequest request, String villeDepart,
                                   String villeArrivee, LocalDate dateVoyage) {
        try {
            // Vérifier s'il y a des voyages à d'autres dates
            boolean voyagesAutresDates = false;

            LocalDate datePrecedente = dateVoyage.minusDays(1);
            LocalDate dateSuivante = dateVoyage.plusDays(1);

            try {
                List<Voyage> voyagesPrecedents = rechercheService.rechercherVoyagesDirects(villeDepart, villeArrivee, datePrecedente);
                List<Voyage> voyagesSuivants = rechercheService.rechercherVoyagesDirects(villeDepart, villeArrivee, dateSuivante);
                voyagesAutresDates = !voyagesPrecedents.isEmpty() || !voyagesSuivants.isEmpty();
                logger.debug("Voyages autres dates: {} (précédents: {}, suivants: {})",
                           voyagesAutresDates, voyagesPrecedents.size(), voyagesSuivants.size());
            } catch (RechercheService.SearchException e) {
                // Ignorer les erreurs de recherche pour les autres dates
                logger.debug("Erreur lors de la recherche d'autres dates: {}", e.getMessage());
                voyagesAutresDates = false;
            }

            // Obtenir des suggestions de destinations alternatives
            List<String> destinationsAlternatives = new ArrayList<>();
            try {
                destinationsAlternatives = rechercheService.getSuggestionsDestinations(villeDepart);
                if (destinationsAlternatives != null) {
                    destinationsAlternatives.remove(villeArrivee); // Retirer la destination recherchée

                    // Limiter à 5 suggestions
                    if (destinationsAlternatives.size() > 5) {
                        destinationsAlternatives = destinationsAlternatives.subList(0, 5);
                    }
                }
            } catch (Exception e) {
                logger.debug("Erreur lors de la récupération des suggestions: {}", e.getMessage());
                destinationsAlternatives = new ArrayList<>();
            }

            request.setAttribute("voyagesAutresDates", voyagesAutresDates);
            request.setAttribute("destinationsAlternatives", destinationsAlternatives);

            // Ajouter des messages contextuels
            if (!voyagesAutresDates) {
                request.setAttribute("noResultsReason", "NO_SCHEDULE");
                request.setAttribute("noResultsMessage",
                    "Aucun voyage n'est programmé entre " + villeDepart + " et " + villeArrivee + " pour le moment.");
            } else {
                request.setAttribute("noResultsReason", "DATE_UNAVAILABLE");
                request.setAttribute("noResultsMessage",
                    "Aucun voyage disponible pour cette date, mais il y en a à d'autres dates.");
            }

            logger.info("Contexte aucun résultat ajouté: raison={}, alternatives={}",
                       request.getAttribute("noResultsReason"), destinationsAlternatives.size());

        } catch (Exception e) {
            logger.warn("Erreur lors de l'ajout du contexte pour aucun résultat", e);
            request.setAttribute("noResultsReason", "UNKNOWN");
            request.setAttribute("noResultsMessage",
                "Aucun voyage trouvé pour votre recherche.");
            request.setAttribute("destinationsAlternatives", new ArrayList<>());
        }
    }
}
