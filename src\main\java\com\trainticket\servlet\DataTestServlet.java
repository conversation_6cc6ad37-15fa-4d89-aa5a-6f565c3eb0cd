package com.trainticket.servlet;

import com.trainticket.dao.*;
import com.trainticket.dao.impl.*;
import com.trainticket.model.*;
import com.trainticket.util.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.Transaction;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Servlet pour tester et initialiser les données de test
 */
public class DataTestServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("text/html; charset=UTF-8");
        PrintWriter out = response.getWriter();

        out.println("<!DOCTYPE html>");
        out.println("<html><head><title>Test des Données</title>");
        out.println("<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>");
        out.println("</head><body>");
        out.println("<div class='container mt-4'>");
        out.println("<h1>Test des Données de l'Application</h1>");

        try {
            // Tester la connexion à la base de données
            Session session = HibernateUtil.getSessionFactory().openSession();
            out.println("<div class='alert alert-success'>✅ Connexion à la base de données réussie</div>");

            // Vérifier les données existantes
            GareDAO gareDAO = new GareDAOImpl();
            TrajetDAO trajetDAO = new TrajetDAOImpl();
            VoyageDAO voyageDAO = new VoyageDAOImpl();
            UtilisateurDAO utilisateurDAO = new UtilisateurDAOImpl();

            List<Gare> gares = gareDAO.findAll();
            List<Trajet> trajets = trajetDAO.findAll();
            List<Voyage> voyages = voyageDAO.findAll();
            List<Utilisateur> utilisateurs = utilisateurDAO.findAll();

            out.println("<h3>État actuel de la base de données :</h3>");
            out.println("<ul class='list-group mb-4'>");
            out.println("<li class='list-group-item'>Gares : " + gares.size() + "</li>");
            out.println("<li class='list-group-item'>Trajets : " + trajets.size() + "</li>");
            out.println("<li class='list-group-item'>Voyages : " + voyages.size() + "</li>");
            out.println("<li class='list-group-item'>Utilisateurs : " + utilisateurs.size() + "</li>");
            out.println("</ul>");

            // Si pas de données, proposer d'en créer
            if (gares.isEmpty() || trajets.isEmpty() || voyages.isEmpty()) {
                out.println("<div class='alert alert-warning'>");
                out.println("⚠️ Peu ou pas de données trouvées. ");
                out.println("<a href='" + request.getContextPath() + "/data-test?action=create' class='btn btn-primary'>Créer des données de test</a>");
                out.println("</div>");
            } else {
                out.println("<div class='alert alert-info'>");
                out.println("ℹ️ Données existantes trouvées. ");
                out.println("<a href='" + request.getContextPath() + "/data-test?action=recreate' class='btn btn-warning'>Recréer les données</a>");
                out.println("</div>");
            }

            // Afficher quelques exemples de données
            if (!gares.isEmpty()) {
                out.println("<h4>Exemples de gares :</h4>");
                out.println("<ul>");
                for (int i = 0; i < Math.min(5, gares.size()); i++) {
                    Gare gare = gares.get(i);
                    out.println("<li>" + gare.getNom() + " (" + gare.getVille() + ")</li>");
                }
                out.println("</ul>");
            }

            if (!voyages.isEmpty()) {
                out.println("<h4>Exemples de voyages :</h4>");
                out.println("<ul>");
                for (int i = 0; i < Math.min(5, voyages.size()); i++) {
                    Voyage voyage = voyages.get(i);
                    out.println("<li>" + voyage.getTrajet().getGareDepart().getVille() +
                               " → " + voyage.getTrajet().getGareArrivee().getVille() +
                               " le " + voyage.getDateHeureDepart() + "</li>");
                }
                out.println("</ul>");
            }

            session.close();

        } catch (Exception e) {
            out.println("<div class='alert alert-danger'>❌ Erreur : " + e.getMessage() + "</div>");
            e.printStackTrace();
        }

        out.println("<a href='" + request.getContextPath() + "/' class='btn btn-secondary'>Retour à l'accueil</a>");
        out.println("</div></body></html>");
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String action = request.getParameter("action");

        if ("create".equals(action) || "recreate".equals(action)) {
            createTestData("recreate".equals(action));
        }

        response.sendRedirect(request.getContextPath() + "/data-test");
    }

    private void createTestData(boolean recreate) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction transaction = null;

        try {
            transaction = session.beginTransaction();

            if (recreate) {
                // Supprimer les données existantes
                session.createQuery("DELETE FROM Billet").executeUpdate();
                session.createQuery("DELETE FROM Voyage").executeUpdate();
                session.createQuery("DELETE FROM Trajet").executeUpdate();
                session.createQuery("DELETE FROM Gare").executeUpdate();
                session.createQuery("DELETE FROM Utilisateur WHERE role != 'ADMIN'").executeUpdate();
            }

            // Créer des gares
            Gare tunis = new Gare("Gare Centrale de Tunis", "Tunis", "Avenue Habib Bourguiba");
            Gare sfax = new Gare("Gare de Sfax", "Sfax", "Avenue de la République");
            Gare sousse = new Gare("Gare de Sousse", "Sousse", "Boulevard Yahya Ibn Omar");
            Gare kairouan = new Gare("Gare de Kairouan", "Kairouan", "Avenue de la Liberté");
            Gare bizerte = new Gare("Gare de Bizerte", "Bizerte", "Rue de la Gare");
            Gare gabes = new Gare("Gare de Gabès", "Gabès", "Avenue Farhat Hached");

            session.save(tunis);
            session.save(sfax);
            session.save(sousse);
            session.save(kairouan);
            session.save(bizerte);
            session.save(gabes);

            // Créer des trajets
            Trajet trajet1 = new Trajet("TN001", tunis, sfax, 280, new BigDecimal("45.50"));
            trajet1.setType(Trajet.TypeTrajet.EXPRESS);

            Trajet trajet2 = new Trajet("TN002", sfax, tunis, 280, new BigDecimal("45.50"));
            trajet2.setType(Trajet.TypeTrajet.EXPRESS);

            Trajet trajet3 = new Trajet("TN003", tunis, sousse, 140, new BigDecimal("25.00"));
            trajet3.setType(Trajet.TypeTrajet.DIRECT);

            Trajet trajet4 = new Trajet("TN004", sousse, tunis, 140, new BigDecimal("25.00"));
            trajet4.setType(Trajet.TypeTrajet.DIRECT);

            Trajet trajet5 = new Trajet("TN005", tunis, kairouan, 160, new BigDecimal("30.00"));
            trajet5.setType(Trajet.TypeTrajet.NORMAL);

            Trajet trajet6 = new Trajet("TN006", kairouan, tunis, 160, new BigDecimal("30.00"));
            trajet6.setType(Trajet.TypeTrajet.NORMAL);

            Trajet trajet7 = new Trajet("TN007", sfax, sousse, 120, new BigDecimal("20.00"));
            trajet7.setType(Trajet.TypeTrajet.DIRECT);

            Trajet trajet8 = new Trajet("TN008", sousse, sfax, 120, new BigDecimal("20.00"));
            trajet8.setType(Trajet.TypeTrajet.DIRECT);

            session.save(trajet1);
            session.save(trajet2);
            session.save(trajet3);
            session.save(trajet4);
            session.save(trajet5);
            session.save(trajet6);
            session.save(trajet7);
            session.save(trajet8);

            // Créer des voyages pour les prochains jours
            LocalDateTime now = LocalDateTime.now();

            // Voyages pour aujourd'hui
            createVoyagesForDay(session, trajet1, now.plusHours(2));
            createVoyagesForDay(session, trajet2, now.plusHours(3));
            createVoyagesForDay(session, trajet3, now.plusHours(1));
            createVoyagesForDay(session, trajet4, now.plusHours(4));

            // Voyages pour demain
            LocalDateTime tomorrow = now.plusDays(1).withHour(8).withMinute(0);
            createVoyagesForDay(session, trajet1, tomorrow);
            createVoyagesForDay(session, trajet2, tomorrow.plusHours(2));
            createVoyagesForDay(session, trajet3, tomorrow.plusHours(1));
            createVoyagesForDay(session, trajet4, tomorrow.plusHours(3));
            createVoyagesForDay(session, trajet5, tomorrow.plusHours(4));
            createVoyagesForDay(session, trajet6, tomorrow.plusHours(5));
            createVoyagesForDay(session, trajet7, tomorrow.plusHours(6));
            createVoyagesForDay(session, trajet8, tomorrow.plusHours(7));

            // Voyages pour après-demain
            LocalDateTime dayAfter = now.plusDays(2).withHour(8).withMinute(0);
            createVoyagesForDay(session, trajet1, dayAfter);
            createVoyagesForDay(session, trajet2, dayAfter.plusHours(2));
            createVoyagesForDay(session, trajet3, dayAfter.plusHours(1));
            createVoyagesForDay(session, trajet4, dayAfter.plusHours(3));

            // Créer un utilisateur de test s'il n'existe pas
            UtilisateurDAO utilisateurDAO = new UtilisateurDAOImpl();
            if (utilisateurDAO.findByEmail("<EMAIL>") == null) {
                Utilisateur testUser = new Utilisateur();
                testUser.setEmail("<EMAIL>");
                testUser.setMotDePasse("password123"); // En production, il faudrait hasher
                testUser.setNom("Test");
                testUser.setPrenom("Utilisateur");
                testUser.setRole(Utilisateur.Role.USER);
                testUser.setStatut(Utilisateur.StatutCompte.ACTIF);
                session.save(testUser);
            }

            transaction.commit();
            System.out.println("Données de test créées avec succès !");

        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            e.printStackTrace();
            throw new RuntimeException("Erreur lors de la création des données de test", e);
        } finally {
            session.close();
        }
    }

    private void createVoyagesForDay(Session session, Trajet trajet, LocalDateTime baseTime) {
        // Créer 3 voyages dans la journée
        for (int i = 0; i < 3; i++) {
            LocalDateTime departTime = baseTime.plusHours(i * 4);
            Voyage voyage = new Voyage(trajet, departTime);
            voyage.setStatut(Voyage.StatutVoyage.PROGRAMME);
            session.save(voyage);
        }
    }
}
