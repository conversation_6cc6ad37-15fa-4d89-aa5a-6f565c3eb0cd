package com.trainticket.dao.impl;

import com.trainticket.dao.BilletDAO;
import com.trainticket.model.Billet;
import com.trainticket.model.Utilisateur;
import com.trainticket.model.Voyage;
import com.trainticket.util.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.query.Query;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public class BilletDAOImpl extends BaseDAOImpl<Billet, Long> implements BilletDAO {
    
    @Override
    public Optional<Billet> findByNumeroBillet(String numeroBillet) {
        String hql = "FROM Billet b WHERE b.numeroBillet = ?0";
        return executeSingleResultQuery(hql, numeroBillet);
    }
    
    @Override
    public List<Billet> findByUtilisateur(Utilisateur utilisateur) {
        String hql = "FROM Billet b WHERE b.utilisateur = ?0 ORDER BY b.dateAchat DESC";
        return executeQuery(hql, utilisateur);
    }
    
    @Override
    public List<Billet> findByVoyage(Voyage voyage) {
        String hql = "FROM Billet b WHERE b.voyage = ?0 ORDER BY b.dateAchat";
        return executeQuery(hql, voyage);
    }
    
    @Override
    public List<Billet> findByStatut(Billet.StatutBillet statut) {
        String hql = "FROM Billet b WHERE b.statut = ?0 ORDER BY b.dateAchat DESC";
        return executeQuery(hql, statut);
    }
    
    @Override
    public List<Billet> findByUtilisateurAndStatut(Utilisateur utilisateur, Billet.StatutBillet statut) {
        String hql = "FROM Billet b WHERE b.utilisateur = ?0 AND b.statut = ?1 " +
                    "ORDER BY b.dateAchat DESC";
        return executeQuery(hql, utilisateur, statut);
    }
    
    @Override
    public List<Billet> findPurchasedBillets(Utilisateur utilisateur) {
        return findByUtilisateurAndStatut(utilisateur, Billet.StatutBillet.ACHETE);
    }
    
    @Override
    public List<Billet> findUserHistory(Utilisateur utilisateur) {
        String hql = "FROM Billet b WHERE b.utilisateur = ?0 " +
                    "ORDER BY b.voyage.dateHeureDepart DESC, b.dateAchat DESC";
        return executeQuery(hql, utilisateur);
    }
    
    @Override
    public List<Billet> findByClasse(Voyage.ClasseBillet classe) {
        String hql = "FROM Billet b WHERE b.classe = ?0 ORDER BY b.dateAchat DESC";
        return executeQuery(hql, classe);
    }
    
    @Override
    public List<Billet> findByDateAchat(LocalDate date) {
        String hql = "FROM Billet b WHERE DATE(b.dateAchat) = ?0 ORDER BY b.dateAchat DESC";
        return executeQuery(hql, date);
    }
    
    @Override
    public List<Billet> findByDateRange(LocalDateTime dateDebut, LocalDateTime dateFin) {
        String hql = "FROM Billet b WHERE b.dateAchat BETWEEN ?0 AND ?1 " +
                    "ORDER BY b.dateAchat DESC";
        return executeQuery(hql, dateDebut, dateFin);
    }
    
    @Override
    public List<Billet> findCancellableBillets(Utilisateur utilisateur) {
        String hql = "FROM Billet b WHERE b.utilisateur = ?0 " +
                    "AND b.statut = 'ACHETE' " +
                    "AND b.voyage.dateHeureDepart > CURRENT_TIMESTAMP + INTERVAL 2 HOUR " +
                    "ORDER BY b.voyage.dateHeureDepart";
        return executeQuery(hql, utilisateur);
    }
    
    @Override
    public List<Billet> findUsableBillets(Utilisateur utilisateur) {
        String hql = "FROM Billet b WHERE b.utilisateur = ?0 " +
                    "AND b.statut = 'ACHETE' " +
                    "AND b.voyage.dateHeureDepart >= CURRENT_TIMESTAMP " +
                    "ORDER BY b.voyage.dateHeureDepart";
        return executeQuery(hql, utilisateur);
    }
    
    @Override
    public long countByStatut(Billet.StatutBillet statut) {
        String hql = "SELECT COUNT(b) FROM Billet b WHERE b.statut = ?0";
        return executeCountQuery(hql, statut);
    }
    
    @Override
    public long countByUtilisateur(Utilisateur utilisateur) {
        String hql = "SELECT COUNT(b) FROM Billet b WHERE b.utilisateur = ?0";
        return executeCountQuery(hql, utilisateur);
    }
    
    @Override
    public long countByVoyage(Voyage voyage) {
        String hql = "SELECT COUNT(b) FROM Billet b WHERE b.voyage = ?0";
        return executeCountQuery(hql, voyage);
    }
    
    @Override
    public boolean existsByNumeroBillet(String numeroBillet) {
        String hql = "SELECT COUNT(b) FROM Billet b WHERE b.numeroBillet = ?0";
        return executeCountQuery(hql, numeroBillet) > 0;
    }
    
    @Override
    public Double calculateTotalRevenue() {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "SELECT SUM(b.prix) FROM Billet b WHERE b.statut IN ('ACHETE', 'UTILISE')";
            Query<Double> query = session.createQuery(hql, Double.class);
            Double result = query.getSingleResult();
            return result != null ? result : 0.0;
        } catch (Exception e) {
            logger.error("Erreur lors du calcul du chiffre d'affaires total", e);
            return 0.0;
        }
    }
    
    @Override
    public Double calculateRevenueByPeriod(LocalDateTime dateDebut, LocalDateTime dateFin) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "SELECT SUM(b.prix) FROM Billet b " +
                        "WHERE b.statut IN ('ACHETE', 'UTILISE') " +
                        "AND b.dateAchat BETWEEN ?1 AND ?2";
            Query<Double> query = session.createQuery(hql, Double.class);
            query.setParameter(1, dateDebut);
            query.setParameter(2, dateFin);
            Double result = query.getSingleResult();
            return result != null ? result : 0.0;
        } catch (Exception e) {
            logger.error("Erreur lors du calcul du chiffre d'affaires par période", e);
            return 0.0;
        }
    }
    
    @Override
    public List<Billet> findWithPagination(int page, int size) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "FROM Billet b ORDER BY b.dateAchat DESC";
            Query<Billet> query = session.createQuery(hql, Billet.class);
            query.setFirstResult(page * size);
            query.setMaxResults(size);
            return query.getResultList();
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération paginée des billets", e);
            throw new RuntimeException("Erreur lors de la récupération", e);
        }
    }
    
    /**
     * Trouver les billets d'aujourd'hui
     * @return liste des billets achetés aujourd'hui
     */
    public List<Billet> findTodayBillets() {
        return findByDateAchat(LocalDate.now());
    }
    
    /**
     * Trouver les billets par classe et statut
     * @param classe la classe
     * @param statut le statut
     * @return liste des billets correspondants
     */
    public List<Billet> findByClasseAndStatut(Voyage.ClasseBillet classe, Billet.StatutBillet statut) {
        String hql = "FROM Billet b WHERE b.classe = ?0 AND b.statut = ?1 " +
                    "ORDER BY b.dateAchat DESC";
        return executeQuery(hql, classe, statut);
    }
    
    /**
     * Calculer le chiffre d'affaires d'aujourd'hui
     * @return chiffre d'affaires du jour
     */
    public Double calculateTodayRevenue() {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        LocalDateTime endOfDay = startOfDay.plusDays(1);
        return calculateRevenueByPeriod(startOfDay, endOfDay);
    }
    
    /**
     * Calculer le chiffre d'affaires du mois
     * @return chiffre d'affaires du mois
     */
    public Double calculateMonthRevenue() {
        LocalDateTime startOfMonth = LocalDate.now().withDayOfMonth(1).atStartOfDay();
        LocalDateTime endOfMonth = startOfMonth.plusMonths(1);
        return calculateRevenueByPeriod(startOfMonth, endOfMonth);
    }
    
    /**
     * Trouver les billets expirés (voyage passé mais billet non utilisé)
     * @return liste des billets expirés
     */
    public List<Billet> findExpiredBillets() {
        String hql = "FROM Billet b WHERE b.statut = 'ACHETE' " +
                    "AND b.voyage.dateHeureArrivee < CURRENT_TIMESTAMP " +
                    "ORDER BY b.voyage.dateHeureArrivee DESC";
        return executeQuery(hql);
    }
    
    /**
     * Compter les billets par classe
     * @param classe la classe
     * @return nombre de billets de cette classe
     */
    public long countByClasse(Voyage.ClasseBillet classe) {
        String hql = "SELECT COUNT(b) FROM Billet b WHERE b.classe = ?0";
        return executeCountQuery(hql, classe);
    }
    
    /**
     * Trouver les billets les plus récents
     * @param limit nombre maximum de billets à retourner
     * @return liste des billets les plus récents
     */
    public List<Billet> findRecentBillets(int limit) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "FROM Billet b ORDER BY b.dateAchat DESC";
            Query<Billet> query = session.createQuery(hql, Billet.class);
            query.setMaxResults(limit);
            return query.getResultList();
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des billets récents", e);
            throw new RuntimeException("Erreur lors de la récupération", e);
        }
    }
    
    /**
     * Calculer le prix moyen des billets
     * @return prix moyen
     */
    public Double calculateAveragePrice() {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "SELECT AVG(b.prix) FROM Billet b WHERE b.statut IN ('ACHETE', 'UTILISE')";
            Query<Double> query = session.createQuery(hql, Double.class);
            Double result = query.getSingleResult();
            return result != null ? result : 0.0;
        } catch (Exception e) {
            logger.error("Erreur lors du calcul du prix moyen", e);
            return 0.0;
        }
    }
}
