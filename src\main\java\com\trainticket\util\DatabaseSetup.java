package com.trainticket.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * Utilitaire pour créer la base de données si elle n'existe pas
 */
public class DatabaseSetup {
    
    private static final String MYSQL_URL = "***************************/?useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true";
    private static final String DB_NAME = "train_ticket_db";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "root";
    
    public static void main(String[] args) {
        createDatabaseIfNotExists();
    }
    
    /**
     * Crée la base de données si elle n'existe pas
     */
    public static void createDatabaseIfNotExists() {
        try {
            // Charger le driver MySQL
            Class.forName("com.mysql.cj.jdbc.Driver");
            
            // Se connecter à MySQL (sans spécifier de base de données)
            try (Connection connection = DriverManager.getConnection(MYSQL_URL, USERNAME, PASSWORD);
                 Statement statement = connection.createStatement()) {
                
                // Créer la base de données si elle n'existe pas
                String createDbQuery = "CREATE DATABASE IF NOT EXISTS " + DB_NAME + 
                                     " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
                statement.executeUpdate(createDbQuery);
                
                System.out.println("✅ Base de données '" + DB_NAME + "' créée ou vérifiée avec succès");
                
                // Vérifier la connexion à la base de données créée
                testDatabaseConnection();
                
            }
            
        } catch (ClassNotFoundException e) {
            System.err.println("❌ Driver MySQL non trouvé: " + e.getMessage());
            System.err.println("Assurez-vous que mysql-connector-java est dans le classpath");
        } catch (SQLException e) {
            System.err.println("❌ Erreur SQL: " + e.getMessage());
            System.err.println("Vérifiez que MySQL est démarré et que les identifiants sont corrects");
        }
    }
    
    /**
     * Test la connexion à la base de données
     */
    private static void testDatabaseConnection() {
        String dbUrl = "***************************/" + DB_NAME + "?useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true";
        
        try (Connection connection = DriverManager.getConnection(dbUrl, USERNAME, PASSWORD)) {
            System.out.println("✅ Connexion à la base de données '" + DB_NAME + "' réussie");
            System.out.println("📊 Catalogue: " + connection.getCatalog());
            
        } catch (SQLException e) {
            System.err.println("❌ Erreur de connexion à la base de données: " + e.getMessage());
        }
    }
    
    /**
     * Affiche les informations de configuration
     */
    public static void printConfiguration() {
        System.out.println("🔧 Configuration de la base de données:");
        System.out.println("   URL: " + MYSQL_URL);
        System.out.println("   Base de données: " + DB_NAME);
        System.out.println("   Utilisateur: " + USERNAME);
        System.out.println("   Mot de passe: " + (PASSWORD.isEmpty() ? "(vide)" : "***"));
    }
}
