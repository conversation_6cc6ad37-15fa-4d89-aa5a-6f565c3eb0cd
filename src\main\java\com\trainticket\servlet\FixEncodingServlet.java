package com.trainticket.servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.sql.DriverManager;

@WebServlet("/fix-encoding")
public class FixEncodingServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    private static final String URL = "********************************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "123";

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("text/html; charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = response.getWriter();

        out.println("<!DOCTYPE html>");
        out.println("<html><head>");
        out.println("<meta charset='UTF-8'>");
        out.println("<title>Fix Encoding</title>");
        out.println("</head><body>");
        out.println("<h1>Correction de l'encodage des villes</h1>");

        try (Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD)) {
            // Corriger Gabès
            String updateGabes = "UPDATE gares SET ville = ?, nom = ?, adresse = ? WHERE id = 10";
            try (PreparedStatement stmt = conn.prepareStatement(updateGabes)) {
                stmt.setString(1, "Gabès");
                stmt.setString(2, "Gare de Gabès");
                stmt.setString(3, "Avenue Farhat Hached, Gabès");
                int updated = stmt.executeUpdate();
                out.println("<p>Gabès corrigé: " + updated + " ligne(s) mise(s) à jour</p>");
            }

            // Corriger Béja
            String updateBeja = "UPDATE gares SET ville = ?, nom = ? WHERE ville LIKE ? OR nom LIKE ?";
            try (PreparedStatement stmt = conn.prepareStatement(updateBeja)) {
                stmt.setString(1, "Béja");
                stmt.setString(2, "Gare de Béja");
                stmt.setString(3, "%B%ja%");
                stmt.setString(4, "%B%ja%");
                int updated = stmt.executeUpdate();
                out.println("<p>Béja corrigé: " + updated + " ligne(s) mise(s) à jour</p>");
            }

            // Vérifier les résultats
            out.println("<h2>Vérification des résultats:</h2>");
            String selectQuery = "SELECT id, ville, nom, adresse FROM gares ORDER BY ville";
            try (PreparedStatement stmt = conn.prepareStatement(selectQuery);
                 ResultSet rs = stmt.executeQuery()) {

                out.println("<table border='1'>");
                out.println("<tr><th>ID</th><th>Ville</th><th>Nom</th><th>Adresse</th></tr>");

                while (rs.next()) {
                    out.println("<tr>");
                    out.println("<td>" + rs.getInt("id") + "</td>");
                    out.println("<td>" + rs.getString("ville") + "</td>");
                    out.println("<td>" + rs.getString("nom") + "</td>");
                    out.println("<td>" + rs.getString("adresse") + "</td>");
                    out.println("</tr>");
                }
                out.println("</table>");
            }

        } catch (SQLException e) {
            out.println("<p style='color: red;'>Erreur: " + e.getMessage() + "</p>");
            e.printStackTrace();
        }

        out.println("<p><a href='/train-ticket/test/search'>Retour aux tests</a></p>");
        out.println("</body></html>");
    }
}
