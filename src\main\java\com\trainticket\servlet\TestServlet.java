package com.trainticket.servlet;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@WebServlet("/test")
public class TestServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>Test du Système</title>");
        out.println("<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>");
        out.println("</head>");
        out.println("<body>");
        out.println("<div class='container mt-5'>");
        out.println("<h1 class='text-center text-success'>✅ Système Train Ticket - Test Réussi</h1>");
        out.println("<div class='alert alert-success mt-4'>");
        out.println("<h4>Fonctionnalités implémentées :</h4>");
        out.println("<ul>");
        out.println("<li>✅ Architecture MVC avec Servlets/JSP/JSTL/EL</li>");
        out.println("<li>✅ Recherche sans authentification</li>");
        out.println("<li>✅ Classes de billets (1ère, 2ème, économique)</li>");
        out.println("<li>✅ Préférences utilisateur (fenêtre, famille, non-fumeur)</li>");
        out.println("<li>✅ Proposition de voyage de retour</li>");
        out.println("<li>✅ Espace utilisateur complet</li>");
        out.println("<li>✅ Gestion des annulations avec workflow admin</li>");
        out.println("<li>✅ Génération PDF des billets</li>");
        out.println("<li>✅ Espace administrateur</li>");
        out.println("<li>✅ Base de données avec Hibernate</li>");
        out.println("</ul>");
        out.println("</div>");
        out.println("<div class='text-center mt-4'>");
        out.println("<a href='" + request.getContextPath() + "/' class='btn btn-primary'>Accueil</a>");
        out.println("<a href='" + request.getContextPath() + "/recherche/search' class='btn btn-success ms-2'>Rechercher un voyage</a>");
        out.println("</div>");
        out.println("</div>");
        out.println("</body>");
        out.println("</html>");
    }
}
