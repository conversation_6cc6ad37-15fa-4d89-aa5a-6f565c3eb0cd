package com.trainticket.servlet;

import com.trainticket.dao.VoyageDAO;
import com.trainticket.dao.BilletDAO;
import com.trainticket.dao.impl.VoyageDAOImpl;
import com.trainticket.dao.impl.BilletDAOImpl;
import com.trainticket.model.Voyage;
import com.trainticket.model.Billet;
import com.trainticket.model.Utilisateur;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

/**
 * Servlet pour gérer le paiement des réservations
 */
@WebServlet(name = "PaymentServlet", urlPatterns = {"/reservation/payment", "/reservation/process-payment"})
public class PaymentServlet extends HttpServlet {

    private VoyageDAO voyageDAO;
    private BilletDAO billetDAO;

    @Override
    public void init() throws ServletException {
        super.init();
        this.voyageDAO = new VoyageDAOImpl();
        this.billetDAO = new BilletDAOImpl();
        System.out.println("PaymentServlet initialisé");
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            HttpSession session = request.getSession();

            // Vérifier que les données de réservation sont en session
            Long voyageId = (Long) session.getAttribute("reservationVoyageId");
            if (voyageId == null) {
                response.sendRedirect(request.getContextPath() + "/recherche/search");
                return;
            }

            // Récupérer le voyage
            Optional<Voyage> voyageOpt = voyageDAO.findById(voyageId);
            if (!voyageOpt.isPresent()) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "Voyage non trouvé");
                return;
            }

            Voyage voyage = voyageOpt.get();

            // Récupérer les données de réservation de la session
            String classe = (String) session.getAttribute("reservationClasse");
            String nom = (String) session.getAttribute("reservationNom");
            String prenom = (String) session.getAttribute("reservationPrenom");
            String email = (String) session.getAttribute("reservationEmail");
            String telephone = (String) session.getAttribute("reservationTelephone");
            String[] options = (String[]) session.getAttribute("reservationOptions");
            Double prixTotal = (Double) session.getAttribute("reservationPrixTotal");

            // Calculer les prix détaillés
            double prixBase = voyage.getTrajet().getPrixBase().doubleValue();
            double prixBillet = prixBase;

            switch (classe) {
                case "AFFAIRES":
                    prixBillet = prixBase * 1.5;
                    break;
                case "PREMIERE":
                    prixBillet = prixBase * 2.0;
                    break;
            }

            double prixOptions = 0;
            if (options != null) {
                for (String option : options) {
                    switch (option) {
                        case "repas": prixOptions += 15; break;
                        case "wifi": prixOptions += 5; break;
                        case "assurance": prixOptions += 10; break;
                        case "priorite": prixOptions += 8; break;
                    }
                }
            }

            // Ajouter les données à la requête
            request.setAttribute("voyage", voyage);
            request.setAttribute("reservationClasse", classe);
            request.setAttribute("reservationNom", nom);
            request.setAttribute("reservationPrenom", prenom);
            request.setAttribute("reservationEmail", email);
            request.setAttribute("reservationTelephone", telephone);
            request.setAttribute("reservationOptions", options);
            request.setAttribute("reservationPrixBillet", prixBillet);
            request.setAttribute("reservationPrixOptions", prixOptions);
            request.setAttribute("reservationPrixTotal", prixTotal != null ? prixTotal : (prixBillet + prixOptions));

            // Rediriger vers la page de paiement
            request.getRequestDispatcher("/WEB-INF/views/reservation/payment.jsp")
                   .forward(request, response);

        } catch (Exception e) {
            System.err.println("Erreur lors de l'affichage de la page de paiement: " + e.getMessage());
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
                             "Erreur lors du chargement de la page de paiement");
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            HttpSession session = request.getSession();

            // Récupérer les données de réservation
            Long voyageId = (Long) session.getAttribute("reservationVoyageId");
            String classe = (String) session.getAttribute("reservationClasse");
            String nom = (String) session.getAttribute("reservationNom");
            String prenom = (String) session.getAttribute("reservationPrenom");
            String email = (String) session.getAttribute("reservationEmail");
            String telephone = (String) session.getAttribute("reservationTelephone");
            String[] options = (String[]) session.getAttribute("reservationOptions");
            Double prixTotal = (Double) session.getAttribute("reservationPrixTotal");

            // Récupérer les données de paiement
            String paymentMethod = request.getParameter("paymentMethod");
            String cardNumber = request.getParameter("cardNumber");
            String cardName = request.getParameter("cardName");
            String billingAddress = request.getParameter("billingAddress");
            String billingCity = request.getParameter("billingCity");

            // Validation basique
            if (voyageId == null || classe == null || nom == null || prenom == null ||
                email == null || paymentMethod == null) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Données manquantes");
                return;
            }

            // Récupérer le voyage
            Optional<Voyage> voyageOpt = voyageDAO.findById(voyageId);
            if (!voyageOpt.isPresent()) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "Voyage non trouvé");
                return;
            }

            Voyage voyage = voyageOpt.get();

            // Vérifier la disponibilité
            if (voyage.getPlacesDisponibles() <= 0) {
                session.setAttribute("errorMessage", "Ce voyage est maintenant complet");
                response.sendRedirect(request.getContextPath() + "/recherche/search");
                return;
            }

            // Simuler le traitement du paiement
            boolean paiementReussi = simulatePayment(paymentMethod, cardNumber);

            if (!paiementReussi) {
                session.setAttribute("errorMessage", "Erreur lors du paiement. Veuillez réessayer.");
                response.sendRedirect(request.getContextPath() + "/reservation/payment");
                return;
            }

            // Créer l'utilisateur temporaire
            Utilisateur utilisateur = new Utilisateur();
            utilisateur.setNom(nom);
            utilisateur.setPrenom(prenom);
            utilisateur.setEmail(email);
            utilisateur.setTelephone(telephone);

            // Créer le billet
            Billet billet = new Billet();
            billet.setNumeroBillet(generateTicketNumber());
            billet.setUtilisateur(utilisateur);
            billet.setVoyage(voyage);
            billet.setClasse(Voyage.ClasseBillet.valueOf(classe));
            billet.setPrix(BigDecimal.valueOf(prixTotal != null ? prixTotal : 0));
            billet.setStatut(Billet.StatutBillet.ACHETE);
            billet.setDateAchat(LocalDateTime.now());

            // Sauvegarder le billet
            Billet billetSauvegarde = billetDAO.save(billet);

            // Mettre à jour les places disponibles du voyage
            voyage.setPlacesDisponibles(voyage.getPlacesDisponibles() - 1);
            voyageDAO.update(voyage);

            // Nettoyer la session
            session.removeAttribute("reservationVoyageId");
            session.removeAttribute("reservationClasse");
            session.removeAttribute("reservationNom");
            session.removeAttribute("reservationPrenom");
            session.removeAttribute("reservationEmail");
            session.removeAttribute("reservationTelephone");
            session.removeAttribute("reservationOptions");
            session.removeAttribute("reservationPrixTotal");

            // Stocker les informations de succès
            session.setAttribute("billetConfirme", billetSauvegarde);
            session.setAttribute("successMessage", "Votre réservation a été confirmée avec succès !");

            // Rediriger vers la page de confirmation
            response.sendRedirect(request.getContextPath() + "/reservation/success");

        } catch (Exception e) {
            System.err.println("Erreur lors du traitement du paiement: " + e.getMessage());
            e.printStackTrace();

            HttpSession session = request.getSession();
            session.setAttribute("errorMessage", "Erreur lors du traitement du paiement: " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/reservation/payment");
        }
    }

    /**
     * Simule le traitement du paiement
     * @param paymentMethod méthode de paiement
     * @param cardNumber numéro de carte (si applicable)
     * @return true si le paiement réussit, false sinon
     */
    private boolean simulatePayment(String paymentMethod, String cardNumber) {
        // Simulation simple - en réalité, on intégrerait une API de paiement
        try {
            Thread.sleep(2000); // Simuler le délai de traitement

            // Simuler un échec pour certains numéros de carte (pour les tests)
            if (cardNumber != null && cardNumber.replace(" ", "").equals("****************")) {
                return false; // Carte refusée
            }

            return true; // Paiement réussi

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * Génère un numéro de billet unique
     * @return numéro de billet
     */
    private String generateTicketNumber() {
        return "TT" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 6).toUpperCase();
    }
}
