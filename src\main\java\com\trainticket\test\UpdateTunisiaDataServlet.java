package com.trainticket.test;

import com.trainticket.util.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.Transaction;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@WebServlet("/update-tunisia-data")
public class UpdateTunisiaDataServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();

        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>Mise à jour des données tunisiennes</title>");
        out.println("<meta charset='UTF-8'>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 20px; }");
        out.println(".success { color: green; }");
        out.println(".error { color: red; }");
        out.println(".info { color: blue; }");
        out.println("table { border-collapse: collapse; width: 100%; margin: 10px 0; }");
        out.println("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
        out.println("th { background-color: #f2f2f2; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        out.println("<h1>🇹🇳 Mise à jour des données tunisiennes</h1>");

        Session session = null;
        Transaction transaction = null;

        try {
            session = HibernateUtil.getSessionFactory().openSession();
            transaction = session.beginTransaction();

            // Supprimer les données existantes
            out.println("<h2>🗑️ Suppression des données existantes...</h2>");

            int billetsDeleted = session.createQuery("DELETE FROM Billet").executeUpdate();
            out.println("<p class='info'>Billets supprimés: " + billetsDeleted + "</p>");

            int voyagesDeleted = session.createQuery("DELETE FROM Voyage").executeUpdate();
            out.println("<p class='info'>Voyages supprimés: " + voyagesDeleted + "</p>");

            int trajetsDeleted = session.createQuery("DELETE FROM Trajet").executeUpdate();
            out.println("<p class='info'>Trajets supprimés: " + trajetsDeleted + "</p>");

            int garesDeleted = session.createQuery("DELETE FROM Gare").executeUpdate();
            out.println("<p class='info'>Gares supprimées: " + garesDeleted + "</p>");

            // Insérer les nouvelles données tunisiennes
            out.println("<h2>🚉 Insertion des gares tunisiennes...</h2>");

            // Utilisation de SQL natif pour l'insertion
            String insertGares = "INSERT INTO gares (nom, ville, code_gare, adresse, latitude, longitude, active) VALUES " +
                "('Gare Centrale de Tunis', 'Tunis', 'TUN', 'Place Barcelone, Tunis', 36.8008, 10.1817, TRUE), " +
                "('Gare de Sfax', 'Sfax', 'SFX', 'Avenue Habib Bourguiba, Sfax', 34.7406, 10.7603, TRUE), " +
                "('Gare de Sousse', 'Sousse', 'SOU', 'Boulevard Hassouna Ayachi, Sousse', 35.8256, 10.6369, TRUE), " +
                "('Gare de Bizerte', 'Bizerte', 'BIZ', 'Avenue Habib Bourguiba, Bizerte', 37.2744, 9.8739, TRUE), " +
                "('Gare de Gabès', 'Gabès', 'GAB', 'Avenue Farhat Hached, Gabès', 33.8815, 10.0982, TRUE), " +
                "('Gare de Monastir', 'Monastir', 'MON', 'Avenue de la République, Monastir', 35.7643, 10.8113, TRUE), " +
                "('Gare de Kairouan', 'Kairouan', 'KAI', 'Avenue de la République, Kairouan', 35.6781, 10.0963, TRUE), " +
                "('Gare de Mahdia', 'Mahdia', 'MAH', 'Avenue 7 Novembre, Mahdia', 35.5047, 11.0622, TRUE), " +
                "('Gare de Nabeul', 'Nabeul', 'NAB', 'Avenue Habib Bourguiba, Nabeul', 36.4561, 10.7376, TRUE), " +
                "('Gare de Gafsa', 'Gafsa', 'GAF', 'Avenue Habib Bourguiba, Gafsa', 34.4250, 8.7842, TRUE), " +
                "('Gare de Béja', 'Béja', 'BEJ', 'Avenue de la Liberté, Béja', 36.7256, 9.1817, TRUE), " +
                "('Gare de Jendouba', 'Jendouba', 'JEN', 'Avenue Habib Bourguiba, Jendouba', 36.5014, 8.7800, TRUE)";

            int garesInserted = session.createNativeQuery(insertGares).executeUpdate();
            out.println("<p class='success'>Gares tunisiennes ajoutées: " + garesInserted + "</p>");

            out.println("<h2>🚄 Insertion des trajets tunisiens...</h2>");

            String insertTrajets = "INSERT INTO trajets (numero_trajet, gare_depart_id, gare_arrivee_id, duree_minutes, prix_base, capacite_total, type, description) VALUES " +
                "('TN001', 1, 2, 180, 25.50, 200, 'EXPRESS', 'Express Tunis - Sfax'), " +
                "('TN002', 2, 1, 180, 25.50, 200, 'EXPRESS', 'Express Sfax - Tunis'), " +
                "('TN003', 1, 3, 120, 18.00, 180, 'DIRECT', 'Train direct Tunis - Sousse'), " +
                "('TN004', 3, 1, 120, 18.00, 180, 'DIRECT', 'Train direct Sousse - Tunis'), " +
                "('TN005', 1, 5, 240, 32.00, 160, 'NORMAL', 'Train Tunis - Gabès'), " +
                "('TN006', 5, 1, 240, 32.00, 160, 'NORMAL', 'Train Gabès - Tunis'), " +
                "('TN101', 3, 2, 90, 15.00, 150, 'NORMAL', 'Train Sousse - Sfax'), " +
                "('TN102', 2, 3, 90, 15.00, 150, 'NORMAL', 'Train Sfax - Sousse'), " +
                "('TN103', 3, 6, 45, 8.50, 140, 'NORMAL', 'Train Sousse - Monastir'), " +
                "('TN104', 6, 3, 45, 8.50, 140, 'NORMAL', 'Train Monastir - Sousse'), " +
                "('TN201', 1, 4, 90, 14.00, 160, 'NORMAL', 'Train Tunis - Bizerte'), " +
                "('TN202', 4, 1, 90, 14.00, 160, 'NORMAL', 'Train Bizerte - Tunis'), " +
                "('TN203', 1, 9, 75, 12.50, 140, 'DIRECT', 'Train direct Tunis - Nabeul'), " +
                "('TN204', 9, 1, 75, 12.50, 140, 'DIRECT', 'Train direct Nabeul - Tunis'), " +
                "('TN301', 1, 7, 150, 22.00, 140, 'NORMAL', 'Train Tunis - Kairouan'), " +
                "('TN302', 7, 1, 150, 22.00, 140, 'NORMAL', 'Train Kairouan - Tunis')";

            int trajetsInserted = session.createNativeQuery(insertTrajets).executeUpdate();
            out.println("<p class='success'>Trajets tunisiens ajoutés: " + trajetsInserted + "</p>");

            transaction.commit();

            out.println("<h2>✅ Mise à jour terminée avec succès!</h2>");
            out.println("<p class='success'>La base de données contient maintenant des données tunisiennes réalistes.</p>");

            // Afficher un résumé
            out.println("<h3>📊 Résumé des données:</h3>");
            out.println("<ul>");
            out.println("<li><strong>12 gares</strong> dans les principales villes tunisiennes</li>");
            out.println("<li><strong>16 trajets</strong> couvrant tout le territoire</li>");
            out.println("<li>Prix en <strong>Dinars Tunisiens (TND)</strong></li>");
            out.println("<li>Coordonnées GPS réelles des gares</li>");
            out.println("</ul>");

            out.println("<h3>🗺️ Principales liaisons:</h3>");
            out.println("<ul>");
            out.println("<li><strong>Tunis ↔ Sfax</strong> (Express, 3h, 25.50 TND)</li>");
            out.println("<li><strong>Tunis ↔ Sousse</strong> (Direct, 2h, 18.00 TND)</li>");
            out.println("<li><strong>Tunis ↔ Bizerte</strong> (1h30, 14.00 TND)</li>");
            out.println("<li><strong>Tunis ↔ Nabeul</strong> (Direct, 1h15, 12.50 TND)</li>");
            out.println("<li><strong>Sousse ↔ Monastir</strong> (45min, 8.50 TND)</li>");
            out.println("</ul>");

            out.println("<p><a href='/train-ticket/'>← Retour à l'application</a></p>");

        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            out.println("<h2 class='error'>❌ Erreur lors de la mise à jour</h2>");
            out.println("<p class='error'>Erreur: " + e.getMessage() + "</p>");
            e.printStackTrace();
        } finally {
            if (session != null) {
                session.close();
            }
        }

        out.println("</body>");
        out.println("</html>");
    }
}
