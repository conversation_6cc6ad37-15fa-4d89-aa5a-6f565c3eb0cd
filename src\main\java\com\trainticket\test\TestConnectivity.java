package com.trainticket.test;

import com.trainticket.util.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.SessionFactory;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@WebServlet("/test/connectivity")
public class TestConnectivity extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        response.setContentType("text/html; charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html><head><title>Test de Connectivité</title></head><body>");
        out.println("<h1>Test de Connectivité à la Base de Données</h1>");
        
        try {
            // Test de la SessionFactory
            out.println("<h2>1. Test SessionFactory</h2>");
            SessionFactory sessionFactory = HibernateUtil.getSessionFactory();
            if (sessionFactory != null) {
                out.println("<p style='color: green;'>✅ SessionFactory créée avec succès</p>");
                
                // Test de session
                out.println("<h2>2. Test Session</h2>");
                try (Session session = sessionFactory.openSession()) {
                    out.println("<p style='color: green;'>✅ Session ouverte avec succès</p>");
                    
                    // Test de requête simple
                    out.println("<h2>3. Test Requête Simple</h2>");
                    String sql = "SELECT 1 as test";
                    Object result = session.createNativeQuery(sql).getSingleResult();
                    out.println("<p style='color: green;'>✅ Requête exécutée avec succès: " + result + "</p>");
                    
                    // Test des tables
                    out.println("<h2>4. Test Tables</h2>");
                    try {
                        String countGares = "SELECT COUNT(*) FROM gares";
                        Object garesCount = session.createNativeQuery(countGares).getSingleResult();
                        out.println("<p style='color: green;'>✅ Table gares: " + garesCount + " enregistrements</p>");
                        
                        String countVoyages = "SELECT COUNT(*) FROM voyages";
                        Object voyagesCount = session.createNativeQuery(countVoyages).getSingleResult();
                        out.println("<p style='color: green;'>✅ Table voyages: " + voyagesCount + " enregistrements</p>");
                        
                    } catch (Exception e) {
                        out.println("<p style='color: orange;'>⚠️ Erreur lors du test des tables: " + e.getMessage() + "</p>");
                    }
                    
                } catch (Exception e) {
                    out.println("<p style='color: red;'>❌ Erreur lors de l'ouverture de session: " + e.getMessage() + "</p>");
                    e.printStackTrace(out);
                }
                
            } else {
                out.println("<p style='color: red;'>❌ SessionFactory est null</p>");
            }
            
        } catch (Exception e) {
            out.println("<p style='color: red;'>❌ Erreur lors de la création de SessionFactory: " + e.getMessage() + "</p>");
            e.printStackTrace(out);
        }
        
        out.println("</body></html>");
    }
}
