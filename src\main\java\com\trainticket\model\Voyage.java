package com.trainticket.model;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name = "voyages")
public class Voyage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "trajet_id", nullable = false)
    private Trajet trajet;

    @Column(name = "date_heure_depart", nullable = false)
    private LocalDateTime dateHeureDepart;

    @Column(name = "date_heure_arrivee", nullable = false)
    private LocalDateTime dateHeureArrivee;

    @Column(name = "places_disponibles", nullable = false)
    private Integer placesDisponibles;

    @Column(name = "places_premiere_classe")
    private Integer placesPremiereClasse = 20;

    @Column(name = "places_deuxieme_classe")
    private Integer placesDeuxiemeClasse = 60;

    @Column(name = "places_economique")
    private Integer placesEconomique = 120;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private StatutVoyage statut = StatutVoyage.PROGRAMME;

    @Column(length = 500)
    private String commentaires;

    @OneToMany(mappedBy = "voyage", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Billet> billets;

    // Constructeurs
    public Voyage() {}

    public Voyage(Trajet trajet, LocalDateTime dateHeureDepart) {
        this.trajet = trajet;
        this.dateHeureDepart = dateHeureDepart;
        this.dateHeureArrivee = dateHeureDepart.plusMinutes(trajet.getDureeMinutes());
        this.placesDisponibles = trajet.getCapaciteTotal();
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Trajet getTrajet() {
        return trajet;
    }

    public void setTrajet(Trajet trajet) {
        this.trajet = trajet;
    }

    public LocalDateTime getDateHeureDepart() {
        return dateHeureDepart;
    }

    public void setDateHeureDepart(LocalDateTime dateHeureDepart) {
        this.dateHeureDepart = dateHeureDepart;
    }

    public LocalDateTime getDateHeureArrivee() {
        return dateHeureArrivee;
    }

    public void setDateHeureArrivee(LocalDateTime dateHeureArrivee) {
        this.dateHeureArrivee = dateHeureArrivee;
    }

    public Integer getPlacesDisponibles() {
        return placesDisponibles;
    }

    public void setPlacesDisponibles(Integer placesDisponibles) {
        this.placesDisponibles = placesDisponibles;
    }

    public Integer getPlacesPremiereClasse() {
        return placesPremiereClasse;
    }

    public void setPlacesPremiereClasse(Integer placesPremiereClasse) {
        this.placesPremiereClasse = placesPremiereClasse;
    }

    public Integer getPlacesDeuxiemeClasse() {
        return placesDeuxiemeClasse;
    }

    public void setPlacesDeuxiemeClasse(Integer placesDeuxiemeClasse) {
        this.placesDeuxiemeClasse = placesDeuxiemeClasse;
    }

    public Integer getPlacesEconomique() {
        return placesEconomique;
    }

    public void setPlacesEconomique(Integer placesEconomique) {
        this.placesEconomique = placesEconomique;
    }

    public StatutVoyage getStatut() {
        return statut;
    }

    public void setStatut(StatutVoyage statut) {
        this.statut = statut;
    }

    public String getCommentaires() {
        return commentaires;
    }

    public void setCommentaires(String commentaires) {
        this.commentaires = commentaires;
    }

    public List<Billet> getBillets() {
        return billets;
    }

    public void setBillets(List<Billet> billets) {
        this.billets = billets;
    }

    // Méthodes utilitaires
    public boolean hasPlacesDisponibles() {
        return placesDisponibles > 0;
    }

    public boolean hasPlacesDisponibles(ClasseBillet classe) {
        switch (classe) {
            case PREMIERE:
                return placesPremiereClasse > 0;
            case DEUXIEME:
                return placesDeuxiemeClasse > 0;
            case ECONOMIQUE:
                return placesEconomique > 0;
            default:
                return false;
        }
    }

    public void reserverPlace(ClasseBillet classe) {
        if (hasPlacesDisponibles(classe)) {
            switch (classe) {
                case PREMIERE:
                    placesPremiereClasse--;
                    break;
                case DEUXIEME:
                    placesDeuxiemeClasse--;
                    break;
                case ECONOMIQUE:
                    placesEconomique--;
                    break;
            }
            placesDisponibles--;
        }
    }

    public void libererPlace(ClasseBillet classe) {
        switch (classe) {
            case PREMIERE:
                placesPremiereClasse++;
                break;
            case DEUXIEME:
                placesDeuxiemeClasse++;
                break;
            case ECONOMIQUE:
                placesEconomique++;
                break;
        }
        placesDisponibles++;
    }

    public boolean isEnCours() {
        LocalDateTime maintenant = LocalDateTime.now();
        return maintenant.isAfter(dateHeureDepart) && maintenant.isBefore(dateHeureArrivee);
    }

    public boolean isTermine() {
        return LocalDateTime.now().isAfter(dateHeureArrivee);
    }

    // Enums
    public enum StatutVoyage {
        PROGRAMME, EN_COURS, TERMINE, ANNULE, RETARDE
    }

    public enum ClasseBillet {
        PREMIERE, DEUXIEME, ECONOMIQUE
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Voyage voyage = (Voyage) o;
        return Objects.equals(id, voyage.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "Voyage{" +
                "id=" + id +
                ", trajet=" + (trajet != null ? trajet.getNumeroTrajet() : "null") +
                ", dateHeureDepart=" + dateHeureDepart +
                ", placesDisponibles=" + placesDisponibles +
                ", statut=" + statut +
                '}';
    }
}
