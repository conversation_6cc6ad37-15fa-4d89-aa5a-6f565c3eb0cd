<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mes Billets - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="${pageContext.request.contextPath}/resources/css/style.css" rel="stylesheet">
</head>
<body>
    <%@ include file="../common/navbar.jsp" %>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-ticket-alt text-primary"></i> Mes Billets</h2>
                    <a href="${pageContext.request.contextPath}/user/profile" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> Retour au profil
                    </a>
                </div>

                <!-- Filtres -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <select class="form-select" id="statutFilter">
                                    <option value="">Tous les statuts</option>
                                    <option value="ACHETE">Achetés</option>
                                    <option value="UTILISE">Utilisés</option>
                                    <option value="ANNULE">Annulés</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="classeFilter">
                                    <option value="">Toutes les classes</option>
                                    <option value="PREMIERE">1ère Classe</option>
                                    <option value="DEUXIEME">2ème Classe</option>
                                    <option value="ECONOMIQUE">Économique</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control" id="searchInput" 
                                       placeholder="Rechercher par numéro de billet ou destination...">
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-primary w-100" onclick="applyFilters()">
                                    <i class="fas fa-filter"></i> Filtrer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Onglets -->
                <ul class="nav nav-tabs" id="ticketTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="active-tab" data-bs-toggle="tab" 
                                data-bs-target="#active" type="button" role="tab">
                            <i class="fas fa-ticket-alt"></i> Billets Actifs
                            <span class="badge bg-primary ms-2">${billetsActifs.size()}</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="history-tab" data-bs-toggle="tab" 
                                data-bs-target="#history" type="button" role="tab">
                            <i class="fas fa-history"></i> Historique
                            <span class="badge bg-secondary ms-2">${billetsHistorique.size()}</span>
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="ticketTabsContent">
                    <!-- Billets Actifs -->
                    <div class="tab-pane fade show active" id="active" role="tabpanel">
                        <div class="card">
                            <div class="card-body">
                                <c:choose>
                                    <c:when test="${empty billetsActifs}">
                                        <div class="text-center py-5">
                                            <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">Aucun billet actif</h5>
                                            <p class="text-muted">Vous n'avez pas de billets actifs pour le moment.</p>
                                            <a href="${pageContext.request.contextPath}/recherche/search" 
                                               class="btn btn-primary">
                                                <i class="fas fa-search"></i> Rechercher un voyage
                                            </a>
                                        </div>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="row">
                                            <c:forEach var="billet" items="${billetsActifs}">
                                                <div class="col-md-6 mb-4">
                                                    <div class="card border-primary">
                                                        <div class="card-header bg-primary text-white">
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <h6 class="mb-0">${billet.numeroBillet}</h6>
                                                                <span class="badge bg-light text-dark">${billet.classeLibelle}</span>
                                                            </div>
                                                        </div>
                                                        <div class="card-body">
                                                            <div class="row">
                                                                <div class="col-12 mb-3">
                                                                    <strong>${billet.voyage.trajet.gareDepart.ville}</strong>
                                                                    <i class="fas fa-arrow-right mx-2 text-primary"></i>
                                                                    <strong>${billet.voyage.trajet.gareArrivee.ville}</strong>
                                                                </div>
                                                                <div class="col-6">
                                                                    <small class="text-muted">Départ</small><br>
                                                                    <strong>
                                                                        <fmt:formatDate value="${billet.voyage.dateHeureDepart}" 
                                                                                      pattern="dd/MM/yyyy"/>
                                                                    </strong><br>
                                                                    <small>
                                                                        <fmt:formatDate value="${billet.voyage.dateHeureDepart}" 
                                                                                      pattern="HH:mm"/>
                                                                    </small>
                                                                </div>
                                                                <div class="col-6">
                                                                    <small class="text-muted">Prix</small><br>
                                                                    <strong class="text-success">
                                                                        <fmt:formatNumber value="${billet.prix}" 
                                                                                        type="currency" currencySymbol="DH"/>
                                                                    </strong>
                                                                </div>
                                                            </div>
                                                            <c:if test="${not empty billet.numeroSiege}">
                                                                <div class="mt-2">
                                                                    <small class="text-muted">Siège: </small>
                                                                    <span class="badge bg-info">${billet.numeroSiege}</span>
                                                                </div>
                                                            </c:if>
                                                        </div>
                                                        <div class="card-footer">
                                                            <div class="d-flex justify-content-between">
                                                                <div class="btn-group" role="group">
                                                                    <a href="${pageContext.request.contextPath}/user/download-ticket/${billet.id}" 
                                                                       class="btn btn-sm btn-outline-primary" target="_blank">
                                                                        <i class="fas fa-download"></i> PDF
                                                                    </a>
                                                                    <button class="btn btn-sm btn-outline-info" 
                                                                            onclick="showTicketDetails(${billet.id})">
                                                                        <i class="fas fa-eye"></i> Détails
                                                                    </button>
                                                                </div>
                                                                <c:if test="${billet.statut == 'ACHETE'}">
                                                                    <a href="${pageContext.request.contextPath}/user/annulation/form?billetId=${billet.id}" 
                                                                       class="btn btn-sm btn-outline-warning">
                                                                        <i class="fas fa-times"></i> Annuler
                                                                    </a>
                                                                </c:if>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </c:forEach>
                                        </div>
                                    </c:otherwise>
                                </c:choose>
                            </div>
                        </div>
                    </div>

                    <!-- Historique -->
                    <div class="tab-pane fade" id="history" role="tabpanel">
                        <div class="card">
                            <div class="card-body">
                                <c:choose>
                                    <c:when test="${empty billetsHistorique}">
                                        <div class="text-center py-5">
                                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">Aucun historique</h5>
                                            <p class="text-muted">Votre historique de voyages apparaîtra ici.</p>
                                        </div>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>Billet</th>
                                                        <th>Voyage</th>
                                                        <th>Date</th>
                                                        <th>Statut</th>
                                                        <th>Prix</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <c:forEach var="billet" items="${billetsHistorique}">
                                                        <tr>
                                                            <td>
                                                                <strong>${billet.numeroBillet}</strong><br>
                                                                <small class="text-muted">${billet.classeLibelle}</small>
                                                            </td>
                                                            <td>
                                                                <strong>${billet.voyage.trajet.gareDepart.ville}</strong>
                                                                <i class="fas fa-arrow-right mx-1"></i>
                                                                <strong>${billet.voyage.trajet.gareArrivee.ville}</strong>
                                                            </td>
                                                            <td>
                                                                <fmt:formatDate value="${billet.voyage.dateHeureDepart}" 
                                                                              pattern="dd/MM/yyyy HH:mm"/>
                                                            </td>
                                                            <td>
                                                                <c:choose>
                                                                    <c:when test="${billet.statut == 'UTILISE'}">
                                                                        <span class="badge bg-success">Utilisé</span>
                                                                    </c:when>
                                                                    <c:when test="${billet.statut == 'ANNULE'}">
                                                                        <span class="badge bg-danger">Annulé</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="badge bg-secondary">${billet.statut}</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </td>
                                                            <td>
                                                                <fmt:formatNumber value="${billet.prix}" 
                                                                                type="currency" currencySymbol="DH"/>
                                                            </td>
                                                            <td>
                                                                <c:if test="${billet.statut == 'UTILISE'}">
                                                                    <a href="${pageContext.request.contextPath}/user/download-ticket/${billet.id}" 
                                                                       class="btn btn-sm btn-outline-primary" target="_blank">
                                                                        <i class="fas fa-download"></i> PDF
                                                                    </a>
                                                                </c:if>
                                                            </td>
                                                        </tr>
                                                    </c:forEach>
                                                </tbody>
                                            </table>
                                        </div>
                                    </c:otherwise>
                                </c:choose>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include file="../common/footer.jsp" %>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function applyFilters() {
            // Implémentation des filtres
            const statutFilter = document.getElementById('statutFilter').value;
            const classeFilter = document.getElementById('classeFilter').value;
            const searchInput = document.getElementById('searchInput').value.toLowerCase();
            
            // Logique de filtrage côté client
            console.log('Filtres appliqués:', { statutFilter, classeFilter, searchInput });
        }

        function showTicketDetails(billetId) {
            // Afficher les détails du billet
            alert('Détails du billet ' + billetId);
        }
    </script>
</body>
</html>
