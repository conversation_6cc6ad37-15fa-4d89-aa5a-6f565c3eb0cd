package com.trainticket.servlet;

import com.trainticket.dao.VoyageDAO;
import com.trainticket.dao.UtilisateurDAO;
import com.trainticket.dao.impl.VoyageDAOImpl;
import com.trainticket.dao.impl.UtilisateurDAOImpl;
import com.trainticket.model.Voyage;
import com.trainticket.model.Utilisateur;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Optional;

/**
 * Servlet pour la sélection et confirmation de voyage
 * Affiche le formulaire de réservation avec les détails du voyage
 */
@WebServlet(name = "SelectVoyageServlet", urlPatterns = {"/reservation/select-voyage", "/select-voyage"})
public class SelectVoyageServlet extends HttpServlet {
    
    private VoyageDAO voyageDAO;
    private UtilisateurDAO utilisateurDAO;
    
    @Override
    public void init() throws ServletException {
        super.init();
        this.voyageDAO = new VoyageDAOImpl();
        this.utilisateurDAO = new UtilisateurDAOImpl();
        System.out.println("SelectVoyageServlet initialisé");
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        try {
            // Récupérer l'ID du voyage
            String voyageIdStr = request.getParameter("voyageId");
            if (voyageIdStr == null || voyageIdStr.trim().isEmpty()) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID du voyage manquant");
                return;
            }
            
            Long voyageId = Long.parseLong(voyageIdStr);
            
            // Récupérer le voyage
            Optional<Voyage> voyageOpt = voyageDAO.findById(voyageId);
            if (!voyageOpt.isPresent()) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "Voyage non trouvé");
                return;
            }
            
            Voyage voyage = voyageOpt.get();
            
            // Vérifier la disponibilité du voyage
            if (voyage.getPlacesDisponibles() <= 0) {
                request.setAttribute("errorMessage", "Ce voyage est complet");
                request.getRequestDispatcher("/WEB-INF/views/error/voyage-unavailable.jsp")
                       .forward(request, response);
                return;
            }
            
            // Vérifier si le voyage est encore programmé
            if (voyage.getStatut() != Voyage.StatutVoyage.PROGRAMME) {
                request.setAttribute("errorMessage", "Ce voyage n'est plus disponible");
                request.getRequestDispatcher("/WEB-INF/views/error/voyage-unavailable.jsp")
                       .forward(request, response);
                return;
            }
            
            // Récupérer l'utilisateur connecté (simulation)
            HttpSession session = request.getSession();
            Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");
            
            // Si pas d'utilisateur connecté, créer un utilisateur temporaire
            if (utilisateur == null) {
                utilisateur = createTemporaryUser();
            }
            
            // Ajouter les données à la requête
            request.setAttribute("voyage", voyage);
            request.setAttribute("utilisateur", utilisateur);
            
            // Calculer les prix pour chaque classe
            double prixBase = voyage.getTrajet().getPrixBase().doubleValue();
            request.setAttribute("prixEconomique", prixBase);
            request.setAttribute("prixAffaires", prixBase * 1.5);
            request.setAttribute("prixPremiere", prixBase * 2.0);
            
            // Rediriger vers la page de sélection
            request.getRequestDispatcher("/WEB-INF/views/reservation/select-voyage.jsp")
                   .forward(request, response);
            
        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID du voyage invalide");
        } catch (Exception e) {
            System.err.println("Erreur lors de la sélection du voyage: " + e.getMessage());
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, 
                             "Erreur lors du chargement du voyage");
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        try {
            // Récupérer les données du formulaire
            String voyageIdStr = request.getParameter("voyageId");
            String classe = request.getParameter("classe");
            String nom = request.getParameter("nom");
            String prenom = request.getParameter("prenom");
            String email = request.getParameter("email");
            String telephone = request.getParameter("telephone");
            String[] options = request.getParameterValues("options");
            
            // Validation des données
            if (voyageIdStr == null || classe == null || nom == null || prenom == null || 
                email == null || telephone == null) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Données manquantes");
                return;
            }
            
            Long voyageId = Long.parseLong(voyageIdStr);
            
            // Récupérer le voyage
            Optional<Voyage> voyageOpt = voyageDAO.findById(voyageId);
            if (!voyageOpt.isPresent()) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "Voyage non trouvé");
                return;
            }
            
            Voyage voyage = voyageOpt.get();
            
            // Calculer le prix total
            double prixBase = voyage.getTrajet().getPrixBase().doubleValue();
            double prixClasse = prixBase;
            
            switch (classe) {
                case "AFFAIRES":
                    prixClasse = prixBase * 1.5;
                    break;
                case "PREMIERE":
                    prixClasse = prixBase * 2.0;
                    break;
                default:
                    prixClasse = prixBase;
            }
            
            // Calculer le prix des options
            double prixOptions = 0;
            if (options != null) {
                for (String option : options) {
                    switch (option) {
                        case "repas":
                            prixOptions += 15;
                            break;
                        case "wifi":
                            prixOptions += 5;
                            break;
                        case "assurance":
                            prixOptions += 10;
                            break;
                        case "priorite":
                            prixOptions += 8;
                            break;
                    }
                }
            }
            
            double prixTotal = prixClasse + prixOptions;
            
            // Stocker les données de réservation en session
            HttpSession session = request.getSession();
            session.setAttribute("reservationVoyageId", voyageId);
            session.setAttribute("reservationClasse", classe);
            session.setAttribute("reservationNom", nom);
            session.setAttribute("reservationPrenom", prenom);
            session.setAttribute("reservationEmail", email);
            session.setAttribute("reservationTelephone", telephone);
            session.setAttribute("reservationOptions", options);
            session.setAttribute("reservationPrixTotal", prixTotal);
            
            // Rediriger vers la page de paiement
            response.sendRedirect(request.getContextPath() + "/reservation/payment");
            
        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Données invalides");
        } catch (Exception e) {
            System.err.println("Erreur lors de la confirmation de réservation: " + e.getMessage());
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, 
                             "Erreur lors de la confirmation");
        }
    }
    
    /**
     * Crée un utilisateur temporaire pour les utilisateurs non connectés
     * @return utilisateur temporaire
     */
    private Utilisateur createTemporaryUser() {
        Utilisateur utilisateur = new Utilisateur();
        utilisateur.setNom("");
        utilisateur.setPrenom("");
        utilisateur.setEmail("");
        utilisateur.setTelephone("");
        return utilisateur;
    }
}
