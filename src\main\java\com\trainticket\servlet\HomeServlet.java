package com.trainticket.servlet;

import com.trainticket.controller.HomeController;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Servlet pour la page d'accueil
 * Charge les données dynamiques et redirige vers index.jsp
 */
@WebServlet(name = "HomeServlet", urlPatterns = {"/home", "/index"})
public class HomeServlet extends HttpServlet {

    private HomeController homeController;

    @Override
    public void init() throws ServletException {
        super.init();
        this.homeController = new HomeController();
        System.out.println("HomeServlet initialisé");
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            // Charger les données dynamiques
            homeController.loadHomeData(request);

            // Rediriger vers la page d'accueil
            request.getRequestDispatcher("/index.jsp").forward(request, response);

        } catch (Exception e) {
            System.err.println("Erreur lors du traitement de la page d'accueil: " + e.getMessage());
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
                             "Erreur lors du chargement de la page d'accueil");
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        doGet(request, response);
    }
}
