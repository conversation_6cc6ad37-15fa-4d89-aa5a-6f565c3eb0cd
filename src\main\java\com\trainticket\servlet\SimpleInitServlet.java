package com.trainticket.servlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.*;

/**
 * Servlet simple pour initialiser la base de données sans Hibernate
 */
public class SimpleInitServlet extends HttpServlet {

    private static final String DB_URL = "***************************/";
    private static final String DB_NAME = "train_ticket_db";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = ""; // Essayons d'abord sans mot de passe

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("text/html; charset=UTF-8");
        PrintWriter out = response.getWriter();

        out.println("<!DOCTYPE html>");
        out.println("<html><head><title>Initialisation Simple</title>");
        out.println("<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>");
        out.println("</head><body>");
        out.println("<div class='container mt-4'>");
        out.println("<h1>Initialisation Simple de la Base de Données</h1>");

        try {
            // Test de connexion MySQL
            out.println("<h3>Test de connexion MySQL :</h3>");
            Class.forName("com.mysql.cj.jdbc.Driver");

            // Essayer plusieurs configurations de connexion
            Connection conn = null;
            String[] passwords = {"", "root", "mysql", "password"};
            String usedPassword = "";

            for (String password : passwords) {
                try {
                    conn = DriverManager.getConnection(DB_URL, DB_USER, password);
                    usedPassword = password.isEmpty() ? "(vide)" : password;
                    out.println("<div class='alert alert-success'>✅ Connexion MySQL réussie avec mot de passe: " + usedPassword + "</div>");
                    break;
                } catch (SQLException e) {
                    out.println("<div class='alert alert-warning'>⚠️ Échec avec mot de passe '" +
                               (password.isEmpty() ? "(vide)" : password) + "': " + e.getMessage() + "</div>");
                }
            }

            if (conn == null) {
                throw new SQLException("Impossible de se connecter avec aucun mot de passe testé");
            }

            // Créer la base de données si elle n'existe pas
            Statement stmt = conn.createStatement();
            stmt.executeUpdate("CREATE DATABASE IF NOT EXISTS " + DB_NAME + " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            out.println("<div class='alert alert-info'>✅ Base de données '" + DB_NAME + "' créée/vérifiée</div>");

            conn.close();

            // Connexion à la base de données spécifique
            conn = DriverManager.getConnection(DB_URL + DB_NAME, DB_USER, DB_PASSWORD);
            out.println("<div class='alert alert-success'>✅ Connexion à la base '" + DB_NAME + "' réussie</div>");

            // Créer les tables de base
            createTables(conn, out);

            // Insérer des données de test
            insertTestData(conn, out);

            conn.close();

            out.println("<div class='alert alert-success'>");
            out.println("<h4>🎉 Initialisation terminée avec succès !</h4>");
            out.println("<p>Vous pouvez maintenant tester l'application :</p>");
            out.println("<a href='" + request.getContextPath() + "/recherche/search' class='btn btn-primary'>Tester la recherche</a>");
            out.println("</div>");

        } catch (Exception e) {
            out.println("<div class='alert alert-danger'>");
            out.println("<h4>❌ Erreur lors de l'initialisation :</h4>");
            out.println("<p>" + e.getMessage() + "</p>");
            out.println("<pre>" + getStackTrace(e) + "</pre>");
            out.println("</div>");
        }

        out.println("<a href='" + request.getContextPath() + "/' class='btn btn-secondary'>Retour à l'accueil</a>");
        out.println("</div></body></html>");
    }

    private void createTables(Connection conn, PrintWriter out) throws SQLException {
        Statement stmt = conn.createStatement();

        // Table gares
        String createGares = "CREATE TABLE IF NOT EXISTS gares (" +
            "id BIGINT AUTO_INCREMENT PRIMARY KEY," +
            "nom VARCHAR(100) NOT NULL," +
            "ville VARCHAR(50) NOT NULL," +
            "adresse VARCHAR(200)," +
            "INDEX idx_ville (ville)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        stmt.executeUpdate(createGares);
        out.println("<div class='alert alert-info'>✅ Table 'gares' créée</div>");

        // Table trajets
        String createTrajets = "CREATE TABLE IF NOT EXISTS trajets (" +
            "id BIGINT AUTO_INCREMENT PRIMARY KEY," +
            "numero_trajet VARCHAR(50) NOT NULL UNIQUE," +
            "gare_depart_id BIGINT NOT NULL," +
            "gare_arrivee_id BIGINT NOT NULL," +
            "duree_minutes INT NOT NULL," +
            "prix_base DECIMAL(10,2) NOT NULL," +
            "capacite_total INT NOT NULL DEFAULT 200," +
            "active BOOLEAN NOT NULL DEFAULT TRUE," +
            "type ENUM('NORMAL', 'DIRECT', 'EXPRESS') NOT NULL DEFAULT 'NORMAL'," +
            "description VARCHAR(500)," +
            "FOREIGN KEY (gare_depart_id) REFERENCES gares(id)," +
            "FOREIGN KEY (gare_arrivee_id) REFERENCES gares(id)," +
            "INDEX idx_numero_trajet (numero_trajet)," +
            "INDEX idx_gare_depart (gare_depart_id)," +
            "INDEX idx_gare_arrivee (gare_arrivee_id)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        stmt.executeUpdate(createTrajets);
        out.println("<div class='alert alert-info'>✅ Table 'trajets' créée</div>");

        // Table voyages
        String createVoyages = "CREATE TABLE IF NOT EXISTS voyages (" +
            "id BIGINT AUTO_INCREMENT PRIMARY KEY," +
            "trajet_id BIGINT NOT NULL," +
            "date_heure_depart DATETIME NOT NULL," +
            "date_heure_arrivee DATETIME NOT NULL," +
            "places_disponibles INT NOT NULL," +
            "places_premiere_classe INT DEFAULT 20," +
            "places_deuxieme_classe INT DEFAULT 60," +
            "places_economique INT DEFAULT 120," +
            "statut ENUM('PROGRAMME', 'EN_COURS', 'TERMINE', 'ANNULE') NOT NULL DEFAULT 'PROGRAMME'," +
            "commentaires VARCHAR(500)," +
            "FOREIGN KEY (trajet_id) REFERENCES trajets(id)," +
            "INDEX idx_date_depart (date_heure_depart)," +
            "INDEX idx_statut (statut)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        stmt.executeUpdate(createVoyages);
        out.println("<div class='alert alert-info'>✅ Table 'voyages' créée</div>");

        stmt.close();
    }

    private void insertTestData(Connection conn, PrintWriter out) throws SQLException {
        // Vérifier si des données existent déjà
        Statement checkStmt = conn.createStatement();
        ResultSet rs = checkStmt.executeQuery("SELECT COUNT(*) FROM gares");
        rs.next();
        int garesCount = rs.getInt(1);
        rs.close();
        checkStmt.close();

        if (garesCount > 0) {
            out.println("<div class='alert alert-warning'>⚠️ Des données existent déjà, insertion ignorée</div>");
            return;
        }

        // Insérer les gares
        String insertGares = "INSERT INTO gares (nom, ville, adresse) VALUES (?, ?, ?)";
        PreparedStatement pstmt = conn.prepareStatement(insertGares);

        String[][] gares = {
            {"Gare Centrale de Tunis", "Tunis", "Avenue Habib Bourguiba"},
            {"Gare de Sfax", "Sfax", "Avenue de la République"},
            {"Gare de Sousse", "Sousse", "Boulevard Yahya Ibn Omar"},
            {"Gare de Kairouan", "Kairouan", "Avenue de la Liberté"},
            {"Gare de Bizerte", "Bizerte", "Rue de la Gare"},
            {"Gare de Gabès", "Gabès", "Avenue Farhat Hached"}
        };

        for (String[] gare : gares) {
            pstmt.setString(1, gare[0]);
            pstmt.setString(2, gare[1]);
            pstmt.setString(3, gare[2]);
            pstmt.executeUpdate();
        }
        pstmt.close();
        out.println("<div class='alert alert-success'>✅ " + gares.length + " gares insérées</div>");

        // Insérer les trajets
        String insertTrajets = "INSERT INTO trajets (numero_trajet, gare_depart_id, gare_arrivee_id, duree_minutes, prix_base, type) VALUES (?, ?, ?, ?, ?, ?)";
        pstmt = conn.prepareStatement(insertTrajets);

        Object[][] trajets = {
            {"TN001", 1, 2, 280, 45.50, "EXPRESS"},
            {"TN002", 2, 1, 280, 45.50, "EXPRESS"},
            {"TN003", 1, 3, 140, 25.00, "DIRECT"},
            {"TN004", 3, 1, 140, 25.00, "DIRECT"},
            {"TN005", 1, 4, 160, 30.00, "NORMAL"},
            {"TN006", 4, 1, 160, 30.00, "NORMAL"},
            {"TN007", 2, 3, 120, 20.00, "DIRECT"},
            {"TN008", 3, 2, 120, 20.00, "DIRECT"}
        };

        for (Object[] trajet : trajets) {
            pstmt.setString(1, (String) trajet[0]);
            pstmt.setInt(2, (Integer) trajet[1]);
            pstmt.setInt(3, (Integer) trajet[2]);
            pstmt.setInt(4, (Integer) trajet[3]);
            pstmt.setDouble(5, (Double) trajet[4]);
            pstmt.setString(6, (String) trajet[5]);
            pstmt.executeUpdate();
        }
        pstmt.close();
        out.println("<div class='alert alert-success'>✅ " + trajets.length + " trajets insérés</div>");

        // Insérer quelques voyages pour demain
        String insertVoyages = "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles) VALUES (?, ?, ?, ?)";
        pstmt = conn.prepareStatement(insertVoyages);

        // Voyages pour demain
        for (int trajetId = 1; trajetId <= 8; trajetId++) {
            for (int hour = 8; hour < 20; hour += 4) {
                pstmt.setInt(1, trajetId);
                pstmt.setString(2, "DATE_ADD(DATE_ADD(CURDATE(), INTERVAL 1 DAY), INTERVAL " + hour + " HOUR)");
                pstmt.setString(3, "DATE_ADD(DATE_ADD(CURDATE(), INTERVAL 1 DAY), INTERVAL " + (hour + 3) + " HOUR)");
                pstmt.setInt(4, 200);
                pstmt.executeUpdate();
            }
        }
        pstmt.close();
        out.println("<div class='alert alert-success'>✅ Voyages de test insérés</div>");
    }

    private String getStackTrace(Exception e) {
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        e.printStackTrace(pw);
        return sw.toString();
    }
}
