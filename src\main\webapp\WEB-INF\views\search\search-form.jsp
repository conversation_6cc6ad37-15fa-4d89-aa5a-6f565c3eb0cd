<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rechercher un voyage - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="${pageContext.request.contextPath}/resources/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <jsp:include page="../common/navbar.jsp" />

    <!-- Hero Section -->
    <section class="hero-section bg-primary text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold">Trouvez votre voyage idéal</h1>
                    <p class="lead">Recherchez parmi nos nombreuses destinations et réservez votre billet en quelques clics</p>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-search fa-5x opacity-50"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Formulaire de recherche principal -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="card shadow-lg">
                        <div class="card-header bg-primary text-white">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-search"></i> Rechercher un voyage
                            </h3>
                        </div>
                        <div class="card-body p-4">
                            <!-- Messages -->
                            <c:if test="${not empty errorMessage}">
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-triangle"></i> ${errorMessage}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            </c:if>

                            <form action="${pageContext.request.contextPath}/recherche/search" method="post" id="searchForm">
                                <input type="hidden" name="action" value="search">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="villeDepart" class="form-label">
                                            <i class="fas fa-map-marker-alt text-primary"></i> Ville de départ
                                        </label>
                                        <select class="form-select" id="villeDepart" name="villeDepart" required>
                                            <option value="">Sélectionnez une ville</option>
                                            <c:forEach var="ville" items="${villes}">
                                                <option value="${ville}">${ville}</option>
                                            </c:forEach>
                                        </select>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="villeArrivee" class="form-label">
                                            <i class="fas fa-flag-checkered text-success"></i> Ville d'arrivée
                                        </label>
                                        <select class="form-select" id="villeArrivee" name="villeArrivee" required>
                                            <option value="">Sélectionnez une ville</option>
                                            <c:forEach var="ville" items="${villes}">
                                                <option value="${ville}">${ville}</option>
                                            </c:forEach>
                                        </select>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="dateVoyage" class="form-label">
                                            <i class="fas fa-calendar text-info"></i> Date de voyage
                                        </label>
                                        <input type="date" class="form-control" id="dateVoyage" name="dateVoyage"
                                               min="<fmt:formatDate value='${now}' pattern='yyyy-MM-dd'/>" required>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="nombrePassagers" class="form-label">
                                            <i class="fas fa-users text-warning"></i> Nombre de passagers
                                        </label>
                                        <select class="form-select" id="nombrePassagers" name="nombrePassagers">
                                            <option value="1">1 passager</option>
                                            <option value="2">2 passagers</option>
                                            <option value="3">3 passagers</option>
                                            <option value="4">4 passagers</option>
                                            <option value="5">5 passagers</option>
                                            <option value="6">6 passagers</option>
                                        </select>
                                    </div>

                                    <div class="col-12">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="voyagesDirects" name="voyagesDirects">
                                            <label class="form-check-label" for="voyagesDirects">
                                                <i class="fas fa-route"></i> Voyages directs uniquement
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Recherche avancée (collapsible) -->
                                <div class="mt-3">
                                    <button class="btn btn-outline-secondary" type="button" data-bs-toggle="collapse"
                                            data-bs-target="#advancedSearch" aria-expanded="false">
                                        <i class="fas fa-cog"></i> Options avancées
                                    </button>
                                </div>

                                <div class="collapse mt-3" id="advancedSearch">
                                    <div class="card card-body bg-light">
                                        <div class="row g-3">
                                            <div class="col-md-4">
                                                <label for="dureeMax" class="form-label">Durée maximale (heures)</label>
                                                <input type="number" class="form-control" id="dureeMax" name="dureeMax"
                                                       min="1" max="24" placeholder="Ex: 5">
                                            </div>

                                            <div class="col-md-4">
                                                <label for="prixMax" class="form-label">Prix maximum (MAD)</label>
                                                <input type="number" class="form-control" id="prixMax" name="prixMax"
                                                       min="0" step="10" placeholder="Ex: 500">
                                            </div>

                                            <div class="col-md-4">
                                                <label for="typeTrajet" class="form-label">Type de trajet</label>
                                                <select class="form-select" id="typeTrajet" name="typeTrajet">
                                                    <option value="">Tous les types</option>
                                                    <option value="EXPRESS">Express</option>
                                                    <option value="DIRECT">Direct</option>
                                                    <option value="NORMAL">Normal</option>
                                                </select>
                                            </div>

                                            <div class="col-md-4">
                                                <label for="classe" class="form-label">Classe préférée</label>
                                                <select class="form-select" id="classe" name="classe">
                                                    <option value="">Toutes les classes</option>
                                                    <option value="ECONOMIQUE">Économique</option>
                                                    <option value="DEUXIEME">Deuxième classe</option>
                                                    <option value="PREMIERE">Première classe</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-4">
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary btn-lg w-100" id="searchBtn">
                                            <i class="fas fa-search"></i> Rechercher des voyages
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Trajets populaires -->
    <c:if test="${not empty trajetsPopulaires}">
        <section class="py-5 bg-light">
            <div class="container">
                <h2 class="text-center mb-5">
                    <i class="fas fa-star text-warning"></i> Trajets populaires
                </h2>
                <div class="row">
                    <c:forEach var="trajet" items="${trajetsPopulaires}" varStatus="status">
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 shadow-sm">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-route text-primary"></i>
                                        ${trajet.gareDepart.ville} → ${trajet.gareArrivee.ville}
                                    </h5>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            <i class="fas fa-clock"></i> ${trajet.dureeMinutes} min
                                            <span class="ms-3">
                                                <i class="fas fa-tag"></i> À partir de ${trajet.prixBase} MAD
                                            </span>
                                        </small>
                                    </p>
                                    <span class="badge bg-${trajet.type == 'EXPRESS' ? 'danger' : trajet.type == 'DIRECT' ? 'warning' : 'secondary'}">
                                        ${trajet.type}
                                    </span>
                                </div>
                                <div class="card-footer">
                                    <button class="btn btn-outline-primary btn-sm w-100"
                                            onclick="selectRoute('${trajet.gareDepart.ville}', '${trajet.gareArrivee.ville}')">
                                        <i class="fas fa-mouse-pointer"></i> Sélectionner ce trajet
                                    </button>
                                </div>
                            </div>
                        </div>
                    </c:forEach>
                </div>
            </div>
        </section>
    </c:if>

    <!-- Conseils de voyage -->
    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">
                <i class="fas fa-lightbulb text-warning"></i> Conseils pour votre voyage
            </h2>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="text-center">
                        <div class="feature-icon bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                        <h5>Réservez à l'avance</h5>
                        <p>Réservez vos billets au moins 24h à l'avance pour bénéficier des meilleurs tarifs.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="text-center">
                        <div class="feature-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3">
                            <i class="fas fa-mobile-alt fa-2x"></i>
                        </div>
                        <h5>Billet électronique</h5>
                        <p>Présentez votre billet électronique directement depuis votre smartphone.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="text-center">
                        <div class="feature-icon bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3">
                            <i class="fas fa-luggage-cart fa-2x"></i>
                        </div>
                        <h5>Bagages</h5>
                        <p>Consultez notre politique bagages pour voyager en toute sérénité.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <jsp:include page="../common/footer.jsp" />

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/main.js"></script>

    <script>
        // Définir la date minimale à aujourd'hui
        document.getElementById('dateVoyage').min = new Date().toISOString().split('T')[0];

        // Empêcher la sélection de la même ville pour départ et arrivée
        document.getElementById('villeDepart').addEventListener('change', function() {
            const villeArrivee = document.getElementById('villeArrivee');
            const selectedValue = this.value;

            Array.from(villeArrivee.options).forEach(option => {
                option.disabled = option.value === selectedValue && option.value !== '';
            });

            // Réinitialiser la sélection si elle est identique
            if (villeArrivee.value === selectedValue && selectedValue !== '') {
                villeArrivee.value = '';
            }
        });

        document.getElementById('villeArrivee').addEventListener('change', function() {
            const villeDepart = document.getElementById('villeDepart');
            const selectedValue = this.value;

            Array.from(villeDepart.options).forEach(option => {
                option.disabled = option.value === selectedValue && option.value !== '';
            });

            // Réinitialiser la sélection si elle est identique
            if (villeDepart.value === selectedValue && selectedValue !== '') {
                villeDepart.value = '';
            }
        });

        // Fonction pour sélectionner un trajet populaire
        function selectRoute(villeDepart, villeArrivee) {
            document.getElementById('villeDepart').value = villeDepart;
            document.getElementById('villeArrivee').value = villeArrivee;

            // Déclencher les événements de changement
            document.getElementById('villeDepart').dispatchEvent(new Event('change'));
            document.getElementById('villeArrivee').dispatchEvent(new Event('change'));

            // Faire défiler vers le formulaire
            document.getElementById('searchForm').scrollIntoView({ behavior: 'smooth' });
        }

        // Gestion du formulaire de recherche
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            const villeDepart = document.getElementById('villeDepart').value;
            const villeArrivee = document.getElementById('villeArrivee').value;

            if (villeDepart === villeArrivee && villeDepart !== '') {
                e.preventDefault();
                alert('La ville de départ et d\'arrivée ne peuvent pas être identiques');
                return;
            }

            // Afficher un indicateur de chargement
            const submitBtn = document.getElementById('searchBtn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Recherche en cours...';
            submitBtn.disabled = true;
        });

        // Auto-complétion pour les villes (si nécessaire)
        // Peut être étendu avec une API de suggestions
    </script>
</body>
</html>
