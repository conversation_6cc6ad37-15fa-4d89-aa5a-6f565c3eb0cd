package com.trainticket.service.impl;

import com.trainticket.dao.GareDAO;
import com.trainticket.dao.TrajetDAO;
import com.trainticket.dao.VoyageDAO;
import com.trainticket.dao.impl.GareDAOImpl;
import com.trainticket.dao.impl.TrajetDAOImpl;
import com.trainticket.dao.impl.VoyageDAOImpl;
import com.trainticket.model.Gare;
import com.trainticket.model.Trajet;
import com.trainticket.model.Voyage;
import com.trainticket.service.RechercheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class RechercheServiceImpl implements RechercheService {

    private static final Logger logger = LoggerFactory.getLogger(RechercheServiceImpl.class);

    private final VoyageDAO voyageDAO;
    private final TrajetDAO trajetDAO;
    private final GareDAO gareDAO;

    public RechercheServiceImpl() {
        this.voyageDAO = new VoyageDAOImpl();
        this.trajetDAO = new TrajetDAOImpl();
        this.gareDAO = new GareDAOImpl();
    }

    public RechercheServiceImpl(VoyageDAO voyageDAO, TrajetDAO trajetDAO, GareDAO gareDAO) {
        this.voyageDAO = voyageDAO;
        this.trajetDAO = trajetDAO;
        this.gareDAO = gareDAO;
    }

    @Override
    public List<Voyage> rechercherVoyages(String villeDepart, String villeArrivee,
                                         LocalDate dateVoyage, int nombrePassagers,
                                         boolean voyagesDirectsUniquement) throws SearchException {
        try {
            validateSearchParameters(villeDepart, villeArrivee, dateVoyage, nombrePassagers);

            logger.info("Recherche de voyages: {} -> {} le {} pour {} passager(s), directs uniquement: {}",
                       villeDepart, villeArrivee, dateVoyage, nombrePassagers, voyagesDirectsUniquement);

            List<Voyage> voyages;

            if (voyagesDirectsUniquement) {
                voyages = voyageDAO.searchDirectVoyages(villeDepart, villeArrivee, dateVoyage);
            } else {
                // Utiliser la nouvelle méthode de recherche avancée
                voyages = ((VoyageDAOImpl) voyageDAO).searchVoyagesAvance(
                    villeDepart, villeArrivee, dateVoyage, nombrePassagers, null
                );
            }

            // Filtrer par nombre de passagers (double vérification)
            List<Voyage> resultats = voyages.stream()
                    .filter(voyage -> voyage.getPlacesDisponibles() >= nombrePassagers)
                    .collect(Collectors.toList());

            logger.info("Nombre de voyages trouvés après filtrage: {}", resultats.size());
            return resultats;

        } catch (SearchException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors de la recherche de voyages de {} à {} le {}",
                        villeDepart, villeArrivee, dateVoyage, e);
            throw new SearchException("Erreur lors de la recherche de voyages", e);
        }
    }

    @Override
    public List<Voyage> rechercherVoyagesDirects(String villeDepart, String villeArrivee,
                                                LocalDate dateVoyage) throws SearchException {
        try {
            validateSearchParameters(villeDepart, villeArrivee, dateVoyage, 1);

            return voyageDAO.searchDirectVoyages(villeDepart, villeArrivee, dateVoyage);

        } catch (SearchException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors de la recherche de voyages directs de {} à {} le {}",
                        villeDepart, villeArrivee, dateVoyage, e);
            throw new SearchException("Erreur lors de la recherche de voyages directs", e);
        }
    }

    @Override
    public List<List<Voyage>> rechercherVoyagesAvecCorrespondance(String villeDepart, String villeArrivee,
                                                                 LocalDate dateVoyage) throws SearchException {
        try {
            validateSearchParameters(villeDepart, villeArrivee, dateVoyage, 1);

            List<List<Voyage>> correspondances = new ArrayList<>();

            // Obtenir toutes les villes intermédiaires possibles
            List<String> villesIntermediaires = getVillesIntermediaires(villeDepart, villeArrivee);

            for (String villeIntermediaire : villesIntermediaires) {
                // Chercher les voyages de départ vers la ville intermédiaire
                List<Voyage> premiersVoyages = voyageDAO.searchVoyages(villeDepart, villeIntermediaire, dateVoyage);

                for (Voyage premierVoyage : premiersVoyages) {
                    // Chercher les voyages de la ville intermédiaire vers la destination
                    // avec un délai minimum de correspondance
                    LocalDate dateCorrespondance = premierVoyage.getDateHeureArrivee().toLocalDate();
                    if (premierVoyage.getDateHeureArrivee().getHour() >= 20) {
                        dateCorrespondance = dateCorrespondance.plusDays(1);
                    }

                    List<Voyage> secondsVoyages = voyageDAO.searchVoyages(villeIntermediaire, villeArrivee, dateCorrespondance);

                    for (Voyage secondVoyage : secondsVoyages) {
                        // Vérifier qu'il y a assez de temps pour la correspondance (minimum 1h)
                        if (secondVoyage.getDateHeureDepart().isAfter(
                                premierVoyage.getDateHeureArrivee().plusHours(1))) {

                            List<Voyage> correspondance = new ArrayList<>();
                            correspondance.add(premierVoyage);
                            correspondance.add(secondVoyage);
                            correspondances.add(correspondance);
                        }
                    }
                }
            }

            return correspondances;

        } catch (SearchException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors de la recherche de voyages avec correspondance de {} à {} le {}",
                        villeDepart, villeArrivee, dateVoyage, e);
            throw new SearchException("Erreur lors de la recherche de voyages avec correspondance", e);
        }
    }

    @Override
    public List<String> getVillesDisponibles() throws SearchException {
        try {
            return gareDAO.findDistinctCities();
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des villes disponibles", e);
            throw new SearchException("Erreur lors de la récupération des villes", e);
        }
    }

    @Override
    public List<Gare> getGaresActives() throws SearchException {
        try {
            return gareDAO.findActiveGares();
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des gares actives", e);
            throw new SearchException("Erreur lors de la récupération des gares", e);
        }
    }

    @Override
    public List<Gare> rechercherGares(String searchTerm) throws SearchException {
        try {
            if (searchTerm == null || searchTerm.trim().isEmpty()) {
                return getGaresActives();
            }

            return gareDAO.searchByNameOrCity(searchTerm.trim());

        } catch (SearchException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors de la recherche de gares avec le terme: {}", searchTerm, e);
            throw new SearchException("Erreur lors de la recherche de gares", e);
        }
    }

    @Override
    public List<Trajet> getTrajetsPopulaires(int limit) throws SearchException {
        try {
            return trajetDAO.findMostPopularTrajets(limit);
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des trajets populaires", e);
            throw new SearchException("Erreur lors de la récupération des trajets populaires", e);
        }
    }

    @Override
    public List<Voyage> getVoyagesDisponibles(LocalDate date) throws SearchException {
        try {
            if (date == null) {
                date = LocalDate.now();
            }

            if (date.isBefore(LocalDate.now())) {
                throw new SearchException("Impossible de rechercher des voyages dans le passé");
            }

            return voyageDAO.findByDate(date).stream()
                    .filter(voyage -> voyage.getPlacesDisponibles() > 0)
                    .filter(voyage -> voyage.getStatut() == Voyage.StatutVoyage.PROGRAMME)
                    .collect(Collectors.toList());

        } catch (SearchException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des voyages disponibles pour le {}", date, e);
            throw new SearchException("Erreur lors de la récupération des voyages", e);
        }
    }

    @Override
    public boolean verifierDisponibilite(Long voyageId, int nombrePassagers,
                                        Voyage.ClasseBillet classe) throws SearchException {
        try {
            Optional<Voyage> voyageOpt = voyageDAO.findById(voyageId);

            if (!voyageOpt.isPresent()) {
                return false;
            }

            Voyage voyage = voyageOpt.get();

            // Vérifier le statut du voyage
            if (voyage.getStatut() != Voyage.StatutVoyage.PROGRAMME) {
                return false;
            }

            // Vérifier la disponibilité générale
            if (voyage.getPlacesDisponibles() < nombrePassagers) {
                return false;
            }

            // Vérifier la disponibilité par classe
            return voyage.hasPlacesDisponibles(classe) &&
                   getPlacesDisponiblesParClasse(voyage, classe) >= nombrePassagers;

        } catch (Exception e) {
            logger.error("Erreur lors de la vérification de disponibilité pour le voyage {}", voyageId, e);
            throw new SearchException("Erreur lors de la vérification de disponibilité", e);
        }
    }

    @Override
    public Voyage getVoyageDetails(Long voyageId) throws SearchException {
        try {
            Optional<Voyage> voyageOpt = voyageDAO.findById(voyageId);

            if (!voyageOpt.isPresent()) {
                throw new SearchException("Voyage non trouvé");
            }

            return voyageOpt.get();

        } catch (SearchException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des détails du voyage {}", voyageId, e);
            throw new SearchException("Erreur lors de la récupération des détails du voyage", e);
        }
    }

    @Override
    public List<Voyage> rechercheAvancee(CriteresRecherche criteres) throws SearchException {
        try {
            // Recherche de base
            List<Voyage> voyages = rechercherVoyages(
                criteres.getVilleDepart(),
                criteres.getVilleArrivee(),
                criteres.getDateVoyage(),
                criteres.getNombrePassagers(),
                criteres.isVoyagesDirectsUniquement()
            );

            // Appliquer les filtres avancés
            return voyages.stream()
                    .filter(voyage -> {
                        // Filtre par durée maximale
                        if (criteres.getDureeMaxMinutes() != null) {
                            return voyage.getTrajet().getDureeMinutes() <= criteres.getDureeMaxMinutes();
                        }
                        return true;
                    })
                    .filter(voyage -> {
                        // Filtre par prix maximum
                        if (criteres.getPrixMax() != null) {
                            return voyage.getTrajet().getPrixBase().doubleValue() <= criteres.getPrixMax();
                        }
                        return true;
                    })
                    .filter(voyage -> {
                        // Filtre par type de trajet
                        if (criteres.getTypeTrajet() != null) {
                            return voyage.getTrajet().getType() == criteres.getTypeTrajet();
                        }
                        return true;
                    })
                    .filter(voyage -> {
                        // Filtre par classe préférée
                        if (criteres.getClassePreferee() != null) {
                            return voyage.hasPlacesDisponibles(criteres.getClassePreferee());
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

        } catch (SearchException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors de la recherche avancée", e);
            throw new SearchException("Erreur lors de la recherche avancée", e);
        }
    }

    @Override
    public List<String> getSuggestionsDestinations(String villeDepart) throws SearchException {
        try {
            if (villeDepart == null || villeDepart.trim().isEmpty()) {
                return getVillesDisponibles();
            }

            List<Trajet> trajets = trajetDAO.findByVilles(villeDepart, "%");

            return trajets.stream()
                    .map(trajet -> trajet.getGareArrivee().getVille())
                    .distinct()
                    .collect(Collectors.toList());

        } catch (SearchException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des suggestions pour {}", villeDepart, e);
            throw new SearchException("Erreur lors de la récupération des suggestions", e);
        }
    }

    /**
     * Valide les paramètres de recherche
     */
    private void validateSearchParameters(String villeDepart, String villeArrivee,
                                        LocalDate dateVoyage, int nombrePassagers) throws SearchException {
        if (villeDepart == null || villeDepart.trim().isEmpty()) {
            throw new SearchException("La ville de départ est requise");
        }

        if (villeArrivee == null || villeArrivee.trim().isEmpty()) {
            throw new SearchException("La ville d'arrivée est requise");
        }

        if (villeDepart.trim().equalsIgnoreCase(villeArrivee.trim())) {
            throw new SearchException("La ville de départ et d'arrivée ne peuvent pas être identiques");
        }

        if (dateVoyage == null) {
            throw new SearchException("La date de voyage est requise");
        }

        if (dateVoyage.isBefore(LocalDate.now())) {
            throw new SearchException("Impossible de rechercher des voyages dans le passé");
        }

        if (nombrePassagers <= 0 || nombrePassagers > 10) {
            throw new SearchException("Le nombre de passagers doit être entre 1 et 10");
        }
    }

    /**
     * Obtient les villes intermédiaires possibles pour les correspondances
     */
    private List<String> getVillesIntermediaires(String villeDepart, String villeArrivee) {
        try {
            List<String> toutes = getVillesDisponibles();
            return toutes.stream()
                    .filter(ville -> !ville.equals(villeDepart) && !ville.equals(villeArrivee))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des villes intermédiaires", e);
            return new ArrayList<>();
        }
    }

    /**
     * Obtient le nombre de places disponibles pour une classe donnée
     */
    private int getPlacesDisponiblesParClasse(Voyage voyage, Voyage.ClasseBillet classe) {
        switch (classe) {
            case PREMIERE:
                return voyage.getPlacesPremiereClasse();
            case DEUXIEME:
                return voyage.getPlacesDeuxiemeClasse();
            case ECONOMIQUE:
                return voyage.getPlacesEconomique();
            default:
                return 0;
        }
    }
}
