#!/bin/bash

echo "========================================"
echo "    TEST DE CONNEXION MYSQL"
echo "========================================"
echo

echo "Test 1: Vérification de MySQL..."
if command -v mysql &> /dev/null; then
    echo "✅ Client MySQL trouvé"
    echo "Version:"
    mysql --version
    
    echo
    echo "Test de connexion avec mot de passe 'root'..."
    if mysql -u root -proot -e "SELECT 'Connexion réussie!' as message;" 2>/dev/null; then
        echo "✅ Connexion MySQL réussie avec mot de passe 'root'"
        
        echo
        echo "Création de la base de données..."
        if mysql -u root -proot -e "CREATE DATABASE IF NOT EXISTS train_ticket_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null; then
            echo "✅ Base de données 'train_ticket_db' créée/vérifiée"
            
            echo
            echo "Vérification des tables existantes..."
            mysql -u root -proot -e "USE train_ticket_db; SHOW TABLES;" 2>/dev/null
            
        else
            echo "❌ Erreur lors de la création de la base de données"
        fi
        
    else
        echo "❌ Connexion échouée avec mot de passe 'root'"
        echo "Test sans mot de passe..."
        if mysql -u root -e "SELECT 'Connexion réussie!' as message;" 2>/dev/null; then
            echo "✅ Connexion MySQL réussie sans mot de passe"
            
            echo
            echo "Création de la base de données..."
            if mysql -u root -e "CREATE DATABASE IF NOT EXISTS train_ticket_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null; then
                echo "✅ Base de données 'train_ticket_db' créée/vérifiée"
            else
                echo "❌ Erreur lors de la création de la base de données"
            fi
            
        else
            echo "❌ Connexion échouée sans mot de passe"
            echo "Vérifiez que MySQL est démarré et accessible"
        fi
    fi
else
    echo "❌ Client MySQL non trouvé dans le PATH"
    echo "Vérifiez que MySQL est installé et dans le PATH"
fi

echo
echo "========================================"
echo "Test terminé."
