package com.trainticket.service;

import com.trainticket.model.DemandeAnnulation;
import com.trainticket.model.Billet;
import com.trainticket.model.Utilisateur;

import java.util.List;
import java.util.Optional;

/**
 * Service pour la gestion des annulations de billets
 */
public interface AnnulationService {

    /**
     * Crée une demande d'annulation pour un billet
     * @param billet le billet à annuler
     * @param utilisateur l'utilisateur demandeur
     * @param motif le motif de l'annulation
     * @return la demande d'annulation créée
     * @throws ServiceException si la demande ne peut pas être créée
     */
    DemandeAnnulation creerDemandeAnnulation(Billet billet, Utilisateur utilisateur, String motif) throws ServiceException;

    /**
     * Approuve une demande d'annulation
     * @param demandeId l'ID de la demande
     * @param admin l'administrateur qui traite la demande
     * @param commentaire commentaire de l'admin
     * @throws ServiceException si l'approbation échoue
     */
    void approuverDemande(Long demandeId, Utilisateur admin, String commentaire) throws ServiceException;

    /**
     * Rejette une demande d'annulation
     * @param demandeId l'ID de la demande
     * @param admin l'administrateur qui traite la demande
     * @param commentaire commentaire de l'admin
     * @throws ServiceException si le rejet échoue
     */
    void rejeterDemande(Long demandeId, Utilisateur admin, String commentaire) throws ServiceException;

    /**
     * Récupère toutes les demandes d'annulation en attente
     * @return liste des demandes en attente
     */
    List<DemandeAnnulation> getDemandesEnAttente();

    /**
     * Récupère les demandes d'annulation d'un utilisateur
     * @param utilisateur l'utilisateur
     * @return liste des demandes
     */
    List<DemandeAnnulation> getDemandesUtilisateur(Utilisateur utilisateur);

    /**
     * Vérifie si un billet peut être annulé
     * @param billet le billet
     * @return true si le billet peut être annulé
     */
    boolean peutEtreAnnule(Billet billet);

    /**
     * Récupère une demande d'annulation par son ID
     * @param id l'ID de la demande
     * @return Optional contenant la demande si trouvée
     */
    Optional<DemandeAnnulation> getDemandeById(Long id);

    /**
     * Vérifie si une demande d'annulation existe déjà pour un billet
     * @param billet le billet
     * @return true si une demande existe déjà
     */
    boolean demandeExistePourBillet(Billet billet);
}
