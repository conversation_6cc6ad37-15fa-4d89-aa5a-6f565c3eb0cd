package com.trainticket.controller;

import com.trainticket.dao.GareDAO;
import com.trainticket.dao.TrajetDAO;
import com.trainticket.dao.VoyageDAO;
import com.trainticket.dao.impl.GareDAOImpl;
import com.trainticket.dao.impl.TrajetDAOImpl;
import com.trainticket.dao.impl.VoyageDAOImpl;
import com.trainticket.model.Gare;
import com.trainticket.model.Trajet;
import com.trainticket.model.Voyage;
// Removed SLF4J imports - using System.out for logging

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

/**
 * Contrôleur pour la page d'accueil
 * Récupère les données dynamiques depuis la base de données
 */
public class HomeController {

    // Using System.out for logging instead of SLF4J

    private final GareDAO gareDAO;
    private final TrajetDAO trajetDAO;
    private final VoyageDAO voyageDAO;

    public HomeController() {
        this.gareDAO = new GareDAOImpl();
        this.trajetDAO = new TrajetDAOImpl();
        this.voyageDAO = new VoyageDAOImpl();
    }

    /**
     * Charge les données pour la page d'accueil
     * @param request la requête HTTP
     */
    public void loadHomeData(HttpServletRequest request) {
        try {
            // Récupérer les villes disponibles
            List<String> villesDisponibles = gareDAO.findDistinctCities();
            request.setAttribute("villesDisponibles", villesDisponibles);

            // Récupérer les statistiques
            loadStatistics(request);

            // Récupérer les trajets populaires
            loadPopularTrajets(request);

            System.out.println("Données de la page d'accueil chargées avec succès");

        } catch (Exception e) {
            System.err.println("Erreur lors du chargement des données de la page d'accueil: " + e.getMessage());
            // En cas d'erreur, on met des valeurs par défaut
            setDefaultValues(request);
        }
    }

    /**
     * Charge les statistiques pour la page d'accueil
     * @param request la requête HTTP
     */
    private void loadStatistics(HttpServletRequest request) {
        try {
            // Nombre de gares actives
            long nombreGares = gareDAO.countActiveGares();
            request.setAttribute("nombreGares", nombreGares);

            // Nombre de trajets actifs
            long nombreTrajets = trajetDAO.countActiveTrajets();
            request.setAttribute("nombreTrajets", nombreTrajets);

            // Nombre de voyages aujourd'hui
            List<Voyage> voyagesAujourdhui = voyageDAO.findByDate(LocalDate.now());
            request.setAttribute("nombreVoyagesAujourdhui", voyagesAujourdhui.size());

            // Nombre de villes distinctes
            List<String> villes = gareDAO.findDistinctCities();
            request.setAttribute("nombreVilles", villes.size());

            System.out.println("Statistiques chargées: " + nombreGares + " gares, " + nombreTrajets +
                              " trajets, " + voyagesAujourdhui.size() + " voyages aujourd'hui, " + villes.size() + " villes");

        } catch (Exception e) {
            System.err.println("Erreur lors du chargement des statistiques: " + e.getMessage());
            // Valeurs par défaut en cas d'erreur
            request.setAttribute("nombreGares", 0);
            request.setAttribute("nombreTrajets", 0);
            request.setAttribute("nombreVoyagesAujourdhui", 0);
            request.setAttribute("nombreVilles", 0);
        }
    }

    /**
     * Charge les trajets populaires
     * @param request la requête HTTP
     */
    private void loadPopularTrajets(HttpServletRequest request) {
        try {
            // Récupérer les trajets les plus populaires (limité à 6)
            List<Trajet> trajetsPopulaires = trajetDAO.findMostPopularTrajets(6);

            // Si pas assez de trajets populaires, prendre les trajets actifs
            if (trajetsPopulaires.size() < 6) {
                List<Trajet> trajetsActifs = trajetDAO.findActiveTrajets();
                // Compléter avec les trajets actifs jusqu'à 6
                for (Trajet trajet : trajetsActifs) {
                    if (trajetsPopulaires.size() >= 6) break;
                    if (!trajetsPopulaires.contains(trajet)) {
                        trajetsPopulaires.add(trajet);
                    }
                }
            }

            request.setAttribute("trajetsPopulaires", trajetsPopulaires);
            System.out.println("Trajets populaires chargés: " + trajetsPopulaires.size() + " trajets");

        } catch (Exception e) {
            System.err.println("Erreur lors du chargement des trajets populaires: " + e.getMessage());
            request.setAttribute("trajetsPopulaires", List.of());
        }
    }

    /**
     * Définit des valeurs par défaut en cas d'erreur
     * @param request la requête HTTP
     */
    private void setDefaultValues(HttpServletRequest request) {
        // Villes tunisiennes par défaut
        List<String> villesDefaut = Arrays.asList(
            "Tunis", "Sfax", "Sousse", "Kairouan", "Bizerte",
            "Gabès", "Ariana", "Gafsa", "Monastir", "Ben Arous"
        );

        request.setAttribute("villesDisponibles", villesDefaut);
        request.setAttribute("nombreGares", 0);
        request.setAttribute("nombreTrajets", 0);
        request.setAttribute("nombreVoyagesAujourdhui", 0);
        request.setAttribute("nombreVilles", villesDefaut.size());
        request.setAttribute("trajetsPopulaires", Arrays.asList());

        System.out.println("ATTENTION: Utilisation des valeurs par défaut pour la page d'accueil");
    }
}
