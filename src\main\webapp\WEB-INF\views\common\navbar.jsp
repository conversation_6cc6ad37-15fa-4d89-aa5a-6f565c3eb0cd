<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
    <div class="container">
        <!-- Logo et nom -->
        <a class="navbar-brand fw-bold" href="${pageContext.request.contextPath}/">
            <i class="fas fa-train"></i> Train Ticket
        </a>

        <!-- Bouton toggle pour mobile -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Menu principal -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="${pageContext.request.contextPath}/">
                        <i class="fas fa-home"></i> Accueil
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="${pageContext.request.contextPath}/recherche/search">
                        <i class="fas fa-search"></i> Rechercher
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="servicesDropdown"
                       role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-concierge-bell"></i> Services
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">
                            <i class="fas fa-info-circle"></i> Informations voyages
                        </a></li>
                        <li><a class="dropdown-item" href="#">
                            <i class="fas fa-luggage-cart"></i> Politique bagages
                        </a></li>
                        <li><a class="dropdown-item" href="#">
                            <i class="fas fa-wheelchair"></i> Accessibilité
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#">
                            <i class="fas fa-headset"></i> Support client
                        </a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">
                        <i class="fas fa-phone"></i> Contact
                    </a>
                </li>
            </ul>

            <!-- Menu utilisateur -->
            <ul class="navbar-nav">
                <c:choose>
                    <c:when test="${not empty sessionScope.utilisateur}">
                        <!-- Utilisateur connecté -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown"
                               role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle"></i>
                                ${sessionScope.utilisateur.prenom} ${sessionScope.utilisateur.nom}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <h6 class="dropdown-header">
                                        <i class="fas fa-user"></i> Mon compte
                                    </h6>
                                </li>
                                <li><a class="dropdown-item" href="${pageContext.request.contextPath}/user/profile">
                                    <i class="fas fa-id-card"></i> Mon profil
                                </a></li>
                                <li><a class="dropdown-item" href="${pageContext.request.contextPath}/user/reservations">
                                    <i class="fas fa-ticket-alt"></i> Mes réservations
                                    <c:if test="${not empty sessionScope.nombreReservationsActives}">
                                        <span class="badge bg-primary ms-1">${sessionScope.nombreReservationsActives}</span>
                                    </c:if>
                                </a></li>
                                <li><a class="dropdown-item" href="${pageContext.request.contextPath}/user/history">
                                    <i class="fas fa-history"></i> Historique
                                </a></li>
                                <li><hr class="dropdown-divider"></li>

                                <!-- Menu admin (si applicable) -->
                                <c:if test="${sessionScope.userRole == 'ADMIN'}">
                                    <li>
                                        <h6 class="dropdown-header">
                                            <i class="fas fa-cog"></i> Administration
                                        </h6>
                                    </li>
                                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/admin/dashboard">
                                        <i class="fas fa-tachometer-alt"></i> Tableau de bord
                                    </a></li>
                                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/admin/manage-users">
                                        <i class="fas fa-users"></i> Gestion utilisateurs
                                    </a></li>
                                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/admin/manage-voyages">
                                        <i class="fas fa-train"></i> Gestion voyages
                                    </a></li>
                                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/admin/reports">
                                        <i class="fas fa-chart-bar"></i> Rapports
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                </c:if>

                                <li><a class="dropdown-item" href="${pageContext.request.contextPath}/user/edit-profile">
                                    <i class="fas fa-edit"></i> Modifier profil
                                </a></li>
                                <li><a class="dropdown-item" href="${pageContext.request.contextPath}/user/change-password">
                                    <i class="fas fa-key"></i> Changer mot de passe
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="${pageContext.request.contextPath}/auth/logout">
                                    <i class="fas fa-sign-out-alt"></i> Se déconnecter
                                </a></li>
                            </ul>
                        </li>

                        <!-- Notifications (optionnel) -->
                        <li class="nav-item dropdown">
                            <a class="nav-link position-relative" href="#" id="notificationsDropdown"
                               role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-bell"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                                      id="notificationCount" style="display: none;">
                                    0
                                </span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                                <li>
                                    <h6 class="dropdown-header">
                                        <i class="fas fa-bell"></i> Notifications
                                    </h6>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li id="noNotifications" class="dropdown-item-text text-center text-muted">
                                    Aucune nouvelle notification
                                </li>
                                <div id="notificationsList"></div>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="#">
                                    <small>Voir toutes les notifications</small>
                                </a></li>
                            </ul>
                        </li>

                    </c:when>
                    <c:otherwise>
                        <!-- Utilisateur non connecté -->
                        <li class="nav-item">
                            <a class="nav-link" href="${pageContext.request.contextPath}/auth/login">
                                <i class="fas fa-sign-in-alt"></i> Se connecter
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="${pageContext.request.contextPath}/auth/register">
                                <i class="fas fa-user-plus"></i> S'inscrire
                            </a>
                        </li>
                    </c:otherwise>
                </c:choose>
            </ul>
        </div>
    </div>
</nav>

<!-- Messages flash -->
<c:if test="${not empty sessionScope.successMessage}">
    <div class="alert alert-success alert-dismissible fade show m-0" role="alert">
        <div class="container">
            <i class="fas fa-check-circle"></i> ${sessionScope.successMessage}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
    <c:remove var="successMessage" scope="session" />
</c:if>

<c:if test="${not empty sessionScope.errorMessage}">
    <div class="alert alert-danger alert-dismissible fade show m-0" role="alert">
        <div class="container">
            <i class="fas fa-exclamation-triangle"></i> ${sessionScope.errorMessage}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
    <c:remove var="errorMessage" scope="session" />
</c:if>

<c:if test="${not empty sessionScope.warningMessage}">
    <div class="alert alert-warning alert-dismissible fade show m-0" role="alert">
        <div class="container">
            <i class="fas fa-exclamation-circle"></i> ${sessionScope.warningMessage}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
    <c:remove var="warningMessage" scope="session" />
</c:if>

<c:if test="${not empty sessionScope.infoMessage}">
    <div class="alert alert-info alert-dismissible fade show m-0" role="alert">
        <div class="container">
            <i class="fas fa-info-circle"></i> ${sessionScope.infoMessage}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
    <c:remove var="infoMessage" scope="session" />
</c:if>

<script>
    // Marquer le lien actuel comme actif
    document.addEventListener('DOMContentLoaded', function() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

        navLinks.forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
        });

        // Charger les notifications si l'utilisateur est connecté
        loadNotifications();
    });

    // Fonction pour charger les notifications
    function loadNotifications() {
        // Simulation de chargement des notifications
        // Dans un vrai système, cela ferait un appel AJAX

        const notifications = [
            // Exemple de notifications
            // { id: 1, message: "Votre voyage de demain est confirmé", type: "info", time: "Il y a 2h" },
            // { id: 2, message: "Nouveau voyage disponible vers Casablanca", type: "success", time: "Il y a 1j" }
        ];

        const notificationCount = document.getElementById('notificationCount');
        const notificationsList = document.getElementById('notificationsList');
        const noNotifications = document.getElementById('noNotifications');

        if (notificationCount && notificationsList && noNotifications) {
            if (notifications.length > 0) {
                notificationCount.textContent = notifications.length;
                notificationCount.style.display = 'block';
                noNotifications.style.display = 'none';

                notificationsList.innerHTML = notifications.map(notif =>
                    '<li class="dropdown-item">' +
                        '<div class="d-flex">' +
                            '<div class="flex-shrink-0">' +
                                '<i class="fas fa-' + (notif.type === 'info' ? 'info-circle text-info' : 'check-circle text-success') + '"></i>' +
                            '</div>' +
                            '<div class="flex-grow-1 ms-2">' +
                                '<div class="small">' + notif.message + '</div>' +
                                '<div class="text-muted small">' + notif.time + '</div>' +
                            '</div>' +
                        '</div>' +
                    '</li>'
                ).join('');
            } else {
                notificationCount.style.display = 'none';
                noNotifications.style.display = 'block';
            }
        }
    }

    // Fonction pour marquer une notification comme lue
    function markNotificationAsRead(notificationId) {
        // Logique pour marquer comme lue
        console.log('Notification ' + notificationId + ' marquée comme lue');
    }

    // Auto-refresh des notifications toutes les 5 minutes
    setInterval(loadNotifications, 300000); // 5 minutes
</script>
