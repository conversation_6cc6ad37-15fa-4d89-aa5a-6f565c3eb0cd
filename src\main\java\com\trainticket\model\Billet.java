package com.trainticket.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

@Entity
@Table(name = "billets")
public class Billet {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true, length = 50)
    private String numeroBillet;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "utilisateur_id", nullable = false)
    private Utilisateur utilisateur;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "voyage_id", nullable = false)
    private Voyage voyage;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Voyage.ClasseBillet classe;

    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal prix;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private StatutBillet statut = StatutBillet.ACHETE;

    @Column(name = "date_achat", nullable = false)
    private LocalDateTime dateAchat;

    @Column(name = "date_utilisation")
    private LocalDateTime dateUtilisation;

    @Column(name = "date_annulation")
    private LocalDateTime dateAnnulation;

    @Column(name = "numero_siege")
    private String numeroSiege;

    @Embedded
    private Preference preferences;

    @Column(length = 500)
    private String commentaires;

    // Constructeurs
    public Billet() {
        this.dateAchat = LocalDateTime.now();
        this.numeroBillet = generateNumeroBillet();
    }

    public Billet(Utilisateur utilisateur, Voyage voyage, Voyage.ClasseBillet classe, BigDecimal prix) {
        this();
        this.utilisateur = utilisateur;
        this.voyage = voyage;
        this.classe = classe;
        this.prix = prix;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNumeroBillet() {
        return numeroBillet;
    }

    public void setNumeroBillet(String numeroBillet) {
        this.numeroBillet = numeroBillet;
    }

    public Utilisateur getUtilisateur() {
        return utilisateur;
    }

    public void setUtilisateur(Utilisateur utilisateur) {
        this.utilisateur = utilisateur;
    }

    public Voyage getVoyage() {
        return voyage;
    }

    public void setVoyage(Voyage voyage) {
        this.voyage = voyage;
    }

    public Voyage.ClasseBillet getClasse() {
        return classe;
    }

    public void setClasse(Voyage.ClasseBillet classe) {
        this.classe = classe;
    }

    public BigDecimal getPrix() {
        return prix;
    }

    public void setPrix(BigDecimal prix) {
        this.prix = prix;
    }

    public StatutBillet getStatut() {
        return statut;
    }

    public void setStatut(StatutBillet statut) {
        this.statut = statut;
    }

    public LocalDateTime getDateAchat() {
        return dateAchat;
    }

    public void setDateAchat(LocalDateTime dateAchat) {
        this.dateAchat = dateAchat;
    }

    public LocalDateTime getDateUtilisation() {
        return dateUtilisation;
    }

    public void setDateUtilisation(LocalDateTime dateUtilisation) {
        this.dateUtilisation = dateUtilisation;
    }

    public LocalDateTime getDateAnnulation() {
        return dateAnnulation;
    }

    public void setDateAnnulation(LocalDateTime dateAnnulation) {
        this.dateAnnulation = dateAnnulation;
    }

    public String getNumeroSiege() {
        return numeroSiege;
    }

    public void setNumeroSiege(String numeroSiege) {
        this.numeroSiege = numeroSiege;
    }

    public Preference getPreferences() {
        return preferences;
    }

    public void setPreferences(Preference preferences) {
        this.preferences = preferences;
    }

    public String getCommentaires() {
        return commentaires;
    }

    public void setCommentaires(String commentaires) {
        this.commentaires = commentaires;
    }

    // Méthodes utilitaires
    private String generateNumeroBillet() {
        return "TKT" + System.currentTimeMillis();
    }

    public boolean peutEtreAnnule() {
        return statut == StatutBillet.ACHETE &&
               voyage.getDateHeureDepart().isAfter(LocalDateTime.now().plusHours(2));
    }

    public boolean peutEtreUtilise() {
        return statut == StatutBillet.ACHETE &&
               !voyage.getDateHeureDepart().isBefore(LocalDateTime.now());
    }

    public void utiliser() {
        if (peutEtreUtilise()) {
            this.statut = StatutBillet.UTILISE;
            this.dateUtilisation = LocalDateTime.now();
        }
    }

    public void annuler() {
        if (peutEtreAnnule()) {
            this.statut = StatutBillet.ANNULE;
            this.dateAnnulation = LocalDateTime.now();
        }
    }

    public String getClasseLibelle() {
        switch (classe) {
            case PREMIERE:
                return "1ère Classe";
            case DEUXIEME:
                return "2ème Classe";
            case ECONOMIQUE:
                return "Économique";
            default:
                return "Inconnue";
        }
    }

    // Enums
    public enum StatutBillet {
        ACHETE, UTILISE, ANNULE, REMBOURSE
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Billet billet = (Billet) o;
        return Objects.equals(id, billet.id) && Objects.equals(numeroBillet, billet.numeroBillet);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, numeroBillet);
    }

    @Override
    public String toString() {
        return "Billet{" +
                "id=" + id +
                ", numeroBillet='" + numeroBillet + '\'' +
                ", utilisateur=" + (utilisateur != null ? utilisateur.getNomComplet() : "null") +
                ", voyage=" + (voyage != null && voyage.getTrajet() != null ? voyage.getTrajet().getNumeroTrajet() : "null") +
                ", classe=" + classe +
                ", prix=" + prix +
                ", statut=" + statut +
                '}';
    }
}
