package com.trainticket.service.impl;

import com.trainticket.dao.BilletDAO;
import com.trainticket.dao.VoyageDAO;
import com.trainticket.dao.impl.BilletDAOImpl;
import com.trainticket.dao.impl.VoyageDAOImpl;
import com.trainticket.model.*;
import com.trainticket.service.ReservationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class ReservationServiceImpl implements ReservationService {
    
    private static final Logger logger = LoggerFactory.getLogger(ReservationServiceImpl.class);
    
    private final BilletDAO billetDAO;
    private final VoyageDAO voyageDAO;
    
    // Multiplicateurs de prix par classe
    private static final BigDecimal MULTIPLICATEUR_PREMIERE = new BigDecimal("2.0");
    private static final BigDecimal MULTIPLICATEUR_DEUXIEME = new BigDecimal("1.5");
    private static final BigDecimal MULTIPLICATEUR_ECONOMIQUE = new BigDecimal("1.0");
    
    // Frais supplémentaires pour les préférences
    private static final BigDecimal FRAIS_WIFI = new BigDecimal("10.0");
    private static final BigDecimal FRAIS_PRISE_ELECTRIQUE = new BigDecimal("5.0");
    private static final BigDecimal FRAIS_ESPACE_FAMILLE = new BigDecimal("15.0");
    
    public ReservationServiceImpl() {
        this.billetDAO = new BilletDAOImpl();
        this.voyageDAO = new VoyageDAOImpl();
    }
    
    public ReservationServiceImpl(BilletDAO billetDAO, VoyageDAO voyageDAO) {
        this.billetDAO = billetDAO;
        this.voyageDAO = voyageDAO;
    }
    
    @Override
    public Billet creerReservation(Utilisateur utilisateur, Voyage voyage, 
                                  Voyage.ClasseBillet classe, Preference preferences) 
                                  throws ReservationException {
        try {
            // Vérifier la disponibilité
            if (!verifierDisponibilite(voyage, classe, 1)) {
                throw new ReservationException("Aucune place disponible pour cette classe");
            }
            
            // Calculer le prix
            BigDecimal prix = calculerPrix(voyage, classe, preferences);
            
            // Créer le billet
            Billet billet = new Billet(utilisateur, voyage, classe, prix);
            billet.setPreferences(preferences);
            
            // Réserver la place dans le voyage
            voyage.reserverPlace(classe);
            voyageDAO.update(voyage);
            
            // Sauvegarder le billet
            Billet billetSauvegarde = billetDAO.save(billet);
            
            logger.info("Réservation créée: billet {} pour l'utilisateur {} (voyage {})", 
                       billetSauvegarde.getNumeroBillet(), utilisateur.getId(), voyage.getId());
            
            return billetSauvegarde;
            
        } catch (ReservationException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors de la création de la réservation", e);
            throw new ReservationException("Erreur lors de la création de la réservation", e);
        }
    }
    
    @Override
    public List<Billet> creerReservationsGroupees(Utilisateur utilisateur, Voyage voyage, 
                                                 List<DetailsReservation> reservations) 
                                                 throws ReservationException {
        try {
            // Vérifier la disponibilité pour toutes les réservations
            for (DetailsReservation details : reservations) {
                if (!verifierDisponibilite(voyage, details.getClasse(), 1)) {
                    throw new ReservationException("Places insuffisantes pour la classe " + 
                                                 details.getClasse());
                }
            }
            
            // Créer tous les billets
            return reservations.stream()
                    .map(details -> {
                        try {
                            return creerReservation(utilisateur, voyage, 
                                                  details.getClasse(), details.getPreferences());
                        } catch (ReservationException e) {
                            throw new RuntimeException(e);
                        }
                    })
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            logger.error("Erreur lors de la création des réservations groupées", e);
            throw new ReservationException("Erreur lors de la création des réservations groupées", e);
        }
    }
    
    @Override
    public boolean annulerReservation(Long billetId, Utilisateur utilisateur) 
                                     throws ReservationException {
        try {
            Optional<Billet> billetOpt = billetDAO.findById(billetId);
            
            if (!billetOpt.isPresent()) {
                throw new ReservationException("Billet non trouvé");
            }
            
            Billet billet = billetOpt.get();
            
            // Vérifier que le billet appartient à l'utilisateur
            if (!billet.getUtilisateur().getId().equals(utilisateur.getId())) {
                throw new ReservationException("Vous n'êtes pas autorisé à annuler ce billet");
            }
            
            // Vérifier si le billet peut être annulé
            if (!peutEtreAnnule(billet)) {
                throw new ReservationException("Ce billet ne peut plus être annulé");
            }
            
            // Annuler le billet
            billet.annuler();
            
            // Libérer la place dans le voyage
            Voyage voyage = billet.getVoyage();
            voyage.libererPlace(billet.getClasse());
            voyageDAO.update(voyage);
            
            // Sauvegarder les modifications
            billetDAO.update(billet);
            
            logger.info("Réservation annulée: billet {} pour l'utilisateur {}", 
                       billet.getNumeroBillet(), utilisateur.getId());
            
            return true;
            
        } catch (ReservationException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors de l'annulation de la réservation", e);
            throw new ReservationException("Erreur lors de l'annulation", e);
        }
    }
    
    @Override
    public Billet modifierReservation(Long billetId, Voyage nouveauVoyage, 
                                     Voyage.ClasseBillet nouvelleClasse, Utilisateur utilisateur) 
                                     throws ReservationException {
        try {
            Optional<Billet> billetOpt = billetDAO.findById(billetId);
            
            if (!billetOpt.isPresent()) {
                throw new ReservationException("Billet non trouvé");
            }
            
            Billet billet = billetOpt.get();
            
            // Vérifier que le billet appartient à l'utilisateur
            if (!billet.getUtilisateur().getId().equals(utilisateur.getId())) {
                throw new ReservationException("Vous n'êtes pas autorisé à modifier ce billet");
            }
            
            // Vérifier si le billet peut être modifié
            if (!peutEtreModifie(billet)) {
                throw new ReservationException("Ce billet ne peut plus être modifié");
            }
            
            // Vérifier la disponibilité du nouveau voyage
            if (!verifierDisponibilite(nouveauVoyage, nouvelleClasse, 1)) {
                throw new ReservationException("Aucune place disponible pour cette nouvelle réservation");
            }
            
            // Libérer l'ancienne place
            Voyage ancienVoyage = billet.getVoyage();
            ancienVoyage.libererPlace(billet.getClasse());
            voyageDAO.update(ancienVoyage);
            
            // Réserver la nouvelle place
            nouveauVoyage.reserverPlace(nouvelleClasse);
            voyageDAO.update(nouveauVoyage);
            
            // Mettre à jour le billet
            billet.setVoyage(nouveauVoyage);
            billet.setClasse(nouvelleClasse);
            
            // Recalculer le prix
            BigDecimal nouveauPrix = calculerPrix(nouveauVoyage, nouvelleClasse, billet.getPreferences());
            billet.setPrix(nouveauPrix);
            
            // Sauvegarder les modifications
            Billet billetModifie = billetDAO.update(billet);
            
            logger.info("Réservation modifiée: billet {} pour l'utilisateur {}", 
                       billet.getNumeroBillet(), utilisateur.getId());
            
            return billetModifie;
            
        } catch (ReservationException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors de la modification de la réservation", e);
            throw new ReservationException("Erreur lors de la modification", e);
        }
    }
    
    @Override
    public BigDecimal calculerPrix(Voyage voyage, Voyage.ClasseBillet classe, Preference preferences) 
                                  throws ReservationException {
        try {
            BigDecimal prixBase = voyage.getTrajet().getPrixBase();
            
            // Appliquer le multiplicateur de classe
            BigDecimal multiplicateur = getMultiplicateurClasse(classe);
            BigDecimal prixAvecClasse = prixBase.multiply(multiplicateur);
            
            // Ajouter les frais pour les préférences
            BigDecimal fraisPreferences = calculerFraisPreferences(preferences);
            
            return prixAvecClasse.add(fraisPreferences);
            
        } catch (Exception e) {
            logger.error("Erreur lors du calcul du prix", e);
            throw new ReservationException("Erreur lors du calcul du prix", e);
        }
    }
    
    @Override
    public boolean verifierDisponibilite(Voyage voyage, Voyage.ClasseBillet classe, int nombrePlaces) {
        if (voyage.getStatut() != Voyage.StatutVoyage.PROGRAMME) {
            return false;
        }
        
        if (voyage.getDateHeureDepart().isBefore(LocalDateTime.now())) {
            return false;
        }
        
        return voyage.hasPlacesDisponibles(classe) && 
               getPlacesDisponiblesParClasse(voyage, classe) >= nombrePlaces;
    }
    
    @Override
    public List<Billet> getReservationsUtilisateur(Utilisateur utilisateur) 
                                                   throws ReservationException {
        try {
            return billetDAO.findByUtilisateur(utilisateur);
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des réservations", e);
            throw new ReservationException("Erreur lors de la récupération des réservations", e);
        }
    }
    
    @Override
    public List<Billet> getReservationsActives(Utilisateur utilisateur) 
                                              throws ReservationException {
        try {
            return billetDAO.findPurchasedBillets(utilisateur);
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des réservations actives", e);
            throw new ReservationException("Erreur lors de la récupération des réservations actives", e);
        }
    }
    
    @Override
    public List<Billet> getHistoriqueVoyages(Utilisateur utilisateur) 
                                            throws ReservationException {
        try {
            return billetDAO.findUserHistory(utilisateur);
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération de l'historique", e);
            throw new ReservationException("Erreur lors de la récupération de l'historique", e);
        }
    }
    
    @Override
    public boolean validerBillet(String numeroBillet) throws ReservationException {
        try {
            Optional<Billet> billetOpt = billetDAO.findByNumeroBillet(numeroBillet);
            
            if (!billetOpt.isPresent()) {
                return false;
            }
            
            Billet billet = billetOpt.get();
            
            if (!billet.peutEtreUtilise()) {
                return false;
            }
            
            billet.utiliser();
            billetDAO.update(billet);
            
            logger.info("Billet validé: {}", numeroBillet);
            return true;
            
        } catch (Exception e) {
            logger.error("Erreur lors de la validation du billet {}", numeroBillet, e);
            throw new ReservationException("Erreur lors de la validation du billet", e);
        }
    }
    
    @Override
    public Billet getBilletDetails(String numeroBillet) throws ReservationException {
        try {
            Optional<Billet> billetOpt = billetDAO.findByNumeroBillet(numeroBillet);
            
            if (!billetOpt.isPresent()) {
                throw new ReservationException("Billet non trouvé");
            }
            
            return billetOpt.get();
            
        } catch (ReservationException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des détails du billet {}", numeroBillet, e);
            throw new ReservationException("Erreur lors de la récupération des détails", e);
        }
    }
    
    @Override
    public boolean peutEtreAnnule(Billet billet) {
        return billet.peutEtreAnnule();
    }
    
    @Override
    public boolean peutEtreModifie(Billet billet) {
        return billet.getStatut() == Billet.StatutBillet.ACHETE && 
               billet.getVoyage().getDateHeureDepart().isAfter(LocalDateTime.now().plusHours(4));
    }
    
    @Override
    public BigDecimal calculerFraisAnnulation(Billet billet) {
        LocalDateTime maintenant = LocalDateTime.now();
        LocalDateTime dateVoyage = billet.getVoyage().getDateHeureDepart();
        
        long heuresAvantVoyage = java.time.Duration.between(maintenant, dateVoyage).toHours();
        
        if (heuresAvantVoyage >= 48) {
            return BigDecimal.ZERO; // Annulation gratuite
        } else if (heuresAvantVoyage >= 24) {
            return billet.getPrix().multiply(new BigDecimal("0.1")); // 10% de frais
        } else if (heuresAvantVoyage >= 2) {
            return billet.getPrix().multiply(new BigDecimal("0.25")); // 25% de frais
        } else {
            return billet.getPrix(); // 100% de frais (pas de remboursement)
        }
    }
    
    @Override
    public BigDecimal appliquerPromotion(BigDecimal prix, String codePromo, Utilisateur utilisateur) 
                                        throws ReservationException {
        if (codePromo == null || codePromo.trim().isEmpty()) {
            return prix;
        }
        
        // Codes promo simples (dans un vrai système, ils seraient en base de données)
        switch (codePromo.toUpperCase()) {
            case "WELCOME10":
                return prix.multiply(new BigDecimal("0.9")); // 10% de réduction
            case "STUDENT15":
                return prix.multiply(new BigDecimal("0.85")); // 15% de réduction
            case "FAMILY20":
                return prix.multiply(new BigDecimal("0.8")); // 20% de réduction
            case "SENIOR25":
                return prix.multiply(new BigDecimal("0.75")); // 25% de réduction
            default:
                throw new ReservationException("Code promo invalide ou expiré");
        }
    }
    
    // Méthodes utilitaires privées
    
    private BigDecimal getMultiplicateurClasse(Voyage.ClasseBillet classe) {
        switch (classe) {
            case PREMIERE:
                return MULTIPLICATEUR_PREMIERE;
            case DEUXIEME:
                return MULTIPLICATEUR_DEUXIEME;
            case ECONOMIQUE:
                return MULTIPLICATEUR_ECONOMIQUE;
            default:
                return MULTIPLICATEUR_ECONOMIQUE;
        }
    }
    
    private BigDecimal calculerFraisPreferences(Preference preferences) {
        BigDecimal frais = BigDecimal.ZERO;
        
        if (preferences != null) {
            if (preferences.getWifiRequis() != null && preferences.getWifiRequis()) {
                frais = frais.add(FRAIS_WIFI);
            }
            
            if (preferences.getPriseElectrique() != null && preferences.getPriseElectrique()) {
                frais = frais.add(FRAIS_PRISE_ELECTRIQUE);
            }
            
            if (preferences.getEspaceFamille() != null && preferences.getEspaceFamille()) {
                frais = frais.add(FRAIS_ESPACE_FAMILLE);
            }
        }
        
        return frais;
    }
    
    private int getPlacesDisponiblesParClasse(Voyage voyage, Voyage.ClasseBillet classe) {
        switch (classe) {
            case PREMIERE:
                return voyage.getPlacesPremiereClasse();
            case DEUXIEME:
                return voyage.getPlacesDeuxiemeClasse();
            case ECONOMIQUE:
                return voyage.getPlacesEconomique();
            default:
                return 0;
        }
    }
}
