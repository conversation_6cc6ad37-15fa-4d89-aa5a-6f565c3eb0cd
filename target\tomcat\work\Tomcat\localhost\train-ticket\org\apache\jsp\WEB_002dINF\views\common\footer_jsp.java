/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-05-29 14:45:15 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.views.common;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;

public final class footer_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("<footer class=\"bg-dark text-light py-5 mt-5\">\n");
      out.write("    <div class=\"container\">\n");
      out.write("        <div class=\"row\">\n");
      out.write("            <!-- À propos -->\n");
      out.write("            <div class=\"col-lg-4 col-md-6 mb-4\">\n");
      out.write("                <h5 class=\"text-primary mb-3\">\n");
      out.write("                    <i class=\"fas fa-train\"></i> Train Ticket\n");
      out.write("                </h5>\n");
      out.write("                <p class=\"text-light-50\">\n");
      out.write("                    Votre plateforme de réservation de billets de train en ligne. \n");
      out.write("                    Voyagez facilement et en toute sécurité à travers le Maroc.\n");
      out.write("                </p>\n");
      out.write("                <div class=\"d-flex gap-3\">\n");
      out.write("                    <a href=\"#\" class=\"text-light\" title=\"Facebook\">\n");
      out.write("                        <i class=\"fab fa-facebook-f fa-lg\"></i>\n");
      out.write("                    </a>\n");
      out.write("                    <a href=\"#\" class=\"text-light\" title=\"Twitter\">\n");
      out.write("                        <i class=\"fab fa-twitter fa-lg\"></i>\n");
      out.write("                    </a>\n");
      out.write("                    <a href=\"#\" class=\"text-light\" title=\"Instagram\">\n");
      out.write("                        <i class=\"fab fa-instagram fa-lg\"></i>\n");
      out.write("                    </a>\n");
      out.write("                    <a href=\"#\" class=\"text-light\" title=\"LinkedIn\">\n");
      out.write("                        <i class=\"fab fa-linkedin-in fa-lg\"></i>\n");
      out.write("                    </a>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("            \n");
      out.write("            <!-- Liens rapides -->\n");
      out.write("            <div class=\"col-lg-2 col-md-6 mb-4\">\n");
      out.write("                <h6 class=\"text-uppercase mb-3\">Liens rapides</h6>\n");
      out.write("                <ul class=\"list-unstyled\">\n");
      out.write("                    <li class=\"mb-2\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/\" class=\"text-light-50 text-decoration-none\">\n");
      out.write("                            <i class=\"fas fa-home fa-sm\"></i> Accueil\n");
      out.write("                        </a>\n");
      out.write("                    </li>\n");
      out.write("                    <li class=\"mb-2\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/recherche/search\" class=\"text-light-50 text-decoration-none\">\n");
      out.write("                            <i class=\"fas fa-search fa-sm\"></i> Rechercher\n");
      out.write("                        </a>\n");
      out.write("                    </li>\n");
      out.write("                    <li class=\"mb-2\">\n");
      out.write("                        <a href=\"#\" class=\"text-light-50 text-decoration-none\">\n");
      out.write("                            <i class=\"fas fa-info-circle fa-sm\"></i> À propos\n");
      out.write("                        </a>\n");
      out.write("                    </li>\n");
      out.write("                    <li class=\"mb-2\">\n");
      out.write("                        <a href=\"#\" class=\"text-light-50 text-decoration-none\">\n");
      out.write("                            <i class=\"fas fa-phone fa-sm\"></i> Contact\n");
      out.write("                        </a>\n");
      out.write("                    </li>\n");
      out.write("                </ul>\n");
      out.write("            </div>\n");
      out.write("            \n");
      out.write("            <!-- Services -->\n");
      out.write("            <div class=\"col-lg-2 col-md-6 mb-4\">\n");
      out.write("                <h6 class=\"text-uppercase mb-3\">Services</h6>\n");
      out.write("                <ul class=\"list-unstyled\">\n");
      out.write("                    <li class=\"mb-2\">\n");
      out.write("                        <a href=\"#\" class=\"text-light-50 text-decoration-none\">\n");
      out.write("                            <i class=\"fas fa-ticket-alt fa-sm\"></i> Réservation\n");
      out.write("                        </a>\n");
      out.write("                    </li>\n");
      out.write("                    <li class=\"mb-2\">\n");
      out.write("                        <a href=\"#\" class=\"text-light-50 text-decoration-none\">\n");
      out.write("                            <i class=\"fas fa-undo fa-sm\"></i> Annulation\n");
      out.write("                        </a>\n");
      out.write("                    </li>\n");
      out.write("                    <li class=\"mb-2\">\n");
      out.write("                        <a href=\"#\" class=\"text-light-50 text-decoration-none\">\n");
      out.write("                            <i class=\"fas fa-luggage-cart fa-sm\"></i> Bagages\n");
      out.write("                        </a>\n");
      out.write("                    </li>\n");
      out.write("                    <li class=\"mb-2\">\n");
      out.write("                        <a href=\"#\" class=\"text-light-50 text-decoration-none\">\n");
      out.write("                            <i class=\"fas fa-wheelchair fa-sm\"></i> Accessibilité\n");
      out.write("                        </a>\n");
      out.write("                    </li>\n");
      out.write("                </ul>\n");
      out.write("            </div>\n");
      out.write("            \n");
      out.write("            <!-- Support -->\n");
      out.write("            <div class=\"col-lg-2 col-md-6 mb-4\">\n");
      out.write("                <h6 class=\"text-uppercase mb-3\">Support</h6>\n");
      out.write("                <ul class=\"list-unstyled\">\n");
      out.write("                    <li class=\"mb-2\">\n");
      out.write("                        <a href=\"#\" class=\"text-light-50 text-decoration-none\">\n");
      out.write("                            <i class=\"fas fa-question-circle fa-sm\"></i> FAQ\n");
      out.write("                        </a>\n");
      out.write("                    </li>\n");
      out.write("                    <li class=\"mb-2\">\n");
      out.write("                        <a href=\"#\" class=\"text-light-50 text-decoration-none\">\n");
      out.write("                            <i class=\"fas fa-headset fa-sm\"></i> Support client\n");
      out.write("                        </a>\n");
      out.write("                    </li>\n");
      out.write("                    <li class=\"mb-2\">\n");
      out.write("                        <a href=\"#\" class=\"text-light-50 text-decoration-none\">\n");
      out.write("                            <i class=\"fas fa-file-alt fa-sm\"></i> Conditions\n");
      out.write("                        </a>\n");
      out.write("                    </li>\n");
      out.write("                    <li class=\"mb-2\">\n");
      out.write("                        <a href=\"#\" class=\"text-light-50 text-decoration-none\">\n");
      out.write("                            <i class=\"fas fa-shield-alt fa-sm\"></i> Confidentialité\n");
      out.write("                        </a>\n");
      out.write("                    </li>\n");
      out.write("                </ul>\n");
      out.write("            </div>\n");
      out.write("            \n");
      out.write("            <!-- Contact -->\n");
      out.write("            <div class=\"col-lg-2 col-md-6 mb-4\">\n");
      out.write("                <h6 class=\"text-uppercase mb-3\">Contact</h6>\n");
      out.write("                <ul class=\"list-unstyled\">\n");
      out.write("                    <li class=\"mb-2 text-light-50\">\n");
      out.write("                        <i class=\"fas fa-map-marker-alt fa-sm\"></i>\n");
      out.write("                        Casablanca, Maroc\n");
      out.write("                    </li>\n");
      out.write("                    <li class=\"mb-2 text-light-50\">\n");
      out.write("                        <i class=\"fas fa-phone fa-sm\"></i>\n");
      out.write("                        +212 5 22 XX XX XX\n");
      out.write("                    </li>\n");
      out.write("                    <li class=\"mb-2 text-light-50\">\n");
      out.write("                        <i class=\"fas fa-envelope fa-sm\"></i>\n");
      out.write("                        <EMAIL>\n");
      out.write("                    </li>\n");
      out.write("                    <li class=\"mb-2 text-light-50\">\n");
      out.write("                        <i class=\"fas fa-clock fa-sm\"></i>\n");
      out.write("                        24h/24 - 7j/7\n");
      out.write("                    </li>\n");
      out.write("                </ul>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("        \n");
      out.write("        <!-- Newsletter -->\n");
      out.write("        <div class=\"row mt-4\">\n");
      out.write("            <div class=\"col-lg-8\">\n");
      out.write("                <h6 class=\"text-uppercase mb-3\">Newsletter</h6>\n");
      out.write("                <p class=\"text-light-50 mb-3\">\n");
      out.write("                    Restez informé de nos dernières offres et actualités\n");
      out.write("                </p>\n");
      out.write("                <form class=\"row g-2\" id=\"newsletterForm\">\n");
      out.write("                    <div class=\"col-md-8\">\n");
      out.write("                        <input type=\"email\" class=\"form-control\" placeholder=\"Votre adresse email\" \n");
      out.write("                               id=\"newsletterEmail\" required>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-md-4\">\n");
      out.write("                        <button type=\"submit\" class=\"btn btn-primary w-100\">\n");
      out.write("                            <i class=\"fas fa-paper-plane\"></i> S'abonner\n");
      out.write("                        </button>\n");
      out.write("                    </div>\n");
      out.write("                </form>\n");
      out.write("            </div>\n");
      out.write("            \n");
      out.write("            <!-- Moyens de paiement -->\n");
      out.write("            <div class=\"col-lg-4\">\n");
      out.write("                <h6 class=\"text-uppercase mb-3\">Moyens de paiement</h6>\n");
      out.write("                <div class=\"d-flex gap-2 flex-wrap\">\n");
      out.write("                    <img src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/resources/images/payment/visa.png\" \n");
      out.write("                         alt=\"Visa\" class=\"payment-logo\" style=\"height: 30px;\">\n");
      out.write("                    <img src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/resources/images/payment/mastercard.png\" \n");
      out.write("                         alt=\"MasterCard\" class=\"payment-logo\" style=\"height: 30px;\">\n");
      out.write("                    <img src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/resources/images/payment/paypal.png\" \n");
      out.write("                         alt=\"PayPal\" class=\"payment-logo\" style=\"height: 30px;\">\n");
      out.write("                    <span class=\"badge bg-success\">\n");
      out.write("                        <i class=\"fas fa-shield-alt\"></i> Paiement sécurisé\n");
      out.write("                    </span>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("        \n");
      out.write("        <hr class=\"my-4\">\n");
      out.write("        \n");
      out.write("        <!-- Copyright et liens légaux -->\n");
      out.write("        <div class=\"row align-items-center\">\n");
      out.write("            <div class=\"col-md-6\">\n");
      out.write("                <p class=\"mb-0 text-light-50\">\n");
      out.write("                    &copy; <span id=\"currentYear\"></span> Train Ticket. Tous droits réservés.\n");
      out.write("                </p>\n");
      out.write("            </div>\n");
      out.write("            <div class=\"col-md-6 text-md-end\">\n");
      out.write("                <div class=\"d-flex justify-content-md-end gap-3 flex-wrap\">\n");
      out.write("                    <a href=\"#\" class=\"text-light-50 text-decoration-none small\">\n");
      out.write("                        Mentions légales\n");
      out.write("                    </a>\n");
      out.write("                    <a href=\"#\" class=\"text-light-50 text-decoration-none small\">\n");
      out.write("                        Politique de confidentialité\n");
      out.write("                    </a>\n");
      out.write("                    <a href=\"#\" class=\"text-light-50 text-decoration-none small\">\n");
      out.write("                        Conditions d'utilisation\n");
      out.write("                    </a>\n");
      out.write("                    <a href=\"#\" class=\"text-light-50 text-decoration-none small\">\n");
      out.write("                        Plan du site\n");
      out.write("                    </a>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("</footer>\n");
      out.write("\n");
      out.write("<!-- Bouton retour en haut -->\n");
      out.write("<button type=\"button\" class=\"btn btn-primary btn-floating\" id=\"backToTop\" \n");
      out.write("        style=\"position: fixed; bottom: 20px; right: 20px; display: none; z-index: 1000;\">\n");
      out.write("    <i class=\"fas fa-arrow-up\"></i>\n");
      out.write("</button>\n");
      out.write("\n");
      out.write("<!-- Chat support (optionnel) -->\n");
      out.write("<div class=\"chat-widget\" style=\"position: fixed; bottom: 20px; left: 20px; z-index: 1000;\">\n");
      out.write("    <button type=\"button\" class=\"btn btn-success btn-floating\" id=\"chatToggle\" \n");
      out.write("            data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" title=\"Support en ligne\">\n");
      out.write("        <i class=\"fas fa-comments\"></i>\n");
      out.write("    </button>\n");
      out.write("</div>\n");
      out.write("\n");
      out.write("<script>\n");
      out.write("    // Année courante\n");
      out.write("    document.getElementById('currentYear').textContent = new Date().getFullYear();\n");
      out.write("    \n");
      out.write("    // Newsletter\n");
      out.write("    document.getElementById('newsletterForm').addEventListener('submit', function(e) {\n");
      out.write("        e.preventDefault();\n");
      out.write("        const email = document.getElementById('newsletterEmail').value;\n");
      out.write("        \n");
      out.write("        if (email) {\n");
      out.write("            // Simulation d'inscription à la newsletter\n");
      out.write("            alert('Merci pour votre inscription à notre newsletter !');\n");
      out.write("            this.reset();\n");
      out.write("        }\n");
      out.write("    });\n");
      out.write("    \n");
      out.write("    // Bouton retour en haut\n");
      out.write("    const backToTopBtn = document.getElementById('backToTop');\n");
      out.write("    \n");
      out.write("    window.addEventListener('scroll', function() {\n");
      out.write("        if (window.pageYOffset > 300) {\n");
      out.write("            backToTopBtn.style.display = 'block';\n");
      out.write("        } else {\n");
      out.write("            backToTopBtn.style.display = 'none';\n");
      out.write("        }\n");
      out.write("    });\n");
      out.write("    \n");
      out.write("    backToTopBtn.addEventListener('click', function() {\n");
      out.write("        window.scrollTo({\n");
      out.write("            top: 0,\n");
      out.write("            behavior: 'smooth'\n");
      out.write("        });\n");
      out.write("    });\n");
      out.write("    \n");
      out.write("    // Chat support\n");
      out.write("    document.getElementById('chatToggle').addEventListener('click', function() {\n");
      out.write("        // Simulation d'ouverture du chat\n");
      out.write("        alert('Chat support en cours de développement');\n");
      out.write("    });\n");
      out.write("    \n");
      out.write("    // Initialiser les tooltips\n");
      out.write("    document.addEventListener('DOMContentLoaded', function() {\n");
      out.write("        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));\n");
      out.write("        tooltipTriggerList.map(function (tooltipTriggerEl) {\n");
      out.write("            return new bootstrap.Tooltip(tooltipTriggerEl);\n");
      out.write("        });\n");
      out.write("    });\n");
      out.write("    \n");
      out.write("    // Liens externes\n");
      out.write("    document.querySelectorAll('a[href^=\"http\"]').forEach(link => {\n");
      out.write("        link.setAttribute('target', '_blank');\n");
      out.write("        link.setAttribute('rel', 'noopener noreferrer');\n");
      out.write("    });\n");
      out.write("    \n");
      out.write("    // Smooth scroll pour les liens d'ancrage\n");
      out.write("    document.querySelectorAll('a[href^=\"#\"]').forEach(anchor => {\n");
      out.write("        anchor.addEventListener('click', function (e) {\n");
      out.write("            e.preventDefault();\n");
      out.write("            const target = document.querySelector(this.getAttribute('href'));\n");
      out.write("            if (target) {\n");
      out.write("                target.scrollIntoView({\n");
      out.write("                    behavior: 'smooth',\n");
      out.write("                    block: 'start'\n");
      out.write("                });\n");
      out.write("            }\n");
      out.write("        });\n");
      out.write("    });\n");
      out.write("</script>\n");
      out.write("\n");
      out.write("<style>\n");
      out.write("    .text-light-50 {\n");
      out.write("        color: rgba(255, 255, 255, 0.7) !important;\n");
      out.write("    }\n");
      out.write("    \n");
      out.write("    .text-light-50:hover {\n");
      out.write("        color: rgba(255, 255, 255, 1) !important;\n");
      out.write("    }\n");
      out.write("    \n");
      out.write("    .payment-logo {\n");
      out.write("        background: white;\n");
      out.write("        padding: 5px;\n");
      out.write("        border-radius: 4px;\n");
      out.write("        opacity: 0.8;\n");
      out.write("        transition: opacity 0.3s ease;\n");
      out.write("    }\n");
      out.write("    \n");
      out.write("    .payment-logo:hover {\n");
      out.write("        opacity: 1;\n");
      out.write("    }\n");
      out.write("    \n");
      out.write("    .btn-floating {\n");
      out.write("        width: 50px;\n");
      out.write("        height: 50px;\n");
      out.write("        border-radius: 50%;\n");
      out.write("        display: flex;\n");
      out.write("        align-items: center;\n");
      out.write("        justify-content: center;\n");
      out.write("        box-shadow: 0 4px 8px rgba(0,0,0,0.2);\n");
      out.write("        transition: all 0.3s ease;\n");
      out.write("    }\n");
      out.write("    \n");
      out.write("    .btn-floating:hover {\n");
      out.write("        transform: translateY(-2px);\n");
      out.write("        box-shadow: 0 6px 12px rgba(0,0,0,0.3);\n");
      out.write("    }\n");
      out.write("    \n");
      out.write("    .chat-widget .btn-floating {\n");
      out.write("        animation: pulse 2s infinite;\n");
      out.write("    }\n");
      out.write("    \n");
      out.write("    @keyframes pulse {\n");
      out.write("        0% {\n");
      out.write("            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);\n");
      out.write("        }\n");
      out.write("        70% {\n");
      out.write("            box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);\n");
      out.write("        }\n");
      out.write("        100% {\n");
      out.write("            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);\n");
      out.write("        }\n");
      out.write("    }\n");
      out.write("    \n");
      out.write("    footer a {\n");
      out.write("        transition: color 0.3s ease;\n");
      out.write("    }\n");
      out.write("    \n");
      out.write("    footer .social-links a:hover {\n");
      out.write("        transform: translateY(-2px);\n");
      out.write("        transition: transform 0.3s ease;\n");
      out.write("    }\n");
      out.write("</style>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
