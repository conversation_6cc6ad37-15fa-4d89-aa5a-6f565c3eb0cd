-- Script pour ajouter des voyages pour les dates futures
-- Ajout de voyages pour le 30 mai 2025 et les jours suivants

-- Voyages Tunis - Sousse (trajet_id = 3)
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES
-- 30 mai 2025
(3, '2025-05-30 06:00:00', '2025-05-30 08:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-05-30 09:30:00', '2025-05-30 11:30:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-05-30 13:00:00', '2025-05-30 15:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-05-30 16:30:00', '2025-05-30 18:30:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-05-30 19:00:00', '2025-05-30 21:00:00', 180, 18, 54, 108, 'PROGRAMME'),

-- 31 mai 2025
(3, '2025-05-31 06:00:00', '2025-05-31 08:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-05-31 09:30:00', '2025-05-31 11:30:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-05-31 13:00:00', '2025-05-31 15:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-05-31 16:30:00', '2025-05-31 18:30:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-05-31 19:00:00', '2025-05-31 21:00:00', 180, 18, 54, 108, 'PROGRAMME'),

-- 1 juin 2025
(3, '2025-06-01 06:00:00', '2025-06-01 08:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-06-01 09:30:00', '2025-06-01 11:30:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-06-01 13:00:00', '2025-06-01 15:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-06-01 16:30:00', '2025-06-01 18:30:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-06-01 19:00:00', '2025-06-01 21:00:00', 180, 18, 54, 108, 'PROGRAMME');

-- Voyages Sousse - Tunis (trajet_id = 4)
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES
-- 30 mai 2025
(4, '2025-05-30 07:00:00', '2025-05-30 09:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-05-30 10:30:00', '2025-05-30 12:30:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-05-30 14:00:00', '2025-05-30 16:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-05-30 17:30:00', '2025-05-30 19:30:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-05-30 20:00:00', '2025-05-30 22:00:00', 180, 18, 54, 108, 'PROGRAMME'),

-- 31 mai 2025
(4, '2025-05-31 07:00:00', '2025-05-31 09:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-05-31 10:30:00', '2025-05-31 12:30:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-05-31 14:00:00', '2025-05-31 16:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-05-31 17:30:00', '2025-05-31 19:30:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-05-31 20:00:00', '2025-05-31 22:00:00', 180, 18, 54, 108, 'PROGRAMME'),

-- 1 juin 2025
(4, '2025-06-01 07:00:00', '2025-06-01 09:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-06-01 10:30:00', '2025-06-01 12:30:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-06-01 14:00:00', '2025-06-01 16:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-06-01 17:30:00', '2025-06-01 19:30:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-06-01 20:00:00', '2025-06-01 22:00:00', 180, 18, 54, 108, 'PROGRAMME');

-- Autres trajets populaires pour les mêmes dates
-- Tunis - Sfax (trajet_id = 1)
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES
(1, '2025-05-30 06:00:00', '2025-05-30 09:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(1, '2025-05-30 10:00:00', '2025-05-30 13:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(1, '2025-05-30 15:00:00', '2025-05-30 18:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(1, '2025-05-31 06:00:00', '2025-05-31 09:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(1, '2025-05-31 10:00:00', '2025-05-31 13:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(1, '2025-05-31 15:00:00', '2025-05-31 18:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(1, '2025-06-01 06:00:00', '2025-06-01 09:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(1, '2025-06-01 10:00:00', '2025-06-01 13:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(1, '2025-06-01 15:00:00', '2025-06-01 18:00:00', 200, 20, 60, 120, 'PROGRAMME');

-- Sfax - Tunis (trajet_id = 2)
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES
(2, '2025-05-30 07:30:00', '2025-05-30 10:30:00', 200, 20, 60, 120, 'PROGRAMME'),
(2, '2025-05-30 14:00:00', '2025-05-30 17:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(2, '2025-05-31 07:30:00', '2025-05-31 10:30:00', 200, 20, 60, 120, 'PROGRAMME'),
(2, '2025-05-31 14:00:00', '2025-05-31 17:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(2, '2025-06-01 07:30:00', '2025-06-01 10:30:00', 200, 20, 60, 120, 'PROGRAMME'),
(2, '2025-06-01 14:00:00', '2025-06-01 17:00:00', 200, 20, 60, 120, 'PROGRAMME');

SELECT 'Voyages ajoutés avec succès pour les dates futures!' as message;
