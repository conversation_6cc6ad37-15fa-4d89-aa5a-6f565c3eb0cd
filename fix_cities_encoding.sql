-- Script de correction rapide des caractères spéciaux pour les villes tunisiennes
-- À exécuter directement dans MySQL

USE train_ticket_db;

-- Afficher l'état actuel
SELECT 'ÉTAT ACTUEL DES GARES:' as info;
SELECT id, nom, ville, code_gare FROM gares WHERE active = TRUE ORDER BY ville;

-- Corrections des noms de villes avec caractères spéciaux

-- 1. <PERSON><PERSON><PERSON> (remplacer les caractères mal encodés)
UPDATE gares SET 
    ville = 'Gabès',
    nom = 'Gare de Gabès',
    adresse = 'Avenue Farhat Hached, Gabès'
WHERE ville LIKE '%Gab%' OR code_gare LIKE 'GAB%';

-- 2. Corriger Béja
UPDATE gares SET 
    ville = 'Béja',
    nom = 'Gare de Béja',
    adresse = 'Avenue Habib Bourguiba, Béja'
WHERE ville LIKE '%Beja%' OR ville LIKE '%Béja%' OR code_gare LIKE 'BEJ%';

-- 3. Corriger Médenine
UPDATE gares SET 
    ville = 'Médenine',
    nom = 'Gare de Médenine',
    adresse = 'Avenue de la République, Médenine'
WHERE ville LIKE '%Medenine%' OR ville LIKE '%Médenine%' OR code_gare LIKE 'MED%';

-- 4. Corriger Kébili
UPDATE gares SET 
    ville = 'Kébili',
    nom = 'Gare de Kébili',
    adresse = 'Avenue Habib Bourguiba, Kébili'
WHERE ville LIKE '%Kebili%' OR ville LIKE '%Kébili%' OR code_gare LIKE 'KEB%';

-- 5. Corriger Tataouine
UPDATE gares SET 
    ville = 'Tataouine',
    nom = 'Gare de Tataouine',
    adresse = 'Avenue de l\'Indépendance, Tataouine'
WHERE ville LIKE '%Tataouine%' OR code_gare LIKE 'TAT%';

-- 6. Corriger Jendouba
UPDATE gares SET 
    ville = 'Jendouba',
    nom = 'Gare de Jendouba',
    adresse = 'Avenue Habib Bourguiba, Jendouba'
WHERE ville LIKE '%Jendouba%' OR code_gare LIKE 'JEN%';

-- 7. Corriger Le Kef
UPDATE gares SET 
    ville = 'Le Kef',
    nom = 'Gare du Kef',
    adresse = 'Avenue Habib Bourguiba, Le Kef'
WHERE ville LIKE '%Kef%' OR code_gare LIKE 'KEF%';

-- 8. Corriger Siliana
UPDATE gares SET 
    ville = 'Siliana',
    nom = 'Gare de Siliana',
    adresse = 'Avenue de la République, Siliana'
WHERE ville LIKE '%Siliana%' OR code_gare LIKE 'SIL%';

-- 9. Corriger Kasserine
UPDATE gares SET 
    ville = 'Kasserine',
    nom = 'Gare de Kasserine',
    adresse = 'Avenue Habib Bourguiba, Kasserine'
WHERE ville LIKE '%Kasserine%' OR code_gare LIKE 'KAS%';

-- 10. Corriger Sidi Bouzid
UPDATE gares SET 
    ville = 'Sidi Bouzid',
    nom = 'Gare de Sidi Bouzid',
    adresse = 'Avenue de l\'Indépendance, Sidi Bouzid'
WHERE ville LIKE '%Sidi Bouzid%' OR code_gare LIKE 'SBZ%';

-- Corriger les descriptions de trajets
UPDATE trajets SET description = REPLACE(description, 'GabÔö£┬┐s', 'Gabès') WHERE description LIKE '%Gab%';
UPDATE trajets SET description = REPLACE(description, 'Beja', 'Béja') WHERE description LIKE '%Beja%';
UPDATE trajets SET description = REPLACE(description, 'Medenine', 'Médenine') WHERE description LIKE '%Medenine%';
UPDATE trajets SET description = REPLACE(description, 'Kebili', 'Kébili') WHERE description LIKE '%Kebili%';

-- Ajouter des gares manquantes si elles n'existent pas
INSERT IGNORE INTO gares (nom, ville, code_gare, adresse, latitude, longitude, active) VALUES
('Gare de Béja', 'Béja', 'BEJ01', 'Avenue Habib Bourguiba, Béja', 36.7256, 9.1817, TRUE),
('Gare de Médenine', 'Médenine', 'MED01', 'Avenue de la République, Médenine', 33.3549, 10.5055, TRUE),
('Gare de Kébili', 'Kébili', 'KEB01', 'Avenue Habib Bourguiba, Kébili', 33.7044, 8.9690, TRUE);

-- Afficher le résultat final
SELECT 'ÉTAT APRÈS CORRECTION:' as info;
SELECT id, nom, ville, code_gare, adresse FROM gares WHERE active = TRUE ORDER BY ville;

-- Compter le nombre total de gares
SELECT COUNT(*) as total_gares_actives FROM gares WHERE active = TRUE;

SELECT 'Correction des caractères spéciaux terminée!' as message;
