package com.trainticket.controller;

import com.trainticket.dao.GareDAO;
import com.trainticket.dao.TrajetDAO;
import com.trainticket.dao.UtilisateurDAO;
import com.trainticket.dao.impl.GareDAOImpl;
import com.trainticket.dao.impl.TrajetDAOImpl;
import com.trainticket.dao.impl.UtilisateurDAOImpl;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * Contrôleur pour les pages d'authentification
 * Récupère les données dynamiques pour enrichir l'expérience utilisateur
 */
public class AuthController {

    private final GareDAO gareDAO;
    private final TrajetDAO trajetDAO;
    private final UtilisateurDAO utilisateurDAO;

    public AuthController() {
        this.gareDAO = new GareDAOImpl();
        this.trajetDAO = new TrajetDAOImpl();
        this.utilisateurDAO = new UtilisateurDAOImpl();
    }

    /**
     * Charge les données pour la page de connexion
     * @param request la requête HTTP
     */
    public void loadLoginData(HttpServletRequest request) {
        try {
            // Statistiques pour rendre la page plus attrayante
            loadSystemStatistics(request);

            // Messages de bienvenue dynamiques
            loadWelcomeMessages(request);

            System.out.println("Données de connexion chargées");

        } catch (Exception e) {
            System.err.println("Erreur lors du chargement des données de connexion: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Charge les données pour la page d'inscription
     * @param request la requête HTTP
     */
    public void loadRegisterData(HttpServletRequest request) {
        try {
            // Statistiques pour encourager l'inscription
            loadSystemStatistics(request);

            // Avantages de l'inscription
            loadRegistrationBenefits(request);

            System.out.println("Données d'inscription chargées");

        } catch (Exception e) {
            System.err.println("Erreur lors du chargement des données d'inscription: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Charge les statistiques du système
     * @param request la requête HTTP
     */
    private void loadSystemStatistics(HttpServletRequest request) {
        try {
            // Nombre de villes desservies
            List<String> villes = gareDAO.findDistinctCities();
            request.setAttribute("nombreVillesDesservies", villes.size());

            // Nombre de trajets disponibles
            long nombreTrajets = trajetDAO.countActiveTrajets();
            request.setAttribute("nombreTrajetsDisponibles", nombreTrajets);

            // Nombre d'utilisateurs inscrits (approximatif pour la confidentialité)
            long nombreUtilisateurs = utilisateurDAO.count();
            // Arrondir à la centaine la plus proche pour la confidentialité
            long utilisateursArrondis = (nombreUtilisateurs / 100) * 100;
            if (utilisateursArrondis == 0 && nombreUtilisateurs > 0) {
                utilisateursArrondis = 100; // Minimum affiché
            }
            request.setAttribute("nombreUtilisateursInscrits", utilisateursArrondis);

            System.out.println("Statistiques système chargées: " + villes.size() + " villes, " +
                              nombreTrajets + " trajets, " + nombreUtilisateurs + " utilisateurs");

        } catch (Exception e) {
            System.err.println("Erreur lors du chargement des statistiques système: " + e.getMessage());
            // Valeurs par défaut
            request.setAttribute("nombreVillesDesservies", 15);
            request.setAttribute("nombreTrajetsDisponibles", 50);
            request.setAttribute("nombreUtilisateursInscrits", 1000);
        }
    }

    /**
     * Charge les messages de bienvenue
     * @param request la requête HTTP
     */
    private void loadWelcomeMessages(HttpServletRequest request) {
        List<String> messages = List.of(
            "Bienvenue sur Train Ticket - Votre compagnon de voyage en Tunisie",
            "Découvrez la Tunisie en train - Confort, rapidité et sécurité",
            "Réservez vos billets en ligne - Simple, rapide et sécurisé",
            "Voyagez malin, voyagez en train - Économique et écologique"
        );

        // Sélectionner un message aléatoire
        int index = (int) (Math.random() * messages.size());
        request.setAttribute("messageAccueil", messages.get(index));
    }

    /**
     * Charge les avantages de l'inscription
     * @param request la requête HTTP
     */
    private void loadRegistrationBenefits(HttpServletRequest request) {
        List<String> avantages = List.of(
            "Réservation rapide avec vos informations sauvegardées",
            "Historique de vos voyages et billets électroniques",
            "Notifications pour les offres spéciales et promotions",
            "Gestion facile de vos réservations et modifications",
            "Programme de fidélité avec des réductions exclusives"
        );

        request.setAttribute("avantagesInscription", avantages);
    }

    /**
     * Charge les données pour la page de profil utilisateur
     * @param request la requête HTTP
     */
    public void loadProfileData(HttpServletRequest request) {
        try {
            // Les données spécifiques au profil seront chargées par UserServlet
            // Ici on peut ajouter des données générales

            // Conseils de sécurité
            List<String> conseilsSecurite = List.of(
                "Utilisez un mot de passe fort avec au moins 8 caractères",
                "Ne partagez jamais vos identifiants de connexion",
                "Déconnectez-vous toujours après utilisation sur un ordinateur public",
                "Vérifiez régulièrement l'historique de vos connexions"
            );

            request.setAttribute("conseilsSecurite", conseilsSecurite);

            System.out.println("Données de profil chargées");

        } catch (Exception e) {
            System.err.println("Erreur lors du chargement des données de profil: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
