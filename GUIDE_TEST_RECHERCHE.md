# Guide de Test - Recherche de Voyages

## Problème Résolu

Le problème de recherche était causé par une erreur dans les requêtes HQL du `VoyageDAOImpl`. Les requêtes utilisaient des JOINs explicites qui retournaient plusieurs entités au lieu de seulement des objets `Voyage`, ce qui causait l'erreur :

```
Cannot create TypedQuery for query with more than one return using requested result type [com.trainticket.model.Voyage]
```

## Solution Appliquée

Les requêtes HQL ont été modifiées pour utiliser la navigation par propriétés au lieu de JOINs explicites :

**Avant (problématique) :**
```sql
FROM Voyage v JOIN v.trajet t JOIN t.gareDepart gd JOIN t.gareArrivee ga 
WHERE LOWER(gd.ville) = LOWER(?0) AND LOWER(ga.ville) = LOWER(?1)
```

**Après (corrigé) :**
```sql
FROM Voyage v 
WHERE LOWER(v.trajet.gareDepart.ville) = LOWER(?0) 
AND LOWER(v.trajet.gareArrivee.ville) = LOWER(?1)
```

## Tests à Effectuer

### 1. Test via l'Interface Web

1. **Accéder à la page de recherche :**
   - URL : http://localhost:8090/train-ticket/recherche/search

2. **Exemples de recherches à tester :**

   **Test 1 : Tunis → Sfax**
   - Ville de départ : Tunis
   - Ville d'arrivée : Sfax
   - Date : Demain ou une date future
   - Nombre de passagers : 1
   - Résultat attendu : Voyages disponibles affichés

   **Test 2 : Sfax → Gabès**
   - Ville de départ : Sfax
   - Ville d'arrivée : Gabès
   - Date : Demain ou une date future
   - Nombre de passagers : 2
   - Résultat attendu : Voyages disponibles affichés

   **Test 3 : Bizerte → Tunis**
   - Ville de départ : Bizerte
   - Ville d'arrivée : Tunis
   - Date : Demain ou une date future
   - Nombre de passagers : 1
   - Cocher "Voyages directs uniquement"
   - Résultat attendu : Voyages directs affichés

### 2. Test via le Servlet de Test

1. **Accéder au servlet de test :**
   - URL : http://localhost:8090/train-ticket/test/search-fix

2. **Vérifications automatiques :**
   - Test de recherche Tunis → Sfax
   - Test de recherche Sfax → Tunis
   - Test de recherche directe
   - Affichage des détails des voyages trouvés

### 3. Vérification des Logs

Les logs du serveur ne devraient plus afficher l'erreur :
```
Cannot create TypedQuery for query with more than one return
```

## Données de Test Disponibles

Après l'initialisation de la base de données, vous devriez avoir :
- **20 villes** avec leurs gares
- **35 trajets** entre différentes villes
- **Voyages programmés** pour les prochains jours

### Principales routes disponibles :
- Tunis ↔ Sfax (Express)
- Sfax ↔ Gabès
- Bizerte ↔ Tunis
- Béja ↔ Le Kef
- Gabès ↔ Tozeur
- Et bien d'autres...

## Résultats Attendus

✅ **Succès :** La recherche s'exécute sans erreur et affiche les voyages disponibles
✅ **Succès :** Les filtres (voyages directs, nombre de passagers) fonctionnent
✅ **Succès :** Les détails des voyages sont correctement affichés

❌ **Échec :** Message d'erreur "Erreur lors de la recherche de voyages"
❌ **Échec :** Page blanche ou erreur 500

## Dépannage

Si la recherche ne fonctionne toujours pas :

1. **Vérifier que le serveur est démarré :**
   - URL : http://localhost:8090/train-ticket/

2. **Vérifier l'initialisation des données :**
   - URL : http://localhost:8090/train-ticket/simple-init

3. **Consulter les logs du serveur** pour d'éventuelles erreurs

4. **Redémarrer le serveur** si nécessaire

## Fonctionnalités de Recherche

- ✅ Recherche par ville de départ et d'arrivée
- ✅ Filtrage par date de voyage
- ✅ Sélection du nombre de passagers
- ✅ Option "Voyages directs uniquement"
- ✅ Affichage des résultats avec détails complets
- ✅ Gestion des cas "aucun résultat trouvé"
- ✅ Suggestions d'alternatives si disponibles
