<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<footer class="bg-dark text-light py-5 mt-5">
    <div class="container">
        <div class="row">
            <!-- À propos -->
            <div class="col-lg-4 col-md-6 mb-4">
                <h5 class="text-primary mb-3">
                    <i class="fas fa-train"></i> Train Ticket
                </h5>
                <p class="text-light-50">
                    Votre plateforme de réservation de billets de train en ligne. 
                    Voyagez facilement et en toute sécurité à travers le Maroc.
                </p>
                <div class="d-flex gap-3">
                    <a href="#" class="text-light" title="Facebook">
                        <i class="fab fa-facebook-f fa-lg"></i>
                    </a>
                    <a href="#" class="text-light" title="Twitter">
                        <i class="fab fa-twitter fa-lg"></i>
                    </a>
                    <a href="#" class="text-light" title="Instagram">
                        <i class="fab fa-instagram fa-lg"></i>
                    </a>
                    <a href="#" class="text-light" title="LinkedIn">
                        <i class="fab fa-linkedin-in fa-lg"></i>
                    </a>
                </div>
            </div>
            
            <!-- Liens rapides -->
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="text-uppercase mb-3">Liens rapides</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <a href="${pageContext.request.contextPath}/" class="text-light-50 text-decoration-none">
                            <i class="fas fa-home fa-sm"></i> Accueil
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="${pageContext.request.contextPath}/recherche/search" class="text-light-50 text-decoration-none">
                            <i class="fas fa-search fa-sm"></i> Rechercher
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="#" class="text-light-50 text-decoration-none">
                            <i class="fas fa-info-circle fa-sm"></i> À propos
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="#" class="text-light-50 text-decoration-none">
                            <i class="fas fa-phone fa-sm"></i> Contact
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Services -->
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="text-uppercase mb-3">Services</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <a href="#" class="text-light-50 text-decoration-none">
                            <i class="fas fa-ticket-alt fa-sm"></i> Réservation
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="#" class="text-light-50 text-decoration-none">
                            <i class="fas fa-undo fa-sm"></i> Annulation
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="#" class="text-light-50 text-decoration-none">
                            <i class="fas fa-luggage-cart fa-sm"></i> Bagages
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="#" class="text-light-50 text-decoration-none">
                            <i class="fas fa-wheelchair fa-sm"></i> Accessibilité
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Support -->
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="text-uppercase mb-3">Support</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <a href="#" class="text-light-50 text-decoration-none">
                            <i class="fas fa-question-circle fa-sm"></i> FAQ
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="#" class="text-light-50 text-decoration-none">
                            <i class="fas fa-headset fa-sm"></i> Support client
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="#" class="text-light-50 text-decoration-none">
                            <i class="fas fa-file-alt fa-sm"></i> Conditions
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="#" class="text-light-50 text-decoration-none">
                            <i class="fas fa-shield-alt fa-sm"></i> Confidentialité
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Contact -->
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="text-uppercase mb-3">Contact</h6>
                <ul class="list-unstyled">
                    <li class="mb-2 text-light-50">
                        <i class="fas fa-map-marker-alt fa-sm"></i>
                        Casablanca, Maroc
                    </li>
                    <li class="mb-2 text-light-50">
                        <i class="fas fa-phone fa-sm"></i>
                        +212 5 22 XX XX XX
                    </li>
                    <li class="mb-2 text-light-50">
                        <i class="fas fa-envelope fa-sm"></i>
                        <EMAIL>
                    </li>
                    <li class="mb-2 text-light-50">
                        <i class="fas fa-clock fa-sm"></i>
                        24h/24 - 7j/7
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Newsletter -->
        <div class="row mt-4">
            <div class="col-lg-8">
                <h6 class="text-uppercase mb-3">Newsletter</h6>
                <p class="text-light-50 mb-3">
                    Restez informé de nos dernières offres et actualités
                </p>
                <form class="row g-2" id="newsletterForm">
                    <div class="col-md-8">
                        <input type="email" class="form-control" placeholder="Votre adresse email" 
                               id="newsletterEmail" required>
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-paper-plane"></i> S'abonner
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Moyens de paiement -->
            <div class="col-lg-4">
                <h6 class="text-uppercase mb-3">Moyens de paiement</h6>
                <div class="d-flex gap-2 flex-wrap">
                    <img src="${pageContext.request.contextPath}/resources/images/payment/visa.png" 
                         alt="Visa" class="payment-logo" style="height: 30px;">
                    <img src="${pageContext.request.contextPath}/resources/images/payment/mastercard.png" 
                         alt="MasterCard" class="payment-logo" style="height: 30px;">
                    <img src="${pageContext.request.contextPath}/resources/images/payment/paypal.png" 
                         alt="PayPal" class="payment-logo" style="height: 30px;">
                    <span class="badge bg-success">
                        <i class="fas fa-shield-alt"></i> Paiement sécurisé
                    </span>
                </div>
            </div>
        </div>
        
        <hr class="my-4">
        
        <!-- Copyright et liens légaux -->
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0 text-light-50">
                    &copy; <span id="currentYear"></span> Train Ticket. Tous droits réservés.
                </p>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="d-flex justify-content-md-end gap-3 flex-wrap">
                    <a href="#" class="text-light-50 text-decoration-none small">
                        Mentions légales
                    </a>
                    <a href="#" class="text-light-50 text-decoration-none small">
                        Politique de confidentialité
                    </a>
                    <a href="#" class="text-light-50 text-decoration-none small">
                        Conditions d'utilisation
                    </a>
                    <a href="#" class="text-light-50 text-decoration-none small">
                        Plan du site
                    </a>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Bouton retour en haut -->
<button type="button" class="btn btn-primary btn-floating" id="backToTop" 
        style="position: fixed; bottom: 20px; right: 20px; display: none; z-index: 1000;">
    <i class="fas fa-arrow-up"></i>
</button>

<!-- Chat support (optionnel) -->
<div class="chat-widget" style="position: fixed; bottom: 20px; left: 20px; z-index: 1000;">
    <button type="button" class="btn btn-success btn-floating" id="chatToggle" 
            data-bs-toggle="tooltip" data-bs-placement="top" title="Support en ligne">
        <i class="fas fa-comments"></i>
    </button>
</div>

<script>
    // Année courante
    document.getElementById('currentYear').textContent = new Date().getFullYear();
    
    // Newsletter
    document.getElementById('newsletterForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const email = document.getElementById('newsletterEmail').value;
        
        if (email) {
            // Simulation d'inscription à la newsletter
            alert('Merci pour votre inscription à notre newsletter !');
            this.reset();
        }
    });
    
    // Bouton retour en haut
    const backToTopBtn = document.getElementById('backToTop');
    
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.style.display = 'block';
        } else {
            backToTopBtn.style.display = 'none';
        }
    });
    
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // Chat support
    document.getElementById('chatToggle').addEventListener('click', function() {
        // Simulation d'ouverture du chat
        alert('Chat support en cours de développement');
    });
    
    // Initialiser les tooltips
    document.addEventListener('DOMContentLoaded', function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
    
    // Liens externes
    document.querySelectorAll('a[href^="http"]').forEach(link => {
        link.setAttribute('target', '_blank');
        link.setAttribute('rel', 'noopener noreferrer');
    });
    
    // Smooth scroll pour les liens d'ancrage
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
</script>

<style>
    .text-light-50 {
        color: rgba(255, 255, 255, 0.7) !important;
    }
    
    .text-light-50:hover {
        color: rgba(255, 255, 255, 1) !important;
    }
    
    .payment-logo {
        background: white;
        padding: 5px;
        border-radius: 4px;
        opacity: 0.8;
        transition: opacity 0.3s ease;
    }
    
    .payment-logo:hover {
        opacity: 1;
    }
    
    .btn-floating {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
    }
    
    .btn-floating:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.3);
    }
    
    .chat-widget .btn-floating {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
        }
    }
    
    footer a {
        transition: color 0.3s ease;
    }
    
    footer .social-links a:hover {
        transform: translateY(-2px);
        transition: transform 0.3s ease;
    }
</style>
