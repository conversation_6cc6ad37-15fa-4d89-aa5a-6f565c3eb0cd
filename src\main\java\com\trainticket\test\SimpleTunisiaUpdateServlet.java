package com.trainticket.test;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;

@WebServlet("/simple-tunisia-update")
public class SimpleTunisiaUpdateServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();

        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>🇹🇳 Mise à jour données tunisiennes</title>");
        out.println("<meta charset='UTF-8'>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }");
        out.println(".container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }");
        out.println(".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        out.println(".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        out.println(".info { color: #0066cc; background: #cce7ff; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        out.println("h1 { color: #333; text-align: center; }");
        out.println("h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }");
        out.println(".btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        out.println("<div class='container'>");
        out.println("<h1>🇹🇳 Mise à jour des données tunisiennes</h1>");

        Connection conn = null;
        Statement stmt = null;

        try {
            // Connexion à la base de données - Essayer plusieurs configurations
            String url = "************************************************************************************************************************************************";
            String username = "root";
            String password = "";

            Class.forName("com.mysql.cj.jdbc.Driver");

            // Essayer d'abord sans mot de passe
            try {
                conn = DriverManager.getConnection(url, username, password);
                out.println("<div class='success'>✅ Connexion réussie sans mot de passe</div>");
            } catch (Exception e1) {
                out.println("<div class='info'>⚠️ Connexion sans mot de passe échouée, essai avec mot de passe...</div>");
                // Essayer avec un mot de passe vide explicite
                try {
                    password = "";
                    conn = DriverManager.getConnection(url + "&allowEmptyPasswords=true", username, password);
                    out.println("<div class='success'>✅ Connexion réussie avec allowEmptyPasswords</div>");
                } catch (Exception e2) {
                    // Essayer avec le mot de passe par défaut
                    password = "root";
                    try {
                        conn = DriverManager.getConnection(url, username, password);
                        out.println("<div class='success'>✅ Connexion réussie avec mot de passe 'root'</div>");
                    } catch (Exception e3) {
                        throw new Exception("Impossible de se connecter à MySQL. Erreurs: " + e1.getMessage() + " | " + e2.getMessage() + " | " + e3.getMessage());
                    }
                }
            }
            stmt = conn.createStatement();

            out.println("<h2>🔍 Diagnostic des données actuelles...</h2>");

            // Voir les données actuelles
            java.sql.ResultSet rs = stmt.executeQuery("SELECT * FROM gares");
            out.println("<div class='info'><h4>Gares actuelles dans la base:</h4><ul>");
            while (rs.next()) {
                out.println("<li>ID: " + rs.getInt("id") + " - " + rs.getString("nom") + " (" + rs.getString("ville") + ")</li>");
            }
            out.println("</ul></div>");
            rs.close();

            out.println("<h2>🗑️ Suppression FORCÉE des données existantes...</h2>");

            // Désactiver les contraintes temporairement
            stmt.executeUpdate("SET FOREIGN_KEY_CHECKS = 0");

            // Supprimer les données existantes avec TRUNCATE pour forcer
            stmt.executeUpdate("TRUNCATE TABLE billets");
            out.println("<div class='info'>✅ Table billets vidée</div>");

            stmt.executeUpdate("TRUNCATE TABLE voyages");
            out.println("<div class='info'>✅ Table voyages vidée</div>");

            stmt.executeUpdate("TRUNCATE TABLE trajets");
            out.println("<div class='info'>✅ Table trajets vidée</div>");

            stmt.executeUpdate("TRUNCATE TABLE gares");
            out.println("<div class='info'>✅ Table gares vidée</div>");

            // Réactiver les contraintes
            stmt.executeUpdate("SET FOREIGN_KEY_CHECKS = 1");

            out.println("<h2>🚉 Insertion des gares tunisiennes...</h2>");

            // Insertion des gares tunisiennes
            String insertGares = "INSERT INTO gares (nom, ville, code_gare, adresse, latitude, longitude, active) VALUES " +
                "('Gare Centrale de Tunis', 'Tunis', 'TUN', 'Place Barcelone, Tunis', 36.8008, 10.1817, TRUE), " +
                "('Gare de Sfax', 'Sfax', 'SFX', 'Avenue Habib Bourguiba, Sfax', 34.7406, 10.7603, TRUE), " +
                "('Gare de Sousse', 'Sousse', 'SOU', 'Boulevard Hassouna Ayachi, Sousse', 35.8256, 10.6369, TRUE), " +
                "('Gare de Bizerte', 'Bizerte', 'BIZ', 'Avenue Habib Bourguiba, Bizerte', 37.2744, 9.8739, TRUE), " +
                "('Gare de Gabès', 'Gabès', 'GAB', 'Avenue Farhat Hached, Gabès', 33.8815, 10.0982, TRUE), " +
                "('Gare de Monastir', 'Monastir', 'MON', 'Avenue de la République, Monastir', 35.7643, 10.8113, TRUE), " +
                "('Gare de Kairouan', 'Kairouan', 'KAI', 'Avenue de la République, Kairouan', 35.6781, 10.0963, TRUE), " +
                "('Gare de Mahdia', 'Mahdia', 'MAH', 'Avenue 7 Novembre, Mahdia', 35.5047, 11.0622, TRUE), " +
                "('Gare de Nabeul', 'Nabeul', 'NAB', 'Avenue Habib Bourguiba, Nabeul', 36.4561, 10.7376, TRUE), " +
                "('Gare de Gafsa', 'Gafsa', 'GAF', 'Avenue Habib Bourguiba, Gafsa', 34.4250, 8.7842, TRUE)";

            int garesInserted = stmt.executeUpdate(insertGares);
            out.println("<div class='success'>✅ Gares tunisiennes ajoutées: " + garesInserted + "</div>");

            out.println("<h2>🚄 Insertion des trajets tunisiens...</h2>");

            // Insertion des trajets tunisiens
            String insertTrajets = "INSERT INTO trajets (numero_trajet, gare_depart_id, gare_arrivee_id, duree_minutes, prix_base, capacite_total, type, description) VALUES " +
                "('TN001', 1, 2, 180, 25.50, 200, 'EXPRESS', 'Express Tunis - Sfax'), " +
                "('TN002', 2, 1, 180, 25.50, 200, 'EXPRESS', 'Express Sfax - Tunis'), " +
                "('TN003', 1, 3, 120, 18.00, 180, 'DIRECT', 'Train direct Tunis - Sousse'), " +
                "('TN004', 3, 1, 120, 18.00, 180, 'DIRECT', 'Train direct Sousse - Tunis'), " +
                "('TN005', 1, 5, 240, 32.00, 160, 'NORMAL', 'Train Tunis - Gabès'), " +
                "('TN006', 5, 1, 240, 32.00, 160, 'NORMAL', 'Train Gabès - Tunis'), " +
                "('TN101', 3, 2, 90, 15.00, 150, 'NORMAL', 'Train Sousse - Sfax'), " +
                "('TN102', 2, 3, 90, 15.00, 150, 'NORMAL', 'Train Sfax - Sousse'), " +
                "('TN103', 3, 6, 45, 8.50, 140, 'NORMAL', 'Train Sousse - Monastir'), " +
                "('TN104', 6, 3, 45, 8.50, 140, 'NORMAL', 'Train Monastir - Sousse'), " +
                "('TN201', 1, 4, 90, 14.00, 160, 'NORMAL', 'Train Tunis - Bizerte'), " +
                "('TN202', 4, 1, 90, 14.00, 160, 'NORMAL', 'Train Bizerte - Tunis'), " +
                "('TN203', 1, 9, 75, 12.50, 140, 'DIRECT', 'Train direct Tunis - Nabeul'), " +
                "('TN204', 9, 1, 75, 12.50, 140, 'DIRECT', 'Train direct Nabeul - Tunis'), " +
                "('TN301', 1, 7, 150, 22.00, 140, 'NORMAL', 'Train Tunis - Kairouan'), " +
                "('TN302', 7, 1, 150, 22.00, 140, 'NORMAL', 'Train Kairouan - Tunis')";

            int trajetsInserted = stmt.executeUpdate(insertTrajets);
            out.println("<div class='success'>✅ Trajets tunisiens ajoutés: " + trajetsInserted + "</div>");

            out.println("<h2>🕐 Insertion des voyages...</h2>");

            // Insertion de quelques voyages
            String insertVoyages = "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES " +
                "(1, '2025-05-27 06:00:00', '2025-05-27 09:00:00', 200, 20, 60, 120, 'PROGRAMME'), " +
                "(1, '2025-05-27 10:00:00', '2025-05-27 13:00:00', 200, 20, 60, 120, 'PROGRAMME'), " +
                "(1, '2025-05-27 15:00:00', '2025-05-27 18:00:00', 200, 20, 60, 120, 'PROGRAMME'), " +
                "(2, '2025-05-27 07:30:00', '2025-05-27 10:30:00', 200, 20, 60, 120, 'PROGRAMME'), " +
                "(2, '2025-05-27 14:00:00', '2025-05-27 17:00:00', 200, 20, 60, 120, 'PROGRAMME'), " +
                "(3, '2025-05-27 08:00:00', '2025-05-27 10:00:00', 180, 18, 54, 108, 'PROGRAMME'), " +
                "(3, '2025-05-27 12:00:00', '2025-05-27 14:00:00', 180, 18, 54, 108, 'PROGRAMME'), " +
                "(4, '2025-05-27 09:15:00', '2025-05-27 11:15:00', 180, 18, 54, 108, 'PROGRAMME'), " +
                "(4, '2025-05-27 13:45:00', '2025-05-27 15:45:00', 180, 18, 54, 108, 'PROGRAMME'), " +
                "(11, '2025-05-27 07:00:00', '2025-05-27 08:30:00', 160, 16, 48, 96, 'PROGRAMME')";

            int voyagesInserted = stmt.executeUpdate(insertVoyages);
            out.println("<div class='success'>✅ Voyages ajoutés: " + voyagesInserted + "</div>");

            out.println("<h2>🔍 Vérification finale des données...</h2>");

            // Vérifier que les données tunisiennes sont bien insérées
            java.sql.ResultSet rsVerif = stmt.executeQuery("SELECT * FROM gares ORDER BY id");
            out.println("<div class='success'><h4>✅ Gares tunisiennes dans la base:</h4><ul>");
            while (rsVerif.next()) {
                out.println("<li><strong>" + rsVerif.getString("ville") + "</strong> - " + rsVerif.getString("nom") + " (" + rsVerif.getString("code_gare") + ")</li>");
            }
            out.println("</ul></div>");
            rsVerif.close();

            // Compter les trajets
            java.sql.ResultSet rsCount = stmt.executeQuery("SELECT COUNT(*) as total FROM trajets");
            int totalTrajets = 0;
            if (rsCount.next()) {
                totalTrajets = rsCount.getInt("total");
            }
            rsCount.close();

            out.println("<h2>🎉 Mise à jour terminée avec succès!</h2>");
            out.println("<div class='success'>");
            out.println("<h3>📊 Résumé des données tunisiennes:</h3>");
            out.println("<ul>");
            out.println("<li><strong>10 gares</strong> dans les principales villes tunisiennes</li>");
            out.println("<li><strong>" + totalTrajets + " trajets</strong> couvrant tout le territoire</li>");
            out.println("<li><strong>Voyages</strong> programmés pour aujourd'hui</li>");
            out.println("<li>Prix en <strong>Dinars Tunisiens (TND)</strong></li>");
            out.println("<li>Coordonnées GPS réelles des gares</li>");
            out.println("</ul>");
            out.println("</div>");

            out.println("<h3>🗺️ Principales liaisons:</h3>");
            out.println("<div class='info'>");
            out.println("<ul>");
            out.println("<li><strong>Tunis ↔ Sfax</strong> (Express, 3h, 25.50 TND)</li>");
            out.println("<li><strong>Tunis ↔ Sousse</strong> (Direct, 2h, 18.00 TND)</li>");
            out.println("<li><strong>Tunis ↔ Bizerte</strong> (1h30, 14.00 TND)</li>");
            out.println("<li><strong>Tunis ↔ Nabeul</strong> (Direct, 1h15, 12.50 TND)</li>");
            out.println("<li><strong>Sousse ↔ Monastir</strong> (45min, 8.50 TND)</li>");
            out.println("</ul>");
            out.println("</div>");

        } catch (Exception e) {
            out.println("<div class='error'>");
            out.println("<h2>❌ Erreur lors de la mise à jour</h2>");
            out.println("<p>Erreur: " + e.getMessage() + "</p>");
            out.println("</div>");
            e.printStackTrace();
        } finally {
            try {
                if (stmt != null) stmt.close();
                if (conn != null) conn.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        out.println("<p><a href='/train-ticket/' class='btn'>← Retour à l'application</a></p>");
        out.println("</div>");
        out.println("</body>");
        out.println("</html>");
    }
}
