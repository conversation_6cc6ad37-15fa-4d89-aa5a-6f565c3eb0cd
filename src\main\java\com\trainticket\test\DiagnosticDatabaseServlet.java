package com.trainticket.test;

import com.trainticket.util.HibernateUtil;
import org.hibernate.Session;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

@WebServlet("/test/diagnostic-db")
public class DiagnosticDatabaseServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("text/html; charset=UTF-8");
        PrintWriter out = response.getWriter();

        out.println("<!DOCTYPE html>");
        out.println("<html><head><title>Diagnostic Base de Données</title>");
        out.println("<meta charset='UTF-8'>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 20px; }");
        out.println("table { border-collapse: collapse; width: 100%; margin: 10px 0; }");
        out.println("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
        out.println("th { background-color: #f2f2f2; }");
        out.println(".section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }");
        out.println(".success { color: green; }");
        out.println(".error { color: red; }");
        out.println("</style>");
        out.println("</head><body>");
        out.println("<h1>Diagnostic Base de Données</h1>");

        try (Session session = HibernateUtil.getSessionFactory().openSession()) {

            // 1. Vérifier les gares
            out.println("<div class='section'>");
            out.println("<h2>1. Gares</h2>");

            String garesQuery = "SELECT id, nom, ville, code_gare, active FROM gares ORDER BY ville, nom";
            List<Object[]> gares = session.createNativeQuery(garesQuery).getResultList();

            out.println("<p class='success'>✅ " + gares.size() + " gares trouvées</p>");
            out.println("<table>");
            out.println("<tr><th>ID</th><th>Nom</th><th>Ville</th><th>Code</th><th>Active</th></tr>");

            for (Object[] gare : gares) {
                out.println("<tr>");
                out.println("<td>" + gare[0] + "</td>");
                out.println("<td>" + gare[1] + "</td>");
                out.println("<td>" + gare[2] + "</td>");
                out.println("<td>" + gare[3] + "</td>");
                out.println("<td>" + gare[4] + "</td>");
                out.println("</tr>");
            }
            out.println("</table>");
            out.println("</div>");

            // 2. Vérifier les trajets
            out.println("<div class='section'>");
            out.println("<h2>2. Trajets</h2>");

            String trajetsQuery = "SELECT t.id, t.numero_trajet, gd.ville as ville_depart, ga.ville as ville_arrivee, " +
                                 "t.duree_minutes, t.prix_base, t.type, t.active " +
                                 "FROM trajets t " +
                                 "JOIN gares gd ON t.gare_depart_id = gd.id " +
                                 "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
                                 "ORDER BY gd.ville, ga.ville";

            List<Object[]> trajets = session.createNativeQuery(trajetsQuery).getResultList();

            out.println("<p class='success'>✅ " + trajets.size() + " trajets trouvés</p>");
            out.println("<table>");
            out.println("<tr><th>ID</th><th>Numéro</th><th>Départ</th><th>Arrivée</th><th>Durée</th><th>Prix</th><th>Type</th><th>Active</th></tr>");

            for (Object[] trajet : trajets) {
                out.println("<tr>");
                out.println("<td>" + trajet[0] + "</td>");
                out.println("<td>" + trajet[1] + "</td>");
                out.println("<td>" + trajet[2] + "</td>");
                out.println("<td>" + trajet[3] + "</td>");
                out.println("<td>" + trajet[4] + " min</td>");
                out.println("<td>" + trajet[5] + " TND</td>");
                out.println("<td>" + trajet[6] + "</td>");
                out.println("<td>" + trajet[7] + "</td>");
                out.println("</tr>");
            }
            out.println("</table>");
            out.println("</div>");

            // 3. Vérifier les voyages
            out.println("<div class='section'>");
            out.println("<h2>3. Voyages</h2>");

            String voyagesQuery = "SELECT v.id, t.numero_trajet, gd.ville as ville_depart, ga.ville as ville_arrivee, " +
                                 "v.date_heure_depart, v.date_heure_arrivee, v.places_disponibles, v.statut " +
                                 "FROM voyages v " +
                                 "JOIN trajets t ON v.trajet_id = t.id " +
                                 "JOIN gares gd ON t.gare_depart_id = gd.id " +
                                 "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
                                 "ORDER BY v.date_heure_depart " +
                                 "LIMIT 20";

            @SuppressWarnings("unchecked")
            List<Object[]> voyages = session.createNativeQuery(voyagesQuery).getResultList();

            // Compter le total
            String countQuery = "SELECT COUNT(*) FROM voyages";
            Object countResult = session.createNativeQuery(countQuery).getSingleResult();
            Long totalVoyages = ((Number) countResult).longValue();

            out.println("<p class='success'>✅ " + totalVoyages + " voyages au total (affichage des 20 premiers)</p>");
            out.println("<table>");
            out.println("<tr><th>ID</th><th>Trajet</th><th>Départ</th><th>Arrivée</th><th>Date Départ</th><th>Date Arrivée</th><th>Places</th><th>Statut</th></tr>");

            for (Object[] voyage : voyages) {
                out.println("<tr>");
                out.println("<td>" + voyage[0] + "</td>");
                out.println("<td>" + voyage[1] + "</td>");
                out.println("<td>" + voyage[2] + "</td>");
                out.println("<td>" + voyage[3] + "</td>");
                out.println("<td>" + voyage[4] + "</td>");
                out.println("<td>" + voyage[5] + "</td>");
                out.println("<td>" + voyage[6] + "</td>");
                out.println("<td>" + voyage[7] + "</td>");
                out.println("</tr>");
            }
            out.println("</table>");
            out.println("</div>");

            // 4. Test de recherche directe SQL
            out.println("<div class='section'>");
            out.println("<h2>4. Test de Recherche SQL Directe</h2>");

            String searchQuery = "SELECT v.id, t.numero_trajet, gd.ville as ville_depart, ga.ville as ville_arrivee, " +
                                "v.date_heure_depart, v.places_disponibles " +
                                "FROM voyages v " +
                                "JOIN trajets t ON v.trajet_id = t.id " +
                                "JOIN gares gd ON t.gare_depart_id = gd.id " +
                                "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
                                "WHERE LOWER(TRIM(gd.ville)) = LOWER(TRIM('Tunis')) " +
                                "AND LOWER(TRIM(ga.ville)) = LOWER(TRIM('Sousse')) " +
                                "AND v.places_disponibles > 0 " +
                                "AND v.statut = 'PROGRAMME' " +
                                "AND t.active = true " +
                                "AND gd.active = true " +
                                "AND ga.active = true " +
                                "ORDER BY v.date_heure_depart";

            @SuppressWarnings("unchecked")
            List<Object[]> resultatsRecherche = session.createNativeQuery(searchQuery).getResultList();

            out.println("<p><strong>Recherche Tunis → Sousse:</strong></p>");
            if (resultatsRecherche.isEmpty()) {
                out.println("<p class='error'>❌ Aucun voyage trouvé</p>");
            } else {
                out.println("<p class='success'>✅ " + resultatsRecherche.size() + " voyage(s) trouvé(s)</p>");
                out.println("<table>");
                out.println("<tr><th>ID</th><th>Trajet</th><th>Départ</th><th>Arrivée</th><th>Date Départ</th><th>Places</th></tr>");

                for (Object[] resultat : resultatsRecherche) {
                    out.println("<tr>");
                    out.println("<td>" + resultat[0] + "</td>");
                    out.println("<td>" + resultat[1] + "</td>");
                    out.println("<td>" + resultat[2] + "</td>");
                    out.println("<td>" + resultat[3] + "</td>");
                    out.println("<td>" + resultat[4] + "</td>");
                    out.println("<td>" + resultat[5] + "</td>");
                    out.println("</tr>");
                }
                out.println("</table>");
            }
            out.println("</div>");

        } catch (Exception e) {
            out.println("<div class='section'>");
            out.println("<h2 class='error'>Erreur</h2>");
            out.println("<p class='error'>❌ " + e.getMessage() + "</p>");
            out.println("<pre>");
            e.printStackTrace(out);
            out.println("</pre>");
            out.println("</div>");
        }

        out.println("<hr>");
        out.println("<p><a href='recherche-avancee'>Test de recherche avancée</a></p>");
        out.println("<p><a href='../recherche/search'>Page de recherche</a></p>");
        out.println("</body></html>");
    }
}
