-- Script pour générer plus de voyages pour les 15 prochains jours
USE train_ticket_db;

-- Générer des voyages pour les jours 3 à 15
-- Trajets principaux avec horaires variés

-- Boucle pour générer des voyages pour les jours 3 à 15
-- TUNIS - SOUSSE (trajet 1) - 5 voyages par jour
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) 
SELECT 
    1 as trajet_id,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', heure) as date_heure_depart,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', ADDTIME(heure, '02:00:00')) as date_heure_arrivee,
    200 as places_disponibles,
    20 as places_premiere_classe,
    60 as places_deuxieme_classe,
    120 as places_economique,
    'PROGRAMME' as statut
FROM 
    (SELECT 3 as day_offset UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION 
     SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION 
     SELECT 13 UNION SELECT 14 UNION SELECT 15) as days
CROSS JOIN
    (SELECT '06:00:00' as heure UNION SELECT '09:30:00' UNION SELECT '13:00:00' UNION SELECT '16:30:00' UNION SELECT '19:00:00') as horaires;

-- SOUSSE - TUNIS (trajet 11) - 5 voyages par jour
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) 
SELECT 
    11 as trajet_id,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', heure) as date_heure_depart,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', ADDTIME(heure, '02:00:00')) as date_heure_arrivee,
    200 as places_disponibles,
    20 as places_premiere_classe,
    60 as places_deuxieme_classe,
    120 as places_economique,
    'PROGRAMME' as statut
FROM 
    (SELECT 3 as day_offset UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION 
     SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION 
     SELECT 13 UNION SELECT 14 UNION SELECT 15) as days
CROSS JOIN
    (SELECT '07:00:00' as heure UNION SELECT '10:30:00' UNION SELECT '14:00:00' UNION SELECT '17:30:00' UNION SELECT '20:00:00') as horaires;

-- TUNIS - SFAX (trajet 2) - 3 voyages par jour
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) 
SELECT 
    2 as trajet_id,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', heure) as date_heure_depart,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', ADDTIME(heure, '03:00:00')) as date_heure_arrivee,
    200 as places_disponibles,
    20 as places_premiere_classe,
    60 as places_deuxieme_classe,
    120 as places_economique,
    'PROGRAMME' as statut
FROM 
    (SELECT 3 as day_offset UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION 
     SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION 
     SELECT 13 UNION SELECT 14 UNION SELECT 15) as days
CROSS JOIN
    (SELECT '08:00:00' as heure UNION SELECT '13:00:00' UNION SELECT '17:00:00') as horaires;

-- SFAX - TUNIS (trajet 15) - 3 voyages par jour
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) 
SELECT 
    15 as trajet_id,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', heure) as date_heure_depart,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', ADDTIME(heure, '03:00:00')) as date_heure_arrivee,
    200 as places_disponibles,
    20 as places_premiere_classe,
    60 as places_deuxieme_classe,
    120 as places_economique,
    'PROGRAMME' as statut
FROM 
    (SELECT 3 as day_offset UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION 
     SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION 
     SELECT 13 UNION SELECT 14 UNION SELECT 15) as days
CROSS JOIN
    (SELECT '09:00:00' as heure UNION SELECT '14:00:00' UNION SELECT '18:00:00') as horaires;

-- SOUSSE - SFAX (trajet 8) - 2 voyages par jour
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) 
SELECT 
    8 as trajet_id,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', heure) as date_heure_depart,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', ADDTIME(heure, '01:30:00')) as date_heure_arrivee,
    180 as places_disponibles,
    18 as places_premiere_classe,
    54 as places_deuxieme_classe,
    108 as places_economique,
    'PROGRAMME' as statut
FROM 
    (SELECT 3 as day_offset UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION 
     SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION 
     SELECT 13 UNION SELECT 14 UNION SELECT 15) as days
CROSS JOIN
    (SELECT '10:00:00' as heure UNION SELECT '15:30:00') as horaires;

-- SFAX - SOUSSE (trajet 16) - 2 voyages par jour
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) 
SELECT 
    16 as trajet_id,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', heure) as date_heure_depart,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', ADDTIME(heure, '01:30:00')) as date_heure_arrivee,
    180 as places_disponibles,
    18 as places_premiere_classe,
    54 as places_deuxieme_classe,
    108 as places_economique,
    'PROGRAMME' as statut
FROM 
    (SELECT 3 as day_offset UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION 
     SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION 
     SELECT 13 UNION SELECT 14 UNION SELECT 15) as days
CROSS JOIN
    (SELECT '12:00:00' as heure UNION SELECT '17:30:00') as horaires;

-- TUNIS - BIZERTE (trajet 4) - 3 voyages par jour
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) 
SELECT 
    4 as trajet_id,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', heure) as date_heure_depart,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', ADDTIME(heure, '01:30:00')) as date_heure_arrivee,
    150 as places_disponibles,
    15 as places_premiere_classe,
    45 as places_deuxieme_classe,
    90 as places_economique,
    'PROGRAMME' as statut
FROM 
    (SELECT 3 as day_offset UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION 
     SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION 
     SELECT 13 UNION SELECT 14 UNION SELECT 15) as days
CROSS JOIN
    (SELECT '08:30:00' as heure UNION SELECT '14:00:00' UNION SELECT '18:00:00') as horaires;

-- BIZERTE - TUNIS (trajet 33) - 3 voyages par jour
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) 
SELECT 
    33 as trajet_id,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', heure) as date_heure_depart,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', ADDTIME(heure, '01:30:00')) as date_heure_arrivee,
    150 as places_disponibles,
    15 as places_premiere_classe,
    45 as places_deuxieme_classe,
    90 as places_economique,
    'PROGRAMME' as statut
FROM 
    (SELECT 3 as day_offset UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION 
     SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION 
     SELECT 13 UNION SELECT 14 UNION SELECT 15) as days
CROSS JOIN
    (SELECT '09:30:00' as heure UNION SELECT '15:00:00' UNION SELECT '19:30:00') as horaires;

-- TUNIS - GABÈS (trajet 3) - 1 voyage par jour
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) 
SELECT 
    3 as trajet_id,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' 07:30:00') as date_heure_depart,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' 12:30:00') as date_heure_arrivee,
    180 as places_disponibles,
    18 as places_premiere_classe,
    54 as places_deuxieme_classe,
    108 as places_economique,
    'PROGRAMME' as statut
FROM 
    (SELECT 3 as day_offset UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION 
     SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION 
     SELECT 13 UNION SELECT 14 UNION SELECT 15) as days;

-- GABÈS - TUNIS (trajet 19) - 1 voyage par jour
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) 
SELECT 
    19 as trajet_id,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' 14:00:00') as date_heure_depart,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' 19:00:00') as date_heure_arrivee,
    180 as places_disponibles,
    18 as places_premiere_classe,
    54 as places_deuxieme_classe,
    108 as places_economique,
    'PROGRAMME' as statut
FROM 
    (SELECT 3 as day_offset UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION 
     SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION 
     SELECT 13 UNION SELECT 14 UNION SELECT 15) as days;

-- SOUSSE - MONASTIR (trajet 9) - 4 voyages par jour
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) 
SELECT 
    9 as trajet_id,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', heure) as date_heure_depart,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', ADDTIME(heure, '00:45:00')) as date_heure_arrivee,
    150 as places_disponibles,
    15 as places_premiere_classe,
    45 as places_deuxieme_classe,
    90 as places_economique,
    'PROGRAMME' as statut
FROM 
    (SELECT 3 as day_offset UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION 
     SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION 
     SELECT 13 UNION SELECT 14 UNION SELECT 15) as days
CROSS JOIN
    (SELECT '09:00:00' as heure UNION SELECT '12:00:00' UNION SELECT '16:00:00' UNION SELECT '19:00:00') as horaires;

-- SOUSSE - MAHDIA (trajet 10) - 2 voyages par jour
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) 
SELECT 
    10 as trajet_id,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', heure) as date_heure_depart,
    CONCAT(DATE_ADD(CURDATE(), INTERVAL day_offset DAY), ' ', ADDTIME(heure, '01:00:00')) as date_heure_arrivee,
    150 as places_disponibles,
    15 as places_premiere_classe,
    45 as places_deuxieme_classe,
    90 as places_economique,
    'PROGRAMME' as statut
FROM 
    (SELECT 3 as day_offset UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION 
     SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION 
     SELECT 13 UNION SELECT 14 UNION SELECT 15) as days
CROSS JOIN
    (SELECT '11:00:00' as heure UNION SELECT '17:00:00') as horaires;

-- Afficher les statistiques finales
SELECT 'Génération de voyages supplémentaires terminée!' as message;
SELECT COUNT(*) as total_voyages FROM voyages;
SELECT DATE(date_heure_depart) as date_voyage, COUNT(*) as nombre_voyages 
FROM voyages 
WHERE DATE(date_heure_depart) >= CURDATE() 
GROUP BY DATE(date_heure_depart) 
ORDER BY date_voyage 
LIMIT 10;
