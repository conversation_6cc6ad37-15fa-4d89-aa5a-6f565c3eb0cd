@echo off
echo ========================================
echo   DIAGNOSTIC DU PROJET
echo ========================================
echo.

echo 🔍 Verification de Java...
java -version
echo.

echo 🔍 Verification de JAVA_HOME...
if "%JAVA_HOME%"=="" (
    echo ❌ JAVA_HOME n'est pas defini
    echo 🔧 Tentative de detection automatique...
    for /f "tokens=2*" %%i in ('reg query "HKEY_LOCAL_MACHINE\SOFTWARE\JavaSoft\Java Development Kit" /s /v JavaHome 2^>nul ^| find "JavaHome"') do set JAVA_HOME=%%j
    if not "%JAVA_HOME%"=="" (
        echo ✅ JAVA_HOME detecte: %JAVA_HOME%
    ) else (
        echo ❌ Impossible de detecter JAVA_HOME automatiquement
    )
) else (
    echo ✅ JAVA_HOME: %JAVA_HOME%
)
echo.

echo 🔍 Verification des fichiers Maven Wrapper...
if exist "mvnw.cmd" (
    echo ✅ mvnw.cmd trouve
) else (
    echo ❌ mvnw.cmd manquant
)

if exist ".mvn\wrapper\maven-wrapper.properties" (
    echo ✅ maven-wrapper.properties trouve
) else (
    echo ❌ maven-wrapper.properties manquant
)

if exist ".mvn\wrapper\maven-wrapper.jar" (
    echo ✅ maven-wrapper.jar trouve
) else (
    echo ❌ maven-wrapper.jar manquant (sera telecharge automatiquement)
)
echo.

echo 🔍 Test d'execution de mvnw.cmd...
echo.
mvnw.cmd --version

echo.
echo ========================================
echo   FIN DU DIAGNOSTIC
echo ========================================
pause
