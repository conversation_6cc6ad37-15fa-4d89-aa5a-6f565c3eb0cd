<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demande d'Annulation - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="${pageContext.request.contextPath}/resources/css/style.css" rel="stylesheet">
</head>
<body>
    <%@ include file="../common/navbar.jsp" %>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h4 class="mb-0">
                            <i class="fas fa-exclamation-triangle"></i> 
                            Demande d'Annulation de Billet
                        </h4>
                    </div>
                    <div class="card-body">
                        <c:if test="${not empty error}">
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle"></i> ${error}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        </c:if>

                        <!-- Informations du billet -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Informations du billet à annuler</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Numéro de billet:</strong> ${billet.numeroBillet}</p>
                                        <p><strong>Passager:</strong> ${billet.utilisateur.nomComplet}</p>
                                        <p><strong>Classe:</strong> ${billet.classeLibelle}</p>
                                        <p><strong>Prix payé:</strong> 
                                           <span class="text-success fw-bold">
                                               <fmt:formatNumber value="${billet.prix}" type="currency" currencySymbol="DH"/>
                                           </span>
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Trajet:</strong> 
                                           ${billet.voyage.trajet.gareDepart.ville} 
                                           <i class="fas fa-arrow-right mx-2"></i> 
                                           ${billet.voyage.trajet.gareArrivee.ville}
                                        </p>
                                        <p><strong>Date de départ:</strong> 
                                           <fmt:formatDate value="${billet.voyage.dateHeureDepart}" 
                                                         pattern="dd/MM/yyyy HH:mm"/>
                                        </p>
                                        <p><strong>Date d'arrivée:</strong> 
                                           <fmt:formatDate value="${billet.voyage.dateHeureArrivee}" 
                                                         pattern="dd/MM/yyyy HH:mm"/>
                                        </p>
                                        <p><strong>Statut:</strong> 
                                           <span class="badge bg-primary">${billet.statut}</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Conditions d'annulation -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Conditions d'annulation</h6>
                            <ul class="mb-0">
                                <li>Annulation gratuite : plus de 48h avant le départ</li>
                                <li>Frais de 10% : entre 24h et 48h avant le départ</li>
                                <li>Frais de 25% : entre 2h et 24h avant le départ</li>
                                <li>Frais de 50% : moins de 2h avant le départ</li>
                                <li>Aucun remboursement après le départ du train</li>
                            </ul>
                        </div>

                        <!-- Formulaire de demande -->
                        <form action="${pageContext.request.contextPath}/user/annulation/create" method="post">
                            <input type="hidden" name="billetId" value="${billet.id}">
                            
                            <div class="mb-3">
                                <label for="motif" class="form-label">
                                    <strong>Motif de l'annulation <span class="text-danger">*</span></strong>
                                </label>
                                <textarea class="form-control" id="motif" name="motif" rows="5" 
                                        placeholder="Veuillez expliquer la raison de votre demande d'annulation..." 
                                        required></textarea>
                                <div class="form-text">
                                    Décrivez précisément les raisons de votre demande d'annulation. 
                                    Cette information aidera l'administrateur à traiter votre demande.
                                </div>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Important</h6>
                                <p class="mb-0">
                                    Une fois votre demande soumise, elle sera examinée par notre équipe dans un délai de 48h. 
                                    Vous recevrez une notification par email concernant la décision.
                                </p>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="${pageContext.request.contextPath}/user/profile" 
                                   class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-paper-plane"></i> Soumettre la demande
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include file="../common/footer.jsp" %>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Validation du formulaire
        document.querySelector('form').addEventListener('submit', function(e) {
            const motif = document.getElementById('motif').value.trim();
            if (motif.length < 10) {
                e.preventDefault();
                alert('Le motif doit contenir au moins 10 caractères.');
                return false;
            }
            
            if (!confirm('Êtes-vous sûr de vouloir soumettre cette demande d\'annulation ?')) {
                e.preventDefault();
                return false;
            }
        });
    </script>
</body>
</html>
