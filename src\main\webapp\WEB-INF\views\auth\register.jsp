<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<%-- Chargement des données dynamiques si pas déjà présentes --%>
<c:if test="${empty avantagesInscription}">
    <%
        try {
            // Créer et utiliser le contrôleur pour charger les données
            com.trainticket.controller.AuthController authController =
                new com.trainticket.controller.AuthController();
            authController.loadRegisterData(request);
        } catch (Exception e) {
            // En cas d'erreur, définir des valeurs par défaut
            java.util.List<String> avantagesDefaut = java.util.Arrays.asList(
                "Réservation rapide et sécurisée",
                "Historique de vos voyages",
                "Offres exclusives"
            );
            request.setAttribute("avantagesInscription", avantagesDefaut);
            request.setAttribute("nombreUtilisateursInscrits", 1000);
            // System.err.println("Erreur lors du chargement des données d'inscription: " + e.getMessage());
        }
    %>
</c:if>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inscription - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="${pageContext.request.contextPath}/resources/css/style.css" rel="stylesheet">
    <style>
        .register-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem 0;
        }
        .register-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 600px;
            margin: 0 auto;
        }
        .register-header {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .register-body {
            padding: 2rem;
        }
        .password-strength {
            height: 5px;
            border-radius: 3px;
            margin-top: 5px;
            transition: all 0.3s ease;
        }
        .strength-weak { background-color: #dc3545; }
        .strength-medium { background-color: #ffc107; }
        .strength-strong { background-color: #28a745; }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="container">
            <div class="register-card">
                <div class="register-header">
                    <h2><i class="fas fa-user-plus"></i> Créer un compte</h2>
                    <p class="mb-3">Rejoignez ${nombreUtilisateursInscrits}+ utilisateurs qui font confiance à Train Ticket</p>

                    <!-- Avantages de l'inscription -->
                    <div class="row text-start">
                        <c:forEach var="avantage" items="${avantagesInscription}" varStatus="status">
                            <c:if test="${status.index < 3}">
                                <div class="col-12 mb-2">
                                    <small><i class="fas fa-check-circle me-2"></i>${avantage}</small>
                                </div>
                            </c:if>
                        </c:forEach>
                    </div>
                </div>

                <div class="register-body">
                    <!-- Messages d'erreur -->
                    <c:if test="${not empty errorMessage}">
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle"></i> ${errorMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </c:if>

                    <form action="${pageContext.request.contextPath}/auth/register" method="post"
                          id="registerForm" data-validate="true">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="prenom" name="prenom"
                                           placeholder="Prénom" value="${prenom}" required>
                                    <label for="prenom"><i class="fas fa-user"></i> Prénom</label>
                                    <div class="invalid-feedback">
                                        Le prénom est requis (minimum 2 caractères)
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="nom" name="nom"
                                           placeholder="Nom" value="${nom}" required>
                                    <label for="nom"><i class="fas fa-user"></i> Nom</label>
                                    <div class="invalid-feedback">
                                        Le nom est requis (minimum 2 caractères)
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-floating mb-3">
                            <input type="email" class="form-control" id="email" name="email"
                                   placeholder="<EMAIL>" value="${email}" required>
                            <label for="email"><i class="fas fa-envelope"></i> Adresse email</label>
                            <div class="invalid-feedback">
                                Veuillez entrer une adresse email valide
                            </div>
                        </div>

                        <div class="form-floating mb-3">
                            <input type="tel" class="form-control" id="telephone" name="telephone"
                                   placeholder="Téléphone" value="${telephone}"
                                   pattern="^(\+212|0)[5-7][0-9]{8}$">
                            <label for="telephone"><i class="fas fa-phone"></i> Téléphone (optionnel)</label>
                            <div class="form-text">Format: 0612345678 ou +212612345678</div>
                            <div class="invalid-feedback">
                                Format de téléphone invalide (format marocain)
                            </div>
                        </div>

                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="adresse" name="adresse"
                                      placeholder="Adresse" style="height: 80px">${adresse}</textarea>
                            <label for="adresse"><i class="fas fa-map-marker-alt"></i> Adresse (optionnel)</label>
                        </div>

                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="motDePasse" name="motDePasse"
                                   placeholder="Mot de passe" required>
                            <label for="motDePasse"><i class="fas fa-lock"></i> Mot de passe</label>
                            <div class="password-strength" id="passwordStrength"></div>
                            <div class="form-text">
                                Le mot de passe doit contenir au moins 8 caractères, une majuscule,
                                une minuscule, un chiffre et un caractère spécial
                            </div>
                            <div class="invalid-feedback">
                                Le mot de passe ne respecte pas les critères de sécurité
                            </div>
                        </div>

                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="confirmMotDePasse"
                                   name="confirmMotDePasse" placeholder="Confirmer le mot de passe" required>
                            <label for="confirmMotDePasse"><i class="fas fa-lock"></i> Confirmer le mot de passe</label>
                            <div class="invalid-feedback">
                                Les mots de passe ne correspondent pas
                            </div>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="acceptTerms" required>
                            <label class="form-check-label" for="acceptTerms">
                                J'accepte les <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">
                                conditions d'utilisation</a> et la
                                <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">
                                politique de confidentialité</a>
                            </label>
                            <div class="invalid-feedback">
                                Vous devez accepter les conditions d'utilisation
                            </div>
                        </div>

                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter">
                            <label class="form-check-label" for="newsletter">
                                Je souhaite recevoir les offres et actualités par email
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg w-100 mb-3">
                            <i class="fas fa-user-plus"></i> Créer mon compte
                        </button>
                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="mb-2">Vous avez déjà un compte ?</p>
                        <a href="${pageContext.request.contextPath}/auth/login"
                           class="btn btn-outline-primary w-100">
                            <i class="fas fa-sign-in-alt"></i> Se connecter
                        </a>
                    </div>

                    <div class="text-center mt-3">
                        <a href="${pageContext.request.contextPath}/" class="text-muted text-decoration-none">
                            <i class="fas fa-arrow-left"></i> Retour à l'accueil
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Conditions d'utilisation -->
    <div class="modal fade" id="termsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Conditions d'utilisation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>1. Acceptation des conditions</h6>
                    <p>En utilisant Train Ticket, vous acceptez ces conditions d'utilisation...</p>

                    <h6>2. Utilisation du service</h6>
                    <p>Vous vous engagez à utiliser le service de manière responsable...</p>

                    <h6>3. Réservations et paiements</h6>
                    <p>Les réservations sont soumises à disponibilité...</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Politique de confidentialité -->
    <div class="modal fade" id="privacyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Politique de confidentialité</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>1. Collecte des données</h6>
                    <p>Nous collectons les informations que vous nous fournissez...</p>

                    <h6>2. Utilisation des données</h6>
                    <p>Vos données sont utilisées pour traiter vos réservations...</p>

                    <h6>3. Protection des données</h6>
                    <p>Nous mettons en place des mesures de sécurité appropriées...</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Validation du formulaire
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const form = this;

            // Validation des mots de passe
            const password = document.getElementById('motDePasse').value;
            const confirmPassword = document.getElementById('confirmMotDePasse').value;

            if (password !== confirmPassword) {
                e.preventDefault();
                document.getElementById('confirmMotDePasse').classList.add('is-invalid');
                return;
            }

            // Validation de la force du mot de passe
            if (!isStrongPassword(password)) {
                e.preventDefault();
                document.getElementById('motDePasse').classList.add('is-invalid');
                return;
            }

            // Validation des conditions
            if (!document.getElementById('acceptTerms').checked) {
                e.preventDefault();
                document.getElementById('acceptTerms').classList.add('is-invalid');
                return;
            }

            // Afficher un indicateur de chargement
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Création du compte...';
            submitBtn.disabled = true;
        });

        // Vérification de la force du mot de passe
        document.getElementById('motDePasse').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('passwordStrength');
            const strength = getPasswordStrength(password);

            strengthBar.className = 'password-strength';

            if (password.length > 0) {
                if (strength < 3) {
                    strengthBar.classList.add('strength-weak');
                } else if (strength < 5) {
                    strengthBar.classList.add('strength-medium');
                } else {
                    strengthBar.classList.add('strength-strong');
                }
            }
        });

        // Vérification de la correspondance des mots de passe
        document.getElementById('confirmMotDePasse').addEventListener('input', function() {
            const password = document.getElementById('motDePasse').value;
            const confirmPassword = this.value;

            if (confirmPassword && password !== confirmPassword) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
            }
        });

        function getPasswordStrength(password) {
            let strength = 0;

            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;

            return strength;
        }

        function isStrongPassword(password) {
            return getPasswordStrength(password) >= 4;
        }

        // Auto-focus sur le premier champ
        document.getElementById('prenom').focus();
    </script>
</body>
</html>
