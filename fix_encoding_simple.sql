-- Script simple pour corriger l'encodage des caractères spéciaux
-- À exécuter directement dans MySQL

USE train_ticket_db;

-- Corriger les noms de villes avec caractères spéciaux
UPDATE gares SET ville = 'Gabès' WHERE ville LIKE '%Gab%' AND ville != 'Gabès';
UPDATE gares SET ville = 'Béja' WHERE ville LIKE '%B%ja%' AND ville != 'Béja';
UPDATE gares SET ville = 'Médenine' WHERE ville LIKE '%M%denine%' AND ville != 'Médenine';
UPDATE gares SET ville = 'Kébili' WHERE ville LIKE '%K%bili%' AND ville != 'Kébili';
UPDATE gares SET ville = 'Tataouine' WHERE ville LIKE '%Tataouine%';
UPDATE gares SET ville = 'Jendouba' WHERE ville LIKE '%Jendouba%';
UPDATE gares SET ville = 'Le Kef' WHERE ville LIKE '%Kef%' AND ville != 'Le Kef';
UPDATE gares SET ville = 'Siliana' WHERE ville LIKE '%Siliana%';
UPDATE gares SET ville = 'Kasserine' WHERE ville LIKE '%Kasserine%';
UPDATE gares SET ville = 'Sidi Bouzid' WHERE ville LIKE '%Sidi%Bouzid%';

-- Corriger les adresses avec caractères spéciaux
UPDATE gares SET adresse = 'Avenue de la République, Kairouan' WHERE adresse LIKE '%R%publique%';
UPDATE gares SET adresse = 'Avenue Bourguiba, Gabès' WHERE adresse LIKE '%Bourguiba%' AND ville = 'Gabès';
UPDATE gares SET adresse = 'Avenue Bourguiba, Béja' WHERE adresse LIKE '%Bourguiba%' AND ville = 'Béja';
UPDATE gares SET adresse = 'Avenue Bourguiba, Le Kef' WHERE adresse LIKE '%Bourguiba%' AND ville = 'Le Kef';

-- Vérifier les résultats
SELECT id, nom, ville, adresse FROM gares WHERE ville IN ('Gabès', 'Béja', 'Médenine', 'Kébili', 'Le Kef') ORDER BY ville;
