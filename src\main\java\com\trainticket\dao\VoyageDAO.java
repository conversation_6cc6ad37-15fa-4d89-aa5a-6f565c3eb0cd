package com.trainticket.dao;

import com.trainticket.model.Voyage;
import com.trainticket.model.Trajet;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;


public interface VoyageDAO extends BaseDAO<Voyage, Long> {

    /**
     * Trouver les voyages par trajet
     * @param trajet le trajet
     * @return liste des voyages pour ce trajet
     */
    List<Voyage> findByTrajet(Trajet trajet);

    /**
     * Trouver les voyages par date
     * @param date la date de voyage
     * @return liste des voyages à cette date
     */
    List<Voyage> findByDate(LocalDate date);

    /**
     * Trouver les voyages entre deux dates
     * @param dateDebut date de début
     * @param dateFin date de fin
     * @return liste des voyages dans cette période
     */
    List<Voyage> findByDateRange(LocalDateTime dateDebut, LocalDateTime dateFin);

    /**
     * Trouver les voyages par statut
     * @param statut le statut du voyage
     * @return liste des voyages avec ce statut
     */
    List<Voyage> findByStatut(Voyage.StatutVoyage statut);

    /**
     * Trouver les voyages disponibles (avec places)
     * @return liste des voyages ayant des places disponibles
     */
    List<Voyage> findAvailableVoyages();

    /**
     * Rechercher des voyages entre deux villes à une date donnée
     * @param villeDepart ville de départ
     * @param villeArrivee ville d'arrivée
     * @param date date de voyage
     * @return liste des voyages correspondants
     */
    List<Voyage> searchVoyages(String villeDepart, String villeArrivee, LocalDate date);

    /**
     * Rechercher des voyages directs entre deux villes
     * @param villeDepart ville de départ
     * @param villeArrivee ville d'arrivée
     * @param date date de voyage
     * @return liste des voyages directs
     */
    List<Voyage> searchDirectVoyages(String villeDepart, String villeArrivee, LocalDate date);

    /**
     * Trouver les voyages avec places disponibles pour une classe
     * @param classe la classe de voyage
     * @return liste des voyages avec places disponibles
     */
    List<Voyage> findVoyagesWithAvailableSeats(Voyage.ClasseBillet classe);

    /**
     * Trouver les voyages à venir
     * @return liste des voyages futurs
     */
    List<Voyage> findUpcomingVoyages();

    /**
     * Trouver les voyages en cours
     * @return liste des voyages en cours
     */
    List<Voyage> findOngoingVoyages();

    /**
     * Trouver les voyages terminés
     * @return liste des voyages terminés
     */
    List<Voyage> findCompletedVoyages();

    /**
     * Trouver les voyages par trajet et date
     * @param trajet le trajet
     * @param date la date
     * @return liste des voyages pour ce trajet à cette date
     */
    List<Voyage> findByTrajetAndDate(Trajet trajet, LocalDate date);

    /**
     * Compter les voyages par statut
     * @param statut le statut
     * @return nombre de voyages avec ce statut
     */
    long countByStatut(Voyage.StatutVoyage statut);

    /**
     * Trouver les voyages les plus réservés
     * @param limit nombre maximum de voyages à retourner
     * @return liste des voyages les plus réservés
     */
    List<Voyage> findMostBookedVoyages(int limit);

    /**
     * Mettre à jour le statut des voyages expirés
     */
    void updateExpiredVoyagesStatus();

    /**
     * Trouver les voyages avec pagination
     * @param page numéro de page (commence à 0)
     * @param size taille de la page
     * @return liste paginée des voyages
     */
    List<Voyage> findWithPagination(int page, int size);
}
