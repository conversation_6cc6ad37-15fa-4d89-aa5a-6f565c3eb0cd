<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<%-- Chargement des données dynamiques si pas déjà présentes --%>
<c:if test="${empty statistiquesAdmin}">
    <jsp:useBean id="adminController" class="com.trainticket.controller.AdminController" scope="request" />
    <%
        // Charger les données si elles ne sont pas déjà présentes
        com.trainticket.controller.AdminController controller = 
            (com.trainticket.controller.AdminController) pageContext.getAttribute("adminController");
        controller.loadDashboardData(request);
    %>
</c:if>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord - Administration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="${pageContext.request.contextPath}/resources/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <jsp:include page="../common/navbar.jsp" />

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar Admin -->
            <div class="col-md-3 col-lg-2 admin-sidebar">
                <div class="p-3">
                    <h5 class="text-white"><i class="fas fa-cogs"></i> Administration</h5>
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="${pageContext.request.contextPath}/admin/dashboard">
                            <i class="fas fa-tachometer-alt"></i> Tableau de bord
                        </a>
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users"></i> Utilisateurs
                        </a>
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/gares">
                            <i class="fas fa-building"></i> Gares
                        </a>
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/trajets">
                            <i class="fas fa-route"></i> Trajets
                        </a>
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/voyages">
                            <i class="fas fa-train"></i> Voyages
                        </a>
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/reservations">
                            <i class="fas fa-ticket-alt"></i> Réservations
                        </a>
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/reports">
                            <i class="fas fa-chart-bar"></i> Rapports
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Contenu principal -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- En-tête -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-tachometer-alt"></i> Tableau de bord</h2>
                    <div>
                        <span class="text-muted">Dernière mise à jour: </span>
                        <span class="fw-bold"><fmt:formatDate value="${now}" pattern="dd/MM/yyyy HH:mm" /></span>
                    </div>
                </div>

                <!-- Statistiques principales -->
                <div class="row g-4 mb-4">
                    <div class="col-md-3">
                        <div class="card dashboard-card text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <h3>${statistiquesAdmin.nombreUtilisateurs}</h3>
                                <p class="mb-0">Utilisateurs</p>
                                <small class="opacity-75">+${statistiquesAdmin.nouveauxUtilisateursCeMois} ce mois</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-card text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-ticket-alt fa-3x mb-3"></i>
                                <h3>${statistiquesAdmin.nombreReservations}</h3>
                                <p class="mb-0">Réservations</p>
                                <small class="opacity-75">+${statistiquesAdmin.reservationsAujourdhui} aujourd'hui</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-card text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-train fa-3x mb-3"></i>
                                <h3>${statistiquesAdmin.nombreVoyages}</h3>
                                <p class="mb-0">Voyages</p>
                                <small class="opacity-75">${statistiquesAdmin.voyagesAujourdhui} aujourd'hui</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-card text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-euro-sign fa-3x mb-3"></i>
                                <h3><fmt:formatNumber value="${statistiquesAdmin.chiffreAffaires}" type="currency" currencySymbol="DT" /></h3>
                                <p class="mb-0">Chiffre d'affaires</p>
                                <small class="opacity-75">Ce mois</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Graphiques et tableaux -->
                <div class="row g-4">
                    <!-- Activité récente -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-clock"></i> Activité récente</h5>
                            </div>
                            <div class="card-body">
                                <c:if test="${not empty activiteRecente}">
                                    <div class="list-group list-group-flush">
                                        <c:forEach var="activite" items="${activiteRecente}" varStatus="status">
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div>
                                                    <i class="fas fa-${activite.icone} text-${activite.couleur} me-2"></i>
                                                    ${activite.description}
                                                </div>
                                                <small class="text-muted">
                                                    <fmt:formatDate value="${activite.date}" pattern="HH:mm" />
                                                </small>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </c:if>
                            </div>
                        </div>
                    </div>

                    <!-- Actions rapides -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-bolt"></i> Actions rapides</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="${pageContext.request.contextPath}/admin/gares/add" class="btn btn-outline-primary">
                                        <i class="fas fa-plus"></i> Ajouter une gare
                                    </a>
                                    <a href="${pageContext.request.contextPath}/admin/trajets/add" class="btn btn-outline-success">
                                        <i class="fas fa-plus"></i> Créer un trajet
                                    </a>
                                    <a href="${pageContext.request.contextPath}/admin/voyages/add" class="btn btn-outline-info">
                                        <i class="fas fa-plus"></i> Programmer un voyage
                                    </a>
                                    <a href="${pageContext.request.contextPath}/admin/reports" class="btn btn-outline-warning">
                                        <i class="fas fa-chart-bar"></i> Générer un rapport
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Alertes système -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-exclamation-triangle text-warning"></i> Alertes</h6>
                            </div>
                            <div class="card-body">
                                <c:if test="${not empty alertesSysteme}">
                                    <c:forEach var="alerte" items="${alertesSysteme}">
                                        <div class="alert alert-${alerte.niveau} alert-sm" role="alert">
                                            <small>${alerte.message}</small>
                                        </div>
                                    </c:forEach>
                                </c:if>
                                <c:if test="${empty alertesSysteme}">
                                    <p class="text-success mb-0">
                                        <i class="fas fa-check-circle"></i> Aucune alerte
                                    </p>
                                </c:if>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Trajets populaires -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-star text-warning"></i> Trajets les plus populaires</h5>
                            </div>
                            <div class="card-body">
                                <c:if test="${not empty trajetsPopulaires}">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Trajet</th>
                                                    <th>Type</th>
                                                    <th>Réservations</th>
                                                    <th>Taux d'occupation</th>
                                                    <th>Revenus</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <c:forEach var="trajet" items="${trajetsPopulaires}" varStatus="status">
                                                    <tr>
                                                        <td>
                                                            <strong>${trajet.gareDepart.ville} → ${trajet.gareArrivee.ville}</strong>
                                                            <br><small class="text-muted">${trajet.numeroTrajet}</small>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-${trajet.type == 'EXPRESS' ? 'danger' : trajet.type == 'DIRECT' ? 'warning' : 'secondary'}">
                                                                ${trajet.type}
                                                            </span>
                                                        </td>
                                                        <td>${trajet.nombreReservations}</td>
                                                        <td>
                                                            <div class="progress" style="height: 20px;">
                                                                <div class="progress-bar" role="progressbar" 
                                                                     style="width: ${trajet.tauxOccupation}%">
                                                                    ${trajet.tauxOccupation}%
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <fmt:formatNumber value="${trajet.revenus}" type="currency" currencySymbol="DT" />
                                                        </td>
                                                    </tr>
                                                </c:forEach>
                                            </tbody>
                                        </table>
                                    </div>
                                </c:if>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <jsp:include page="../common/footer.jsp" />

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/main.js"></script>
    
    <script>
        // Actualisation automatique des données toutes les 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000);
        
        // Animation des cartes de statistiques
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
