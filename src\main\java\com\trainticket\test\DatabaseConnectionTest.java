package com.trainticket.test;

import com.trainticket.util.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.SessionFactory;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * Servlet de test pour vérifier la connexion à la base de données
 */
@WebServlet(name = "DatabaseConnectionTest", urlPatterns = {"/test/db-connection"})
public class DatabaseConnectionTest extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();

        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>Test de Connexion Base de Données</title>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 40px; }");
        out.println(".success { color: green; font-weight: bold; }");
        out.println(".error { color: red; font-weight: bold; }");
        out.println(".info { color: blue; }");
        out.println("pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        out.println("<h1>🔍 Test de Connexion Base de Données</h1>");

        // Test 1: Connexion JDBC directe
        out.println("<h2>1. Test Connexion JDBC Directe</h2>");
        testJdbcConnection(out);

        // Test 2: Connexion Hibernate
        out.println("<h2>2. Test Connexion Hibernate</h2>");
        testHibernateConnection(out);

        // Test 3: Vérification des tables
        out.println("<h2>3. Vérification des Tables</h2>");
        testTablesExistence(out);

        out.println("<hr>");
        out.println("<p><a href='" + request.getContextPath() + "/'>&larr; Retour à l'accueil</a></p>");
        out.println("</body>");
        out.println("</html>");
    }

    private void testJdbcConnection(PrintWriter out) {
        String url = "********************************************************************************************************";
        String username = "root";
        String password = "root";

        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection connection = DriverManager.getConnection(url, username, password);
            
            out.println("<p class='success'>✅ Connexion JDBC réussie !</p>");
            out.println("<p class='info'>URL: " + url + "</p>");
            out.println("<p class='info'>Utilisateur: " + username + "</p>");
            out.println("<p class='info'>Base de données: " + connection.getCatalog() + "</p>");
            
            connection.close();
            
        } catch (ClassNotFoundException e) {
            out.println("<p class='error'>❌ Driver MySQL non trouvé !</p>");
            out.println("<pre>" + e.getMessage() + "</pre>");
        } catch (SQLException e) {
            out.println("<p class='error'>❌ Erreur de connexion JDBC !</p>");
            out.println("<pre>" + e.getMessage() + "</pre>");
            out.println("<p><strong>Solutions possibles :</strong></p>");
            out.println("<ul>");
            out.println("<li>Vérifiez que MySQL est démarré</li>");
            out.println("<li>Vérifiez que la base 'train_ticket_db' existe</li>");
            out.println("<li>Vérifiez les identifiants (root/root)</li>");
            out.println("<li>Vérifiez que le port 3306 est ouvert</li>");
            out.println("</ul>");
        }
    }

    private void testHibernateConnection(PrintWriter out) {
        try {
            SessionFactory sessionFactory = HibernateUtil.getSessionFactory();
            Session session = sessionFactory.openSession();
            
            out.println("<p class='success'>✅ Connexion Hibernate réussie !</p>");
            out.println("<p class='info'>SessionFactory: " + sessionFactory.getClass().getSimpleName() + "</p>");
            out.println("<p class='info'>Session: " + session.getClass().getSimpleName() + "</p>");
            
            session.close();
            
        } catch (Exception e) {
            out.println("<p class='error'>❌ Erreur de connexion Hibernate !</p>");
            out.println("<pre>" + e.getMessage() + "</pre>");
            if (e.getCause() != null) {
                out.println("<p><strong>Cause :</strong></p>");
                out.println("<pre>" + e.getCause().getMessage() + "</pre>");
            }
        }
    }

    private void testTablesExistence(PrintWriter out) {
        try {
            Session session = HibernateUtil.getSessionFactory().openSession();
            
            // Test simple pour vérifier si les tables existent
            String[] tables = {"Utilisateur", "Gare", "Trajet", "Voyage", "Billet"};
            
            for (String table : tables) {
                try {
                    Long count = (Long) session.createQuery("SELECT COUNT(*) FROM " + table).uniqueResult();
                    out.println("<p class='success'>✅ Table " + table + ": " + count + " enregistrements</p>");
                } catch (Exception e) {
                    out.println("<p class='error'>❌ Table " + table + ": " + e.getMessage() + "</p>");
                }
            }
            
            session.close();
            
        } catch (Exception e) {
            out.println("<p class='error'>❌ Impossible de vérifier les tables !</p>");
            out.println("<pre>" + e.getMessage() + "</pre>");
        }
    }
}
