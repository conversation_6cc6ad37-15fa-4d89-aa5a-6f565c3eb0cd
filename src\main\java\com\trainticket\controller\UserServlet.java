package com.trainticket.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trainticket.model.Billet;
import com.trainticket.model.Utilisateur;
import com.trainticket.service.ReservationService;
import com.trainticket.service.impl.ReservationServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UserServlet extends HttpServlet {

    private static final Logger logger = LoggerFactory.getLogger(UserServlet.class);
    private ReservationService reservationService;
    private ObjectMapper objectMapper;

    @Override
    public void init() throws ServletException {
        super.init();
        this.reservationService = new ReservationServiceImpl();
        this.objectMapper = new ObjectMapper();
        logger.info("UserServlet initialisé");
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Vérifier l'authentification
        if (!isUserAuthenticated(request)) {
            response.sendRedirect(request.getContextPath() + "/auth/login");
            return;
        }

        String pathInfo = request.getPathInfo();

        if (pathInfo == null) {
            response.sendRedirect(request.getContextPath() + "/user/profile");
            return;
        }

        switch (pathInfo) {
            case "/profile":
                showProfile(request, response);
                break;
            case "/reservations":
                showReservations(request, response);
                break;
            case "/history":
                showHistory(request, response);
                break;
            case "/billet":
                showBilletDetails(request, response);
                break;
            case "/edit-profile":
                showEditProfile(request, response);
                break;
            case "/change-password":
                showChangePassword(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Vérifier l'authentification
        if (!isUserAuthenticated(request)) {
            response.sendRedirect(request.getContextPath() + "/auth/login");
            return;
        }

        String pathInfo = request.getPathInfo();

        if (pathInfo == null) {
            response.sendRedirect(request.getContextPath() + "/user/profile");
            return;
        }

        switch (pathInfo) {
            case "/update-profile":
                handleUpdateProfile(request, response);
                break;
            case "/cancel-reservation":
                handleCancelReservation(request, response);
                break;
            case "/validate-ticket":
                handleValidateTicket(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    private void showProfile(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession();
        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");

        try {
            // Obtenir les statistiques de l'utilisateur
            List<Billet> reservationsActives = reservationService.getReservationsActives(utilisateur);
            List<Billet> historiqueVoyages = reservationService.getHistoriqueVoyages(utilisateur);

            request.setAttribute("utilisateur", utilisateur);
            request.setAttribute("nombreReservationsActives", reservationsActives.size());
            request.setAttribute("nombreVoyagesEffectues", historiqueVoyages.size());

            request.getRequestDispatcher("/WEB-INF/views/user/profile.jsp").forward(request, response);

        } catch (ReservationService.ReservationException e) {
            logger.error("Erreur lors du chargement du profil pour l'utilisateur {}",
                        utilisateur.getId(), e);
            request.setAttribute("errorMessage", "Erreur lors du chargement du profil");
            request.getRequestDispatcher("/WEB-INF/views/user/profile.jsp").forward(request, response);
        }
    }

    private void showReservations(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession();
        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");

        try {
            List<Billet> reservationsActives = reservationService.getReservationsActives(utilisateur);

            request.setAttribute("reservations", reservationsActives);
            request.getRequestDispatcher("/WEB-INF/views/user/reservations.jsp").forward(request, response);

        } catch (ReservationService.ReservationException e) {
            logger.error("Erreur lors du chargement des réservations pour l'utilisateur {}",
                        utilisateur.getId(), e);
            request.setAttribute("errorMessage", "Erreur lors du chargement des réservations");
            request.getRequestDispatcher("/WEB-INF/views/user/reservations.jsp").forward(request, response);
        }
    }

    private void showHistory(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession();
        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");

        try {
            List<Billet> historique = reservationService.getHistoriqueVoyages(utilisateur);

            request.setAttribute("historique", historique);
            request.getRequestDispatcher("/WEB-INF/views/user/history.jsp").forward(request, response);

        } catch (ReservationService.ReservationException e) {
            logger.error("Erreur lors du chargement de l'historique pour l'utilisateur {}",
                        utilisateur.getId(), e);
            request.setAttribute("errorMessage", "Erreur lors du chargement de l'historique");
            request.getRequestDispatcher("/WEB-INF/views/user/history.jsp").forward(request, response);
        }
    }

    private void showBilletDetails(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String numeroBillet = request.getParameter("numero");

        if (numeroBillet == null || numeroBillet.trim().isEmpty()) {
            response.sendRedirect(request.getContextPath() + "/user/reservations");
            return;
        }

        HttpSession session = request.getSession();
        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");

        try {
            Billet billet = reservationService.getBilletDetails(numeroBillet);

            // Vérifier que le billet appartient à l'utilisateur connecté
            if (!billet.getUtilisateur().getId().equals(utilisateur.getId())) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès non autorisé");
                return;
            }

            request.setAttribute("billet", billet);
            request.setAttribute("peutEtreAnnule", reservationService.peutEtreAnnule(billet));
            request.setAttribute("peutEtreModifie", reservationService.peutEtreModifie(billet));

            request.getRequestDispatcher("/WEB-INF/views/user/billet-details.jsp").forward(request, response);

        } catch (ReservationService.ReservationException e) {
            logger.error("Erreur lors du chargement des détails du billet {}", numeroBillet, e);
            response.sendRedirect(request.getContextPath() + "/user/reservations");
        }
    }

    private void showEditProfile(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession();
        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");

        request.setAttribute("utilisateur", utilisateur);
        request.getRequestDispatcher("/WEB-INF/views/user/edit-profile.jsp").forward(request, response);
    }

    private void showChangePassword(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        request.getRequestDispatcher("/WEB-INF/views/user/change-password.jsp").forward(request, response);
    }

    private void handleUpdateProfile(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession();
        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");

        try {
            // Récupérer les nouveaux données
            String nom = request.getParameter("nom");
            String prenom = request.getParameter("prenom");
            String telephone = request.getParameter("telephone");
            String adresse = request.getParameter("adresse");

            // Mettre à jour l'utilisateur
            utilisateur.setNom(nom);
            utilisateur.setPrenom(prenom);
            utilisateur.setTelephone(telephone);
            utilisateur.setAdresse(adresse);

            // Valider et sauvegarder (nous devons créer un UserService pour cela)
            // Pour l'instant, on simule la mise à jour

            logger.info("Profil mis à jour pour l'utilisateur {} (ID: {})",
                       utilisateur.getEmail(), utilisateur.getId());

            session.setAttribute("utilisateur", utilisateur);
            session.setAttribute("successMessage", "Profil mis à jour avec succès");

            response.sendRedirect(request.getContextPath() + "/user/profile");

        } catch (Exception e) {
            logger.error("Erreur lors de la mise à jour du profil pour l'utilisateur {}",
                        utilisateur.getId(), e);
            session.setAttribute("errorMessage", "Erreur lors de la mise à jour du profil");
            response.sendRedirect(request.getContextPath() + "/user/edit-profile");
        }
    }

    private void handleCancelReservation(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        HttpSession session = request.getSession();
        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");

        try {
            Long billetId = Long.parseLong(request.getParameter("billetId"));

            boolean success = reservationService.annulerReservation(billetId, utilisateur);

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);

            if (success) {
                result.put("message", "Réservation annulée avec succès");
                logger.info("Réservation annulée: billet ID {} pour l'utilisateur {} (ID: {})",
                           billetId, utilisateur.getEmail(), utilisateur.getId());
            } else {
                result.put("message", "Impossible d'annuler cette réservation");
            }

            objectMapper.writeValue(response.getWriter(), result);

        } catch (Exception e) {
            logger.error("Erreur lors de l'annulation de la réservation", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Erreur lors de l'annulation: " + e.getMessage());

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            objectMapper.writeValue(response.getWriter(), result);
        }
    }

    private void handleValidateTicket(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try {
            String numeroBillet = request.getParameter("numeroBillet");

            boolean success = reservationService.validerBillet(numeroBillet);

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);

            if (success) {
                result.put("message", "Billet validé avec succès");
                logger.info("Billet validé: {}", numeroBillet);
            } else {
                result.put("message", "Impossible de valider ce billet");
            }

            objectMapper.writeValue(response.getWriter(), result);

        } catch (Exception e) {
            logger.error("Erreur lors de la validation du billet", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Erreur lors de la validation: " + e.getMessage());

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            objectMapper.writeValue(response.getWriter(), result);
        }
    }

    private boolean isUserAuthenticated(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        return session != null && session.getAttribute("utilisateur") != null;
    }
}
