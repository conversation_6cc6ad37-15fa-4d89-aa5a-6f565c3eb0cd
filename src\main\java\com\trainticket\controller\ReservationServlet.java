package com.trainticket.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trainticket.model.*;
import com.trainticket.service.RechercheService;
import com.trainticket.service.ReservationService;
import com.trainticket.service.impl.RechercheServiceImpl;
import com.trainticket.service.impl.ReservationServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class ReservationServlet extends HttpServlet {

    private static final Logger logger = LoggerFactory.getLogger(ReservationServlet.class);
    private ReservationService reservationService;
    private RechercheService rechercheService;
    private ObjectMapper objectMapper;

    @Override
    public void init() throws ServletException {
        super.init();
        this.reservationService = new ReservationServiceImpl();
        this.rechercheService = new RechercheServiceImpl();
        this.objectMapper = new ObjectMapper();
        logger.info("ReservationServlet initialisé");
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Vérifier l'authentification
        if (!isUserAuthenticated(request)) {
            String currentUrl = request.getRequestURL().toString();
            if (request.getQueryString() != null) {
                currentUrl += "?" + request.getQueryString();
            }
            response.sendRedirect(request.getContextPath() + "/auth/login?redirectUrl=" + currentUrl);
            return;
        }

        String pathInfo = request.getPathInfo();

        if (pathInfo == null) {
            response.sendRedirect(request.getContextPath() + "/");
            return;
        }

        switch (pathInfo) {
            case "/select":
                showSelectOptionsPage(request, response);
                break;
            case "/confirm":
                showConfirmationPage(request, response);
                break;
            case "/success":
                showSuccessPage(request, response);
                break;
            case "/api/calculate-price":
                calculatePriceAPI(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Vérifier l'authentification
        if (!isUserAuthenticated(request)) {
            response.sendRedirect(request.getContextPath() + "/auth/login");
            return;
        }

        String pathInfo = request.getPathInfo();

        if (pathInfo == null) {
            response.sendRedirect(request.getContextPath() + "/");
            return;
        }

        switch (pathInfo) {
            case "/create":
                handleCreateReservation(request, response);
                break;
            case "/modify":
                handleModifyReservation(request, response);
                break;
            case "/cancel":
                handleCancelReservation(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    private void showSelectOptionsPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String voyageIdStr = request.getParameter("voyageId");

        if (voyageIdStr == null || voyageIdStr.trim().isEmpty()) {
            response.sendRedirect(request.getContextPath() + "/recherche/search");
            return;
        }

        try {
            Long voyageId = Long.parseLong(voyageIdStr);
            Voyage voyage = rechercheService.getVoyageDetails(voyageId);

            // Vérifier que le voyage est disponible
            if (voyage.getPlacesDisponibles() <= 0 ||
                voyage.getStatut() != Voyage.StatutVoyage.PROGRAMME) {
                request.setAttribute("errorMessage", "Ce voyage n'est plus disponible");
                response.sendRedirect(request.getContextPath() + "/recherche/search");
                return;
            }

            request.setAttribute("voyage", voyage);
            request.getRequestDispatcher("/WEB-INF/views/reservation/select-options.jsp")
                   .forward(request, response);

        } catch (NumberFormatException e) {
            logger.warn("ID de voyage invalide: {}", voyageIdStr);
            response.sendRedirect(request.getContextPath() + "/recherche/search");
        } catch (RechercheService.SearchException e) {
            logger.error("Erreur lors de la récupération du voyage {}", voyageIdStr, e);
            request.setAttribute("errorMessage", "Voyage non trouvé");
            response.sendRedirect(request.getContextPath() + "/recherche/search");
        }
    }

    private void showConfirmationPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession();

        // Récupérer les données de réservation de la session
        Long voyageId = (Long) session.getAttribute("reservationVoyageId");
        Voyage.ClasseBillet classe = (Voyage.ClasseBillet) session.getAttribute("reservationClasse");
        Preference preferences = (Preference) session.getAttribute("reservationPreferences");

        if (voyageId == null || classe == null) {
            response.sendRedirect(request.getContextPath() + "/recherche/search");
            return;
        }

        try {
            Voyage voyage = rechercheService.getVoyageDetails(voyageId);

            // Vérifier la disponibilité
            if (!reservationService.verifierDisponibilite(voyage, classe, 1)) {
                request.setAttribute("errorMessage", "Cette classe n'est plus disponible");
                response.sendRedirect(request.getContextPath() + "/reservation/select?voyageId=" + voyageId);
                return;
            }

            // Calculer le prix
            BigDecimal prix = reservationService.calculerPrix(voyage, classe, preferences);

            request.setAttribute("voyage", voyage);
            request.setAttribute("classe", classe);
            request.setAttribute("preferences", preferences);
            request.setAttribute("prix", prix);

            request.getRequestDispatcher("/WEB-INF/views/reservation/confirm-reservation.jsp")
                   .forward(request, response);

        } catch (Exception e) {
            logger.error("Erreur lors de la préparation de la confirmation", e);
            request.setAttribute("errorMessage", "Erreur lors de la préparation de la réservation");
            response.sendRedirect(request.getContextPath() + "/recherche/search");
        }
    }

    private void showSuccessPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String numeroBillet = request.getParameter("billet");

        if (numeroBillet == null || numeroBillet.trim().isEmpty()) {
            response.sendRedirect(request.getContextPath() + "/user/reservations");
            return;
        }

        try {
            Billet billet = reservationService.getBilletDetails(numeroBillet);
            request.setAttribute("billet", billet);

            request.getRequestDispatcher("/WEB-INF/views/reservation/success.jsp")
                   .forward(request, response);

        } catch (ReservationService.ReservationException e) {
            logger.error("Erreur lors de la récupération du billet {}", numeroBillet, e);
            response.sendRedirect(request.getContextPath() + "/user/reservations");
        }
    }

    private void handleCreateReservation(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession();
        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");

        try {
            // Récupérer les paramètres
            Long voyageId = Long.parseLong(request.getParameter("voyageId"));
            Voyage.ClasseBillet classe = Voyage.ClasseBillet.valueOf(request.getParameter("classe"));

            // Créer les préférences
            Preference preferences = new Preference();

            String preferenceFenetre = request.getParameter("preferenceFenetre");
            if (preferenceFenetre != null && !preferenceFenetre.trim().isEmpty()) {
                preferences.setPreferenceFenetre(Preference.PreferenceFenetre.valueOf(preferenceFenetre));
            }

            preferences.setWagonNonFumeur("on".equals(request.getParameter("wagonNonFumeur")));
            preferences.setEspaceFamille("on".equals(request.getParameter("espaceFamille")));
            preferences.setAccesHandicape("on".equals(request.getParameter("accesHandicape")));
            preferences.setWifiRequis("on".equals(request.getParameter("wifiRequis")));
            preferences.setPriseElectrique("on".equals(request.getParameter("priseElectrique")));
            preferences.setCommentairesSpeciaux(request.getParameter("commentairesSpeciaux"));

            // Récupérer le voyage
            Voyage voyage = rechercheService.getVoyageDetails(voyageId);

            // Créer la réservation
            Billet billet = reservationService.creerReservation(utilisateur, voyage, classe, preferences);

            logger.info("Réservation créée: billet {} pour l'utilisateur {} (ID: {})",
                       billet.getNumeroBillet(), utilisateur.getEmail(), utilisateur.getId());

            // Nettoyer la session
            session.removeAttribute("reservationVoyageId");
            session.removeAttribute("reservationClasse");
            session.removeAttribute("reservationPreferences");

            // Rediriger vers la page de succès
            response.sendRedirect(request.getContextPath() + "/reservation/success?billet=" +
                                billet.getNumeroBillet());

        } catch (Exception e) {
            logger.error("Erreur lors de la création de la réservation", e);
            session.setAttribute("errorMessage", "Erreur lors de la création de la réservation: " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/recherche/search");
        }
    }

    private void handleModifyReservation(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession();
        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");

        try {
            Long billetId = Long.parseLong(request.getParameter("billetId"));
            Long nouveauVoyageId = Long.parseLong(request.getParameter("nouveauVoyageId"));
            Voyage.ClasseBillet nouvelleClasse = Voyage.ClasseBillet.valueOf(request.getParameter("nouvelleClasse"));

            Voyage nouveauVoyage = rechercheService.getVoyageDetails(nouveauVoyageId);

            Billet billetModifie = reservationService.modifierReservation(
                billetId, nouveauVoyage, nouvelleClasse, utilisateur
            );

            logger.info("Réservation modifiée: billet {} pour l'utilisateur {} (ID: {})",
                       billetModifie.getNumeroBillet(), utilisateur.getEmail(), utilisateur.getId());

            session.setAttribute("successMessage", "Réservation modifiée avec succès");
            response.sendRedirect(request.getContextPath() + "/user/reservations");

        } catch (Exception e) {
            logger.error("Erreur lors de la modification de la réservation", e);
            session.setAttribute("errorMessage", "Erreur lors de la modification: " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/user/reservations");
        }
    }

    private void handleCancelReservation(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession();
        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");

        try {
            Long billetId = Long.parseLong(request.getParameter("billetId"));

            boolean success = reservationService.annulerReservation(billetId, utilisateur);

            if (success) {
                logger.info("Réservation annulée: billet ID {} pour l'utilisateur {} (ID: {})",
                           billetId, utilisateur.getEmail(), utilisateur.getId());
                session.setAttribute("successMessage", "Réservation annulée avec succès");
            } else {
                session.setAttribute("errorMessage", "Impossible d'annuler cette réservation");
            }

        } catch (Exception e) {
            logger.error("Erreur lors de l'annulation de la réservation", e);
            session.setAttribute("errorMessage", "Erreur lors de l'annulation: " + e.getMessage());
        }

        response.sendRedirect(request.getContextPath() + "/user/reservations");
    }

    private void calculatePriceAPI(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try {
            Long voyageId = Long.parseLong(request.getParameter("voyageId"));
            Voyage.ClasseBillet classe = Voyage.ClasseBillet.valueOf(request.getParameter("classe"));

            // Créer des préférences basiques pour le calcul
            Preference preferences = new Preference();
            preferences.setWagonNonFumeur("true".equals(request.getParameter("wagonNonFumeur")));
            preferences.setEspaceFamille("true".equals(request.getParameter("espaceFamille")));
            preferences.setAccesHandicape("true".equals(request.getParameter("accesHandicape")));
            preferences.setWifiRequis("true".equals(request.getParameter("wifiRequis")));
            preferences.setPriseElectrique("true".equals(request.getParameter("priseElectrique")));

            Voyage voyage = rechercheService.getVoyageDetails(voyageId);
            BigDecimal prix = reservationService.calculerPrix(voyage, classe, preferences);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("prix", prix);
            result.put("prixFormate", prix + " MAD");

            objectMapper.writeValue(response.getWriter(), result);

        } catch (Exception e) {
            logger.error("Erreur lors du calcul du prix", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Erreur lors du calcul du prix");

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            objectMapper.writeValue(response.getWriter(), result);
        }
    }

    private boolean isUserAuthenticated(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        return session != null && session.getAttribute("utilisateur") != null;
    }
}
