<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h1>✅ Application Train Ticket - Test de Connexion</h1>
                    </div>
                    <div class="card-body">
                        <h3>🎉 Félicitations !</h3>
                        <p>Si vous voyez cette page, cela signifie que :</p>
                        <ul>
                            <li>✅ Tomcat fonctionne correctement</li>
                            <li>✅ L'application est déployée</li>
                            <li>✅ Les fichiers statiques sont accessibles</li>
                        </ul>
                        
                        <hr>
                        
                        <h4>Liens de test :</h4>
                        <div class="d-grid gap-2">
                            <a href="index.jsp" class="btn btn-primary">Page d'accueil (JSP)</a>
                            <a href="data-test" class="btn btn-warning">Test des données</a>
                            <a href="recherche/search" class="btn btn-info">Page de recherche</a>
                        </div>
                        
                        <hr>
                        
                        <h4>Informations système :</h4>
                        <p><strong>URL actuelle :</strong> <span id="currentUrl"></span></p>
                        <p><strong>Date/Heure :</strong> <span id="currentTime"></span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('currentTime').textContent = new Date().toLocaleString();
    </script>
</body>
</html>
