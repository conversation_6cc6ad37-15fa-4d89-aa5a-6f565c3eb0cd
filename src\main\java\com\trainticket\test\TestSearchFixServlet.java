package com.trainticket.test;

import com.trainticket.dao.VoyageDAO;
import com.trainticket.dao.impl.VoyageDAOImpl;
import com.trainticket.model.Voyage;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDate;
import java.util.List;

@WebServlet("/test/search-fix")
public class TestSearchFixServlet extends HttpServlet {

    private VoyageDAO voyageDAO = new VoyageDAOImpl();

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>Test de Recherche Corrigée</title>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 40px; }");
        out.println(".success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; }");
        out.println(".error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; }");
        out.println(".voyage { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        out.println("<h1>Test de Recherche de Voyages - Version Corrigée</h1>");
        
        try {
            // Test 1: Recherche Tunis -> Sfax
            out.println("<h2>Test 1: Recherche Tunis → Sfax (demain)</h2>");
            LocalDate demain = LocalDate.now().plusDays(1);
            List<Voyage> voyages1 = voyageDAO.searchVoyages("Tunis", "Sfax", demain);
            
            if (voyages1.isEmpty()) {
                out.println("<div class='error'>Aucun voyage trouvé pour Tunis → Sfax le " + demain + "</div>");
            } else {
                out.println("<div class='success'>✅ " + voyages1.size() + " voyage(s) trouvé(s) pour Tunis → Sfax le " + demain + "</div>");
                for (Voyage voyage : voyages1) {
                    out.println("<div class='voyage'>");
                    out.println("<strong>Voyage ID:</strong> " + voyage.getId() + "<br>");
                    out.println("<strong>Trajet:</strong> " + voyage.getTrajet().getDescription() + "<br>");
                    out.println("<strong>Départ:</strong> " + voyage.getDateHeureDepart() + "<br>");
                    out.println("<strong>Arrivée:</strong> " + voyage.getDateHeureArrivee() + "<br>");
                    out.println("<strong>Places disponibles:</strong> " + voyage.getPlacesDisponibles() + "<br>");
                    out.println("<strong>Statut:</strong> " + voyage.getStatut() + "<br>");
                    out.println("</div>");
                }
            }
            
            // Test 2: Recherche Sfax -> Tunis
            out.println("<h2>Test 2: Recherche Sfax → Tunis (demain)</h2>");
            List<Voyage> voyages2 = voyageDAO.searchVoyages("Sfax", "Tunis", demain);
            
            if (voyages2.isEmpty()) {
                out.println("<div class='error'>Aucun voyage trouvé pour Sfax → Tunis le " + demain + "</div>");
            } else {
                out.println("<div class='success'>✅ " + voyages2.size() + " voyage(s) trouvé(s) pour Sfax → Tunis le " + demain + "</div>");
                for (Voyage voyage : voyages2) {
                    out.println("<div class='voyage'>");
                    out.println("<strong>Voyage ID:</strong> " + voyage.getId() + "<br>");
                    out.println("<strong>Trajet:</strong> " + voyage.getTrajet().getDescription() + "<br>");
                    out.println("<strong>Départ:</strong> " + voyage.getDateHeureDepart() + "<br>");
                    out.println("<strong>Arrivée:</strong> " + voyage.getDateHeureArrivee() + "<br>");
                    out.println("<strong>Places disponibles:</strong> " + voyage.getPlacesDisponibles() + "<br>");
                    out.println("<strong>Statut:</strong> " + voyage.getStatut() + "<br>");
                    out.println("</div>");
                }
            }
            
            // Test 3: Recherche directe
            out.println("<h2>Test 3: Recherche directe Tunis → Sfax (demain)</h2>");
            List<Voyage> voyages3 = voyageDAO.searchDirectVoyages("Tunis", "Sfax", demain);
            
            if (voyages3.isEmpty()) {
                out.println("<div class='error'>Aucun voyage direct trouvé pour Tunis → Sfax le " + demain + "</div>");
            } else {
                out.println("<div class='success'>✅ " + voyages3.size() + " voyage(s) direct(s) trouvé(s) pour Tunis → Sfax le " + demain + "</div>");
                for (Voyage voyage : voyages3) {
                    out.println("<div class='voyage'>");
                    out.println("<strong>Voyage ID:</strong> " + voyage.getId() + "<br>");
                    out.println("<strong>Trajet:</strong> " + voyage.getTrajet().getDescription() + "<br>");
                    out.println("<strong>Type:</strong> " + voyage.getTrajet().getType() + "<br>");
                    out.println("<strong>Départ:</strong> " + voyage.getDateHeureDepart() + "<br>");
                    out.println("<strong>Arrivée:</strong> " + voyage.getDateHeureArrivee() + "<br>");
                    out.println("<strong>Places disponibles:</strong> " + voyage.getPlacesDisponibles() + "<br>");
                    out.println("<strong>Statut:</strong> " + voyage.getStatut() + "<br>");
                    out.println("</div>");
                }
            }
            
        } catch (Exception e) {
            out.println("<div class='error'>");
            out.println("<h3>❌ Erreur lors du test:</h3>");
            out.println("<p><strong>Message:</strong> " + e.getMessage() + "</p>");
            out.println("<p><strong>Type:</strong> " + e.getClass().getSimpleName() + "</p>");
            out.println("<h4>Stack trace:</h4>");
            out.println("<pre>");
            e.printStackTrace(out);
            out.println("</pre>");
            out.println("</div>");
        }
        
        out.println("<hr>");
        out.println("<p><a href='/train-ticket/recherche/search'>← Retour à la recherche</a></p>");
        out.println("</body>");
        out.println("</html>");
    }
}
