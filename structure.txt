train-ticket-management/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   ├── com/
│   │   │   │   ├── trainticket/
│   │   │   │   │   ├── controller/
│   │   │   │   │   │   ├── AdminServlet.java
│   │   │   │   │   │   ├── AuthServlet.java
│   │   │   │   │   │   ├── RechercheServlet.java
│   │   │   │   │   │   ├── ReservationServlet.java
│   │   │   │   │   │   ├── PaiementServlet.java
│   │   │   │   │   │   ├── UserServlet.java
│   │   │   │   │   ├── dao/
│   │   │   │   │   │   ├── BilletDAO.java
│   │   │   │   │   │   ├── GareDAO.java
│   │   │   │   │   │   ├── TrajetDAO.java
│   │   │   │   │   │   ├── UtilisateurDAO.java
│   │   │   │   │   │   ├── VoyageDAO.java
│   │   │   │   │   │   ├── impl/
│   │   │   │   │   │   │   ├── BilletDAOImpl.java
│   │   │   │   │   │   │   ├── GareDAOImpl.java
│   │   │   │   │   │   │   ├── TrajetDAOImpl.java
│   │   │   │   │   │   │   ├── UtilisateurDAOImpl.java
│   │   │   │   │   │   │   ├── VoyageDAOImpl.java
│   │   │   │   │   ├── model/
│   │   │   │   │   │   ├── Billet.java
│   │   │   │   │   │   ├── Gare.java
│   │   │   │   │   │   ├── Trajet.java
│   │   │   │   │   │   ├── Utilisateur.java
│   │   │   │   │   │   ├── Voyage.java
│   │   │   │   │   │   ├── Preference.java
│   │   │   │   │   ├── service/
│   │   │   │   │   │   ├── AdminService.java
│   │   │   │   │   │   ├── AuthService.java
│   │   │   │   │   │   ├── RechercheService.java
│   │   │   │   │   │   ├── ReservationService.java
│   │   │   │   │   │   ├── PaiementService.java
│   │   │   │   │   │   ├── UserService.java
│   │   │   │   │   │   ├── impl/
│   │   │   │   │   │   │   ├── AdminServiceImpl.java
│   │   │   │   │   │   │   ├── AuthServiceImpl.java
│   │   │   │   │   │   │   ├── RechercheServiceImpl.java
│   │   │   │   │   │   │   ├── ReservationServiceImpl.java
│   │   │   │   │   │   │   ├── PaiementServiceImpl.java
│   │   │   │   │   │   │   ├── UserServiceImpl.java
│   │   │   │   │   ├── util/
│   │   │   │   │   │   ├── HibernateUtil.java
│   │   │   │   │   │   ├── PasswordUtil.java
│   │   ├── resources/
│   │   │   ├── hibernate.cfg.xml
│   │   │   ├── db.sql
│   │   ├── webapp/
│   │   │   ├── WEB-INF/
│   │   │   │   ├── web.xml
│   │   │   │   ├── views/
│   │   │   │   │   ├── common/
│   │   │   │   │   │   ├── header.jsp
│   │   │   │   │   │   ├── footer.jsp
│   │   │   │   │   │   ├── navigation.jsp
│   │   │   │   │   ├── auth/
│   │   │   │   │   │   ├── login.jsp
│   │   │   │   │   │   ├── register.jsp
│   │   │   │   │   ├── search/
│   │   │   │   │   │   ├── search-form.jsp
│   │   │   │   │   │   ├── search-results.jsp
│   │   │   │   │   ├── reservation/
│   │   │   │   │   │   ├── select-options.jsp
│   │   │   │   │   │   ├── confirm-reservation.jsp
│   │   │   │   │   ├── user/
│   │   │   │   │   │   ├── profile.jsp
│   │   │   │   │   │   ├── history.jsp
│   │   │   │   │   │   ├── reservations.jsp
│   │   │   │   │   ├── admin/
│   │   │   │   │   │   ├── dashboard.jsp
│   │   │   │   │   │   ├── manage-trajets.jsp
│   │   │   │   │   │   ├── manage-gares.jsp
│   │   │   │   │   │   ├── manage-voyages.jsp
│   │   │   │   │   │   ├── manage-users.jsp
│   │   │   │   │   ├── error/
│   │   │   │   │   │   ├── 404.jsp
│   │   │   │   │   │   ├── 500.jsp
│   │   │   ├── resources/
│   │   │   │   ├── css/
│   │   │   │   │   ├── style.css
│   │   │   │   ├── js/
│   │   │   │   │   ├── main.js
│   │   │   │   │   ├── validation.js
│   │   │   │   ├── images/
│   │   │   │   │   ├── logo.png
│   │   │   ├── index.jsp
├── pom.xml