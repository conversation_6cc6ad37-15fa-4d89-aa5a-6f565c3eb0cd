package com.trainticket.test;

import com.trainticket.service.RechercheService;
import com.trainticket.service.impl.RechercheServiceImpl;
import com.trainticket.model.Voyage;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDate;
import java.util.List;

@WebServlet("/test/search")
public class TestSearchServlet extends HttpServlet {

    private RechercheService rechercheService = new RechercheServiceImpl();

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        response.setContentType("text/html; charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html lang='fr'>");
        out.println("<head>");
        out.println("<meta charset='UTF-8'>");
        out.println("<title>Test de Recherche</title>");
        out.println("<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>");
        out.println("</head>");
        out.println("<body>");
        out.println("<div class='container mt-5'>");
        out.println("<h1>Test de Recherche de Voyages</h1>");
        
        try {
            // Test 1: Recherche avec villes existantes
            out.println("<h3>Test 1: Recherche Tunis -> Sousse</h3>");
            List<Voyage> voyages1 = rechercheService.rechercherVoyages(
                "Tunis", "Sousse", LocalDate.now().plusDays(1), 1, false
            );
            out.println("<p>Résultats trouvés: " + voyages1.size() + "</p>");
            
            // Test 2: Recherche avec villes inexistantes
            out.println("<h3>Test 2: Recherche VilleInexistante -> AutreVille</h3>");
            List<Voyage> voyages2 = rechercheService.rechercherVoyages(
                "VilleInexistante", "AutreVille", LocalDate.now().plusDays(1), 1, false
            );
            out.println("<p>Résultats trouvés: " + voyages2.size() + "</p>");
            
            // Test 3: Lister toutes les villes disponibles
            out.println("<h3>Test 3: Villes disponibles</h3>");
            List<String> villes = rechercheService.getVillesDisponibles();
            out.println("<p>Nombre de villes: " + villes.size() + "</p>");
            out.println("<ul>");
            for (String ville : villes) {
                out.println("<li>" + ville + "</li>");
            }
            out.println("</ul>");
            
            // Test 4: Recherche avec caractères spéciaux
            out.println("<h3>Test 4: Recherche avec caractères spéciaux</h3>");
            try {
                List<Voyage> voyages4 = rechercheService.rechercherVoyages(
                    "Tunis", "Gabès", LocalDate.now().plusDays(1), 1, false
                );
                out.println("<p>Résultats Tunis -> Gabès: " + voyages4.size() + "</p>");
            } catch (Exception e) {
                out.println("<p class='text-danger'>Erreur Tunis -> Gabès: " + e.getMessage() + "</p>");
                e.printStackTrace();
            }
            
        } catch (Exception e) {
            out.println("<div class='alert alert-danger'>");
            out.println("<h4>Erreur:</h4>");
            out.println("<p>" + e.getMessage() + "</p>");
            out.println("<pre>");
            e.printStackTrace(out);
            out.println("</pre>");
            out.println("</div>");
        }
        
        out.println("</div>");
        out.println("</body>");
        out.println("</html>");
    }
}
