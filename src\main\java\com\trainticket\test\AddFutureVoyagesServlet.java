package com.trainticket.test;

import com.trainticket.util.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.Transaction;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@WebServlet("/test/add-future-voyages")
public class AddFutureVoyagesServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();

        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>Ajout de voyages futurs</title>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 40px; }");
        out.println(".success { color: green; }");
        out.println(".error { color: red; }");
        out.println(".info { color: blue; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        out.println("<h1>Ajout de voyages pour les dates futures</h1>");

        Session session = null;
        Transaction transaction = null;

        try {
            session = HibernateUtil.getSessionFactory().openSession();
            transaction = session.beginTransaction();

            out.println("<div class='info'>Connexion à la base de données réussie...</div>");

            // Voyages Tunis - Sousse (trajet_id = 3)
            String[] tunisToSousse = {
                "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES (3, '2025-05-30 06:00:00', '2025-05-30 08:00:00', 180, 18, 54, 108, 'PROGRAMME')",
                "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES (3, '2025-05-30 09:30:00', '2025-05-30 11:30:00', 180, 18, 54, 108, 'PROGRAMME')",
                "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES (3, '2025-05-30 13:00:00', '2025-05-30 15:00:00', 180, 18, 54, 108, 'PROGRAMME')",
                "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES (3, '2025-05-30 16:30:00', '2025-05-30 18:30:00', 180, 18, 54, 108, 'PROGRAMME')",
                "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES (3, '2025-05-30 19:00:00', '2025-05-30 21:00:00', 180, 18, 54, 108, 'PROGRAMME')",
                "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES (3, '2025-05-31 06:00:00', '2025-05-31 08:00:00', 180, 18, 54, 108, 'PROGRAMME')",
                "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES (3, '2025-05-31 09:30:00', '2025-05-31 11:30:00', 180, 18, 54, 108, 'PROGRAMME')",
                "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES (3, '2025-05-31 13:00:00', '2025-05-31 15:00:00', 180, 18, 54, 108, 'PROGRAMME')",
                "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES (3, '2025-06-01 06:00:00', '2025-06-01 08:00:00', 180, 18, 54, 108, 'PROGRAMME')",
                "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES (3, '2025-06-01 13:00:00', '2025-06-01 15:00:00', 180, 18, 54, 108, 'PROGRAMME')"
            };

            // Voyages Sousse - Tunis (trajet_id = 4)
            String[] sousseToTunis = {
                "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES (4, '2025-05-30 07:00:00', '2025-05-30 09:00:00', 180, 18, 54, 108, 'PROGRAMME')",
                "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES (4, '2025-05-30 10:30:00', '2025-05-30 12:30:00', 180, 18, 54, 108, 'PROGRAMME')",
                "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES (4, '2025-05-30 14:00:00', '2025-05-30 16:00:00', 180, 18, 54, 108, 'PROGRAMME')",
                "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES (4, '2025-05-30 17:30:00', '2025-05-30 19:30:00', 180, 18, 54, 108, 'PROGRAMME')",
                "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES (4, '2025-05-31 07:00:00', '2025-05-31 09:00:00', 180, 18, 54, 108, 'PROGRAMME')",
                "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES (4, '2025-06-01 07:00:00', '2025-06-01 09:00:00', 180, 18, 54, 108, 'PROGRAMME')"
            };

            out.println("<h3>Ajout des voyages Tunis → Sousse</h3>");
            for (String sql : tunisToSousse) {
                try {
                    session.createNativeQuery(sql).executeUpdate();
                    out.println("<div class='success'>✓ Voyage ajouté</div>");
                } catch (Exception e) {
                    out.println("<div class='error'>✗ Erreur: " + e.getMessage() + "</div>");
                }
            }

            out.println("<h3>Ajout des voyages Sousse → Tunis</h3>");
            for (String sql : sousseToTunis) {
                try {
                    session.createNativeQuery(sql).executeUpdate();
                    out.println("<div class='success'>✓ Voyage ajouté</div>");
                } catch (Exception e) {
                    out.println("<div class='error'>✗ Erreur: " + e.getMessage() + "</div>");
                }
            }

            transaction.commit();
            out.println("<h2 class='success'>✅ Tous les voyages ont été ajoutés avec succès!</h2>");

            // Vérification
            out.println("<h3>Vérification des voyages ajoutés</h3>");
            String verifyQuery = "SELECT COUNT(*) FROM voyages v JOIN trajets t ON v.trajet_id = t.id " +
                               "JOIN gares gd ON t.gare_depart_id = gd.id " +
                               "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
                               "WHERE gd.ville = 'Tunis' AND ga.ville = 'Sousse' " +
                               "AND DATE(v.date_heure_depart) = '2025-05-30'";
            
            Long count = (Long) session.createNativeQuery(verifyQuery).getSingleResult();
            out.println("<div class='info'>Nombre de voyages Tunis → Sousse le 30/05/2025: " + count + "</div>");

        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            out.println("<div class='error'>Erreur: " + e.getMessage() + "</div>");
            e.printStackTrace();
        } finally {
            if (session != null) {
                session.close();
            }
        }

        out.println("<br><a href='" + request.getContextPath() + "/recherche/search'>Tester la recherche</a>");
        out.println("<br><a href='" + request.getContextPath() + "/'>Retour à l'accueil</a>");
        out.println("</body>");
        out.println("</html>");
    }
}
