package com.trainticket.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

@Entity
@Table(name = "demandes_annulation")
public class DemandeAnnulation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "billet_id", nullable = false)
    private Billet billet;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "utilisateur_id", nullable = false)
    private Utilisateur utilisateur;

    @Column(name = "motif_annulation", nullable = false, length = 1000)
    private String motifAnnulation;

    @Column(name = "date_demande", nullable = false)
    private LocalDateTime dateDemande;

    @Column(name = "date_traitement")
    private LocalDateTime dateTraitement;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private StatutDemande statut = StatutDemande.EN_ATTENTE;

    @Column(name = "commentaire_admin", length = 1000)
    private String commentaireAdmin;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "admin_id")
    private Utilisateur adminTraitant;

    @Column(name = "montant_remboursement", precision = 10, scale = 2)
    private BigDecimal montantRemboursement;

    @Column(name = "frais_annulation", precision = 10, scale = 2)
    private BigDecimal fraisAnnulation = BigDecimal.ZERO;

    // Constructeurs
    public DemandeAnnulation() {
        this.dateDemande = LocalDateTime.now();
    }

    public DemandeAnnulation(Billet billet, Utilisateur utilisateur, String motifAnnulation) {
        this();
        this.billet = billet;
        this.utilisateur = utilisateur;
        this.motifAnnulation = motifAnnulation;
        this.montantRemboursement = calculerMontantRemboursement();
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Billet getBillet() {
        return billet;
    }

    public void setBillet(Billet billet) {
        this.billet = billet;
    }

    public Utilisateur getUtilisateur() {
        return utilisateur;
    }

    public void setUtilisateur(Utilisateur utilisateur) {
        this.utilisateur = utilisateur;
    }

    public String getMotifAnnulation() {
        return motifAnnulation;
    }

    public void setMotifAnnulation(String motifAnnulation) {
        this.motifAnnulation = motifAnnulation;
    }

    public LocalDateTime getDateDemande() {
        return dateDemande;
    }

    public void setDateDemande(LocalDateTime dateDemande) {
        this.dateDemande = dateDemande;
    }

    public LocalDateTime getDateTraitement() {
        return dateTraitement;
    }

    public void setDateTraitement(LocalDateTime dateTraitement) {
        this.dateTraitement = dateTraitement;
    }

    public StatutDemande getStatut() {
        return statut;
    }

    public void setStatut(StatutDemande statut) {
        this.statut = statut;
    }

    public String getCommentaireAdmin() {
        return commentaireAdmin;
    }

    public void setCommentaireAdmin(String commentaireAdmin) {
        this.commentaireAdmin = commentaireAdmin;
    }

    public Utilisateur getAdminTraitant() {
        return adminTraitant;
    }

    public void setAdminTraitant(Utilisateur adminTraitant) {
        this.adminTraitant = adminTraitant;
    }

    public BigDecimal getMontantRemboursement() {
        return montantRemboursement;
    }

    public void setMontantRemboursement(BigDecimal montantRemboursement) {
        this.montantRemboursement = montantRemboursement;
    }

    public BigDecimal getFraisAnnulation() {
        return fraisAnnulation;
    }

    public void setFraisAnnulation(BigDecimal fraisAnnulation) {
        this.fraisAnnulation = fraisAnnulation;
    }

    // Méthodes utilitaires
    private BigDecimal calculerMontantRemboursement() {
        if (billet == null) return BigDecimal.ZERO;

        LocalDateTime maintenant = LocalDateTime.now();
        LocalDateTime dateVoyage = billet.getVoyage().getDateHeureDepart();

        // Calcul des frais selon le délai d'annulation
        long heuresAvantVoyage = java.time.Duration.between(maintenant, dateVoyage).toHours();

        BigDecimal prixBillet = billet.getPrix();
        BigDecimal frais = BigDecimal.ZERO;

        if (heuresAvantVoyage < 2) {
            // Moins de 2h : 50% de frais
            frais = prixBillet.multiply(new BigDecimal("0.50"));
        } else if (heuresAvantVoyage < 24) {
            // Moins de 24h : 25% de frais
            frais = prixBillet.multiply(new BigDecimal("0.25"));
        } else if (heuresAvantVoyage < 48) {
            // Moins de 48h : 10% de frais
            frais = prixBillet.multiply(new BigDecimal("0.10"));
        }
        // Plus de 48h : pas de frais

        this.fraisAnnulation = frais;
        return prixBillet.subtract(frais);
    }

    public void approuver(Utilisateur admin, String commentaire) {
        this.statut = StatutDemande.APPROUVEE;
        this.adminTraitant = admin;
        this.commentaireAdmin = commentaire;
        this.dateTraitement = LocalDateTime.now();

        // Annuler le billet
        if (billet != null) {
            billet.setStatut(Billet.StatutBillet.ANNULE);
            billet.setDateAnnulation(LocalDateTime.now());
        }
    }

    public void rejeter(Utilisateur admin, String commentaire) {
        this.statut = StatutDemande.REJETEE;
        this.adminTraitant = admin;
        this.commentaireAdmin = commentaire;
        this.dateTraitement = LocalDateTime.now();
        this.montantRemboursement = BigDecimal.ZERO;
    }

    public boolean peutEtreTraitee() {
        return statut == StatutDemande.EN_ATTENTE;
    }

    // Enums
    public enum StatutDemande {
        EN_ATTENTE, APPROUVEE, REJETEE, REMBOURSEE
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DemandeAnnulation that = (DemandeAnnulation) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "DemandeAnnulation{" +
                "id=" + id +
                ", billet=" + (billet != null ? billet.getNumeroBillet() : "null") +
                ", utilisateur=" + (utilisateur != null ? utilisateur.getNomComplet() : "null") +
                ", statut=" + statut +
                ", dateDemande=" + dateDemande +
                '}';
    }
}
