<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Train Ticket - Réservation de billets de train</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-train"></i> Train Ticket
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#recherche">Rechercher</a>
                <a class="nav-link" href="#trajets">Trajets</a>
                <a class="nav-link" href="#contact">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section bg-light py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold text-primary">Voyagez en toute simplicité</h1>
                    <p class="lead">Réservez vos billets de train en ligne rapidement et facilement.
                       Découvrez nos destinations et profitez d'un voyage confortable.</p>
                    <a href="#recherche" class="btn btn-primary btn-lg">
                        <i class="fas fa-search"></i> Rechercher un voyage
                    </a>
                </div>
                <div class="col-lg-6">
                    <img src="https://via.placeholder.com/600x400/007bff/ffffff?text=Train"
                         alt="Train" class="img-fluid rounded shadow">
                </div>
            </div>
        </div>
    </section>

    <!-- Formulaire de recherche -->
    <section id="recherche" class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="card shadow">
                        <div class="card-header bg-primary text-white">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-search"></i> Rechercher un voyage
                            </h3>
                        </div>
                        <div class="card-body">
                            <form action="recherche/search" method="post">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="villeDepart" class="form-label">Ville de départ</label>
                                        <select class="form-select" id="villeDepart" name="villeDepart" required>
                                            <option value="">Sélectionnez une ville</option>
                                            <option value="Tunis">Tunis</option>
                                            <option value="Sfax">Sfax</option>
                                            <option value="Sousse">Sousse</option>
                                            <option value="Kairouan">Kairouan</option>
                                            <option value="Bizerte">Bizerte</option>
                                            <option value="Gabès">Gabès</option>
                                            <option value="Ariana">Ariana</option>
                                            <option value="Gafsa">Gafsa</option>
                                            <option value="Monastir">Monastir</option>
                                            <option value="Ben Arous">Ben Arous</option>
                                            <option value="Kasserine">Kasserine</option>
                                            <option value="Médenine">Médenine</option>
                                        </select>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="villeArrivee" class="form-label">Ville d'arrivée</label>
                                        <select class="form-select" id="villeArrivee" name="villeArrivee" required>
                                            <option value="">Sélectionnez une ville</option>
                                            <option value="Tunis">Tunis</option>
                                            <option value="Sfax">Sfax</option>
                                            <option value="Sousse">Sousse</option>
                                            <option value="Kairouan">Kairouan</option>
                                            <option value="Bizerte">Bizerte</option>
                                            <option value="Gabès">Gabès</option>
                                            <option value="Ariana">Ariana</option>
                                            <option value="Gafsa">Gafsa</option>
                                            <option value="Monastir">Monastir</option>
                                            <option value="Ben Arous">Ben Arous</option>
                                            <option value="Kasserine">Kasserine</option>
                                            <option value="Médenine">Médenine</option>
                                        </select>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="dateVoyage" class="form-label">Date de voyage</label>
                                        <input type="date" class="form-control" id="dateVoyage" name="dateVoyage" required>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="nombrePassagers" class="form-label">Nombre de passagers</label>
                                        <select class="form-select" id="nombrePassagers" name="nombrePassagers">
                                            <option value="1">1 passager</option>
                                            <option value="2">2 passagers</option>
                                            <option value="3">3 passagers</option>
                                            <option value="4">4 passagers</option>
                                            <option value="5">5 passagers</option>
                                        </select>
                                    </div>

                                    <div class="col-12">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="voyagesDirects" name="voyagesDirects">
                                            <label class="form-check-label" for="voyagesDirects">
                                                Voyages directs uniquement
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary btn-lg w-100">
                                            <i class="fas fa-search"></i> Rechercher
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistiques -->
    <section class="py-5 bg-primary text-white">
        <div class="container">
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="stat-item">
                        <h3 class="display-4 fw-bold">25</h3>
                        <p class="lead">Gares desservies</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <h3 class="display-4 fw-bold">45</h3>
                        <p class="lead">Trajets disponibles</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <h3 class="display-4 fw-bold">12</h3>
                        <p class="lead">Voyages aujourd'hui</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <h3 class="display-4 fw-bold">12</h3>
                        <p class="lead">Villes connectées</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Trajets populaires -->
    <section id="trajets" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 mb-5 text-center">
                    <h2 class="display-5 fw-bold">Trajets populaires</h2>
                    <p class="lead">Découvrez les destinations les plus demandées</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-route text-primary"></i>
                                Tunis → Sfax
                            </h5>
                            <p class="card-text">
                                <strong>Durée:</strong> 180 minutes<br>
                                <strong>Type:</strong> <span class="badge bg-warning">DIRECT</span>
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="h5 text-primary">25.50 DT</span>
                                <small class="text-muted">à partir de</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-route text-primary"></i>
                                Tunis → Sousse
                            </h5>
                            <p class="card-text">
                                <strong>Durée:</strong> 120 minutes<br>
                                <strong>Type:</strong> <span class="badge bg-danger">EXPRESS</span>
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="h5 text-primary">18.00 DT</span>
                                <small class="text-muted">à partir de</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-route text-primary"></i>
                                Sfax → Gabès
                            </h5>
                            <p class="card-text">
                                <strong>Durée:</strong> 90 minutes<br>
                                <strong>Type:</strong> <span class="badge bg-secondary">REGIONAL</span>
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="h5 text-primary">12.75 DT</span>
                                <small class="text-muted">à partir de</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Avantages -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row text-center">
                <div class="col-12 mb-5">
                    <h2 class="display-5 fw-bold">Pourquoi choisir Train Ticket ?</h2>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="text-center">
                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                        <h4>Réservation rapide</h4>
                        <p>Réservez vos billets en quelques clics seulement. Interface simple et intuitive.</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                            <i class="fas fa-shield-alt fa-2x"></i>
                        </div>
                        <h4>Paiement sécurisé</h4>
                        <p>Vos transactions sont protégées par les dernières technologies de sécurité.</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                            <i class="fas fa-mobile-alt fa-2x"></i>
                        </div>
                        <h4>Billets électroniques</h4>
                        <p>Recevez vos billets instantanément par email. Plus besoin d'imprimer !</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-train"></i> Train Ticket</h5>
                    <p>Votre partenaire de confiance pour tous vos voyages en train.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; 2025 Train Ticket. Tous droits réservés.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Définir la date minimale à aujourd'hui
        document.getElementById('dateVoyage').min = new Date().toISOString().split('T')[0];

        // Empêcher la sélection de la même ville pour départ et arrivée
        document.getElementById('villeDepart').addEventListener('change', function() {
            const villeArrivee = document.getElementById('villeArrivee');
            const selectedValue = this.value;

            Array.from(villeArrivee.options).forEach(option => {
                option.disabled = option.value === selectedValue && option.value !== '';
            });
        });

        document.getElementById('villeArrivee').addEventListener('change', function() {
            const villeDepart = document.getElementById('villeDepart');
            const selectedValue = this.value;

            Array.from(villeDepart.options).forEach(option => {
                option.disabled = option.value === selectedValue && option.value !== '';
            });
        });
    </script>
</body>
</html>
