<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mes Demandes d'Annulation - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="${pageContext.request.contextPath}/resources/css/style.css" rel="stylesheet">
</head>
<body>
    <%@ include file="../common/navbar.jsp" %>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-times-circle text-warning"></i> Mes Demandes d'Annulation</h2>
                    <a href="${pageContext.request.contextPath}/user/profile" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> Retour au profil
                    </a>
                </div>

                <c:if test="${not empty error}">
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> ${error}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </c:if>

                <c:if test="${not empty success}">
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> ${success}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </c:if>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Historique des demandes d'annulation</h5>
                    </div>
                    <div class="card-body">
                        <c:choose>
                            <c:when test="${empty demandes}">
                                <div class="text-center py-5">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Aucune demande d'annulation</h5>
                                    <p class="text-muted">Vous n'avez pas encore fait de demande d'annulation.</p>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Billet</th>
                                                <th>Voyage</th>
                                                <th>Date demande</th>
                                                <th>Statut</th>
                                                <th>Montant remboursement</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <c:forEach var="demande" items="${demandes}">
                                                <tr>
                                                    <td>
                                                        <strong>${demande.billet.numeroBillet}</strong><br>
                                                        <small class="text-muted">${demande.billet.classeLibelle}</small>
                                                    </td>
                                                    <td>
                                                        <strong>${demande.billet.voyage.trajet.gareDepart.ville}</strong>
                                                        <i class="fas fa-arrow-right mx-2"></i>
                                                        <strong>${demande.billet.voyage.trajet.gareArrivee.ville}</strong><br>
                                                        <small class="text-muted">
                                                            <fmt:formatDate value="${demande.billet.voyage.dateHeureDepart}" 
                                                                          pattern="dd/MM/yyyy HH:mm"/>
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <fmt:formatDate value="${demande.dateDemande}" 
                                                                      pattern="dd/MM/yyyy HH:mm"/>
                                                    </td>
                                                    <td>
                                                        <c:choose>
                                                            <c:when test="${demande.statut == 'EN_ATTENTE'}">
                                                                <span class="badge bg-warning">
                                                                    <i class="fas fa-clock"></i> En attente
                                                                </span>
                                                            </c:when>
                                                            <c:when test="${demande.statut == 'APPROUVEE'}">
                                                                <span class="badge bg-success">
                                                                    <i class="fas fa-check"></i> Approuvée
                                                                </span>
                                                            </c:when>
                                                            <c:when test="${demande.statut == 'REJETEE'}">
                                                                <span class="badge bg-danger">
                                                                    <i class="fas fa-times"></i> Rejetée
                                                                </span>
                                                            </c:when>
                                                            <c:when test="${demande.statut == 'REMBOURSEE'}">
                                                                <span class="badge bg-info">
                                                                    <i class="fas fa-money-bill"></i> Remboursée
                                                                </span>
                                                            </c:when>
                                                        </c:choose>
                                                    </td>
                                                    <td>
                                                        <c:if test="${demande.montantRemboursement != null}">
                                                            <strong class="text-success">
                                                                <fmt:formatNumber value="${demande.montantRemboursement}" 
                                                                                type="currency" currencySymbol="DH"/>
                                                            </strong>
                                                            <c:if test="${demande.fraisAnnulation > 0}">
                                                                <br><small class="text-muted">
                                                                    Frais: <fmt:formatNumber value="${demande.fraisAnnulation}" 
                                                                                           type="currency" currencySymbol="DH"/>
                                                                </small>
                                                            </c:if>
                                                        </c:if>
                                                    </td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                                data-bs-toggle="modal" 
                                                                data-bs-target="#detailsModal${demande.id}">
                                                            <i class="fas fa-eye"></i> Détails
                                                        </button>
                                                    </td>
                                                </tr>

                                                <!-- Modal pour les détails -->
                                                <div class="modal fade" id="detailsModal${demande.id}" tabindex="-1">
                                                    <div class="modal-dialog modal-lg">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title">Détails de la demande d'annulation</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <div class="row">
                                                                    <div class="col-md-6">
                                                                        <h6>Informations du billet</h6>
                                                                        <p><strong>Numéro:</strong> ${demande.billet.numeroBillet}</p>
                                                                        <p><strong>Voyage:</strong> 
                                                                           ${demande.billet.voyage.trajet.gareDepart.ville} → 
                                                                           ${demande.billet.voyage.trajet.gareArrivee.ville}</p>
                                                                        <p><strong>Date:</strong> 
                                                                           <fmt:formatDate value="${demande.billet.voyage.dateHeureDepart}" 
                                                                                         pattern="dd/MM/yyyy HH:mm"/></p>
                                                                        <p><strong>Prix payé:</strong> 
                                                                           <fmt:formatNumber value="${demande.billet.prix}" 
                                                                                           type="currency" currencySymbol="DH"/></p>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <h6>Informations de la demande</h6>
                                                                        <p><strong>Date demande:</strong> 
                                                                           <fmt:formatDate value="${demande.dateDemande}" 
                                                                                         pattern="dd/MM/yyyy HH:mm"/></p>
                                                                        <p><strong>Statut:</strong> ${demande.statut}</p>
                                                                        <c:if test="${demande.dateTraitement != null}">
                                                                            <p><strong>Date traitement:</strong> 
                                                                               <fmt:formatDate value="${demande.dateTraitement}" 
                                                                                             pattern="dd/MM/yyyy HH:mm"/></p>
                                                                        </c:if>
                                                                    </div>
                                                                </div>
                                                                <div class="mt-3">
                                                                    <h6>Motif de l'annulation</h6>
                                                                    <p class="border p-3 bg-light">${demande.motifAnnulation}</p>
                                                                </div>
                                                                <c:if test="${not empty demande.commentaireAdmin}">
                                                                    <div class="mt-3">
                                                                        <h6>Commentaire de l'administrateur</h6>
                                                                        <p class="border p-3 bg-light">${demande.commentaireAdmin}</p>
                                                                    </div>
                                                                </c:if>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </c:forEach>
                                        </tbody>
                                    </table>
                                </div>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include file="../common/footer.jsp" %>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
