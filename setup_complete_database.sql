-- Script principal pour configurer complètement la base de données
-- Exécute tous les scripts de données dans le bon ordre

USE train_ticket_db;

-- ========================================
-- CONFIGURATION INITIALE
-- ========================================

-- Désactiver les vérifications de clés étrangères temporairement
SET FOREIGN_KEY_CHECKS = 0;
SET AUTOCOMMIT = 0;

-- Commencer une transaction
START TRANSACTION;

-- ========================================
-- NETTOYAGE COMPLET
-- ========================================

-- Supprimer toutes les données existantes
DELETE FROM billets;
DELETE FROM voyages;
DELETE FROM trajets;
DELETE FROM gares;
DELETE FROM utilisateurs;

-- Réinitialiser les compteurs auto-increment
ALTER TABLE billets AUTO_INCREMENT = 1;
ALTER TABLE voyages AUTO_INCREMENT = 1;
ALTER TABLE trajets AUTO_INCREMENT = 1;
ALTER TABLE gares AUTO_INCREMENT = 1;
ALTER TABLE utilisateurs AUTO_INCREMENT = 1;

-- ========================================
-- INSERTION DES GARES
-- ========================================

INSERT INTO gares (nom, ville, codeGare, adresse, latitude, longitude, active) VALUES
-- Nord de la Tunisie
('Gare Centrale de Tunis', 'Tunis', 'TUN01', 'Place Barcelone, Tunis', 36.8065, 10.1815, TRUE),
('Gare de Tunis Marine', 'Tunis', 'TUN02', 'Avenue Habib Bourguiba, Tunis', 36.8008, 10.1817, TRUE),
('Gare de La Marsa', 'La Marsa', 'MAR01', 'Avenue Habib Bourguiba, La Marsa', 36.8778, 10.3247, TRUE),
('Gare de Sidi Bou Said', 'Sidi Bou Said', 'SBS01', 'Place Sidi Bou Said', 36.8689, 10.3469, TRUE),
('Gare de Carthage', 'Carthage', 'CAR01', 'Avenue de Carthage', 36.8531, 10.3314, TRUE),

-- Centre-Est
('Gare de Sousse', 'Sousse', 'SOU01', 'Boulevard Hassouna Ayachi, Sousse', 35.8256, 10.6369, TRUE),
('Gare de Monastir', 'Monastir', 'MON01', 'Avenue de l\'Indépendance, Monastir', 35.7643, 10.8113, TRUE),
('Gare de Mahdia', 'Mahdia', 'MAH01', 'Avenue Ali Belhouane, Mahdia', 35.5047, 11.0622, TRUE),
('Gare de Sfax', 'Sfax', 'SFX01', 'Avenue Habib Bourguiba, Sfax', 34.7406, 10.7603, TRUE),

-- Sud
('Gare de Gabès', 'Gabès', 'GAB01', 'Avenue Farhat Hached, Gabès', 33.8815, 10.0982, TRUE),
('Gare de Gafsa', 'Gafsa', 'GAF01', 'Avenue Taieb Mehiri, Gafsa', 34.4250, 8.7842, TRUE),
('Gare de Tozeur', 'Tozeur', 'TOZ01', 'Avenue Abou El Kacem Chebbi, Tozeur', 33.9197, 8.1335, TRUE),

-- Nord-Ouest
('Gare de Béja', 'Béja', 'BEJ01', 'Avenue Habib Bourguiba, Béja', 36.7256, 9.1817, TRUE),
('Gare de Jendouba', 'Jendouba', 'JEN01', 'Avenue Habib Bourguiba, Jendouba', 36.5014, 8.7800, TRUE),
('Gare du Kef', 'Le Kef', 'KEF01', 'Avenue Bourguiba, Le Kef', 36.1699, 8.7047, TRUE),

-- Centre-Ouest
('Gare de Kairouan', 'Kairouan', 'KAI01', 'Avenue de la République, Kairouan', 35.6781, 10.0963, TRUE),
('Gare de Kasserine', 'Kasserine', 'KAS01', 'Avenue Habib Bourguiba, Kasserine', 35.1676, 8.8369, TRUE),
('Gare de Sidi Bouzid', 'Sidi Bouzid', 'SBZ01', 'Avenue 14 Janvier, Sidi Bouzid', 35.0381, 9.4858, TRUE),

-- Nord-Est
('Gare de Bizerte', 'Bizerte', 'BIZ01', 'Avenue Habib Bourguiba, Bizerte', 37.2744, 9.8739, TRUE),
('Gare de Nabeul', 'Nabeul', 'NAB01', 'Avenue Taieb Mehiri, Nabeul', 36.4560, 10.7376, TRUE),
('Gare de Hammamet', 'Hammamet', 'HAM01', 'Avenue de la Paix, Hammamet', 36.4000, 10.6167, TRUE);

-- ========================================
-- INSERTION DES TRAJETS
-- ========================================

INSERT INTO trajets (numeroTrajet, gare_depart_id, gare_arrivee_id, dureeMinutes, prixBase, capaciteTotal, type, description, active) VALUES

-- Trajets principaux depuis Tunis
('TUN-SOU-001', 1, 6, 120, 15.50, 200, 'EXPRESS', 'Train express Tunis - Sousse', TRUE),
('TUN-SFX-001', 1, 9, 180, 22.00, 200, 'EXPRESS', 'Train express Tunis - Sfax', TRUE),
('TUN-GAB-001', 1, 10, 300, 35.00, 180, 'DIRECT', 'Train direct Tunis - Gabès', TRUE),
('TUN-BIZ-001', 1, 19, 90, 12.00, 150, 'NORMAL', 'Train Tunis - Bizerte', TRUE),
('TUN-KAI-001', 1, 16, 150, 18.00, 180, 'NORMAL', 'Train Tunis - Kairouan', TRUE),
('TUN-BEJ-001', 1, 13, 120, 16.00, 150, 'NORMAL', 'Train Tunis - Béja', TRUE),
('TUN-JEN-001', 1, 14, 180, 24.00, 150, 'DIRECT', 'Train direct Tunis - Jendouba', TRUE),

-- Trajets depuis Sousse
('SOU-SFX-001', 6, 9, 90, 12.00, 180, 'NORMAL', 'Train Sousse - Sfax', TRUE),
('SOU-MON-001', 6, 7, 45, 8.00, 150, 'NORMAL', 'Train Sousse - Monastir', TRUE),
('SOU-MAH-001', 6, 8, 60, 10.00, 150, 'NORMAL', 'Train Sousse - Mahdia', TRUE),
('SOU-TUN-001', 6, 1, 120, 15.50, 200, 'EXPRESS', 'Train express Sousse - Tunis', TRUE),
('SOU-KAI-001', 6, 16, 75, 11.00, 150, 'NORMAL', 'Train Sousse - Kairouan', TRUE),

-- Trajets depuis Sfax
('SFX-GAB-001', 9, 10, 120, 16.00, 180, 'NORMAL', 'Train Sfax - Gabès', TRUE),
('SFX-GAF-001', 9, 11, 150, 20.00, 150, 'NORMAL', 'Train Sfax - Gafsa', TRUE),
('SFX-TUN-001', 9, 1, 180, 22.00, 200, 'EXPRESS', 'Train express Sfax - Tunis', TRUE),
('SFX-SOU-001', 9, 6, 90, 12.00, 180, 'NORMAL', 'Train Sfax - Sousse', TRUE),

-- Trajets depuis Gabès
('GAB-GAF-001', 10, 11, 90, 14.00, 150, 'NORMAL', 'Train Gabès - Gafsa', TRUE),
('GAB-TOZ-001', 10, 12, 120, 18.00, 120, 'NORMAL', 'Train Gabès - Tozeur', TRUE),
('GAB-TUN-001', 10, 1, 300, 35.00, 180, 'DIRECT', 'Train direct Gabès - Tunis', TRUE),
('GAB-SFX-001', 10, 9, 120, 16.00, 180, 'NORMAL', 'Train Gabès - Sfax', TRUE),

-- Trajets depuis Gafsa
('GAF-TOZ-001', 11, 12, 60, 12.00, 120, 'NORMAL', 'Train Gafsa - Tozeur', TRUE),
('GAF-KAS-001', 11, 17, 90, 14.00, 120, 'NORMAL', 'Train Gafsa - Kasserine', TRUE),
('GAF-SBZ-001', 11, 18, 120, 16.00, 120, 'NORMAL', 'Train Gafsa - Sidi Bouzid', TRUE),

-- Trajets côtiers
('NAB-HAM-001', 20, 21, 30, 6.00, 120, 'NORMAL', 'Train Nabeul - Hammamet', TRUE),
('HAM-SOU-001', 21, 6, 90, 12.00, 150, 'NORMAL', 'Train Hammamet - Sousse', TRUE),
('MON-MAH-001', 7, 8, 45, 8.00, 120, 'NORMAL', 'Train Monastir - Mahdia', TRUE),

-- Trajets Nord-Ouest
('BEJ-JEN-001', 13, 14, 90, 14.00, 120, 'NORMAL', 'Train Béja - Jendouba', TRUE),
('JEN-KEF-001', 14, 15, 60, 10.00, 120, 'NORMAL', 'Train Jendouba - Le Kef', TRUE),
('BEJ-KEF-001', 13, 15, 120, 16.00, 120, 'NORMAL', 'Train Béja - Le Kef', TRUE),

-- Trajets Centre
('KAI-SBZ-001', 16, 18, 90, 13.00, 120, 'NORMAL', 'Train Kairouan - Sidi Bouzid', TRUE),
('KAS-SBZ-001', 17, 18, 60, 10.00, 120, 'NORMAL', 'Train Kasserine - Sidi Bouzid', TRUE),

-- Trajets retour
('SFX-TUN-002', 9, 1, 180, 22.00, 200, 'EXPRESS', 'Train express Sfax - Tunis (retour)', TRUE),
('GAB-SFX-002', 10, 9, 120, 16.00, 180, 'NORMAL', 'Train Gabès - Sfax (retour)', TRUE),
('BIZ-TUN-001', 19, 1, 90, 12.00, 150, 'NORMAL', 'Train Bizerte - Tunis', TRUE),
('JEN-TUN-001', 14, 1, 180, 24.00, 150, 'DIRECT', 'Train direct Jendouba - Tunis', TRUE);

-- ========================================
-- INSERTION DES UTILISATEURS
-- ========================================

INSERT INTO utilisateurs (email, motDePasse, nom, prenom, telephone, adresse, role, statut, date_creation) VALUES
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Admin', 'Système', '+216 70 123 456', 'Siège Social, Tunis', 'ADMIN', 'ACTIF', NOW()),
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Ben Ali', 'Ahmed', '+216 98 123 456', 'Avenue Bourguiba, Tunis', 'USER', 'ACTIF', NOW()),
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Trabelsi', 'Fatma', '+216 97 234 567', 'Rue de la République, Sousse', 'USER', 'ACTIF', NOW()),
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Gharbi', 'Mohamed', '+216 96 345 678', 'Avenue Habib Bourguiba, Sfax', 'USER', 'ACTIF', NOW()),
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Test', 'Utilisateur', '+216 88 123 456', 'Adresse de test', 'USER', 'ACTIF', NOW());

-- Valider la transaction
COMMIT;

-- Réactiver les vérifications de clés étrangères
SET FOREIGN_KEY_CHECKS = 1;
SET AUTOCOMMIT = 1;

-- ========================================
-- STATISTIQUES FINALES
-- ========================================

SELECT '=== CONFIGURATION TERMINÉE ===' as message;
SELECT COUNT(*) as nombre_gares FROM gares;
SELECT COUNT(*) as nombre_trajets FROM trajets;
SELECT COUNT(*) as nombre_utilisateurs FROM utilisateurs;

SELECT 'Base de données configurée avec succès!' as message;
SELECT 'Vous pouvez maintenant exécuter generate_future_voyages.sql pour créer les voyages' as next_step;
