-- <PERSON><PERSON>t simplifié pour créer des voyages pour les prochains jours
USE train_ticket_db;

-- Vérifier la structure de la table voyages
DESCRIBE voyages;

-- Insérer des voyages pour aujourd'hui et les prochains jours
-- Trajets principaux avec plusieurs horaires

-- TUNIS - SOUSSE (ID trajet = 1)
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES
-- Aujourd'hui
(1, CONCAT(CURDATE(), ' 06:00:00'), CONCAT(CURDATE(), ' 08:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(CURDATE(), ' 09:30:00'), CONCAT(CURDATE(), ' 11:30:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(CURDATE(), ' 13:00:00'), CONCAT(CURDATE(), ' 15:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(CURDATE(), ' 16:30:00'), CONCAT(CURDATE(), ' 18:30:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(CURDATE(), ' 19:00:00'), CONCAT(CURDATE(), ' 21:00:00'), 200, 20, 60, 120, 'PROGRAMME'),

-- Demain
(1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 06:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 08:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 09:30:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 11:30:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 13:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 15:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 16:30:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 18:30:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 19:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 21:00:00'), 200, 20, 60, 120, 'PROGRAMME'),

-- Après-demain
(1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 06:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 08:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 09:30:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 11:30:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 13:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 15:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 16:30:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 18:30:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 19:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 21:00:00'), 200, 20, 60, 120, 'PROGRAMME'),

-- TUNIS - SFAX (ID trajet = 2)
(2, CONCAT(CURDATE(), ' 08:00:00'), CONCAT(CURDATE(), ' 11:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(2, CONCAT(CURDATE(), ' 15:00:00'), CONCAT(CURDATE(), ' 18:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(2, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 08:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 11:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(2, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 15:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 18:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(2, CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 08:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 11:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(2, CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 15:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 18:00:00'), 200, 20, 60, 120, 'PROGRAMME'),

-- SOUSSE - TUNIS (ID trajet = 11)
(11, CONCAT(CURDATE(), ' 07:00:00'), CONCAT(CURDATE(), ' 09:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(11, CONCAT(CURDATE(), ' 10:30:00'), CONCAT(CURDATE(), ' 12:30:00'), 200, 20, 60, 120, 'PROGRAMME'),
(11, CONCAT(CURDATE(), ' 14:00:00'), CONCAT(CURDATE(), ' 16:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(11, CONCAT(CURDATE(), ' 17:30:00'), CONCAT(CURDATE(), ' 19:30:00'), 200, 20, 60, 120, 'PROGRAMME'),
(11, CONCAT(CURDATE(), ' 20:00:00'), CONCAT(CURDATE(), ' 22:00:00'), 200, 20, 60, 120, 'PROGRAMME'),

-- SFAX - TUNIS (ID trajet = 15)
(15, CONCAT(CURDATE(), ' 09:00:00'), CONCAT(CURDATE(), ' 12:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(15, CONCAT(CURDATE(), ' 16:00:00'), CONCAT(CURDATE(), ' 19:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(15, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 09:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 12:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(15, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 16:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 19:00:00'), 200, 20, 60, 120, 'PROGRAMME'),

-- SOUSSE - SFAX (ID trajet = 8)
(8, CONCAT(CURDATE(), ' 10:00:00'), CONCAT(CURDATE(), ' 11:30:00'), 180, 18, 54, 108, 'PROGRAMME'),
(8, CONCAT(CURDATE(), ' 15:30:00'), CONCAT(CURDATE(), ' 17:00:00'), 180, 18, 54, 108, 'PROGRAMME'),
(8, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 10:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 11:30:00'), 180, 18, 54, 108, 'PROGRAMME'),
(8, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 15:30:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 17:00:00'), 180, 18, 54, 108, 'PROGRAMME'),

-- SFAX - SOUSSE (ID trajet = 16)
(16, CONCAT(CURDATE(), ' 12:00:00'), CONCAT(CURDATE(), ' 13:30:00'), 180, 18, 54, 108, 'PROGRAMME'),
(16, CONCAT(CURDATE(), ' 17:30:00'), CONCAT(CURDATE(), ' 19:00:00'), 180, 18, 54, 108, 'PROGRAMME'),
(16, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 12:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 13:30:00'), 180, 18, 54, 108, 'PROGRAMME'),
(16, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 17:30:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 19:00:00'), 180, 18, 54, 108, 'PROGRAMME'),

-- TUNIS - BIZERTE (ID trajet = 4)
(4, CONCAT(CURDATE(), ' 08:30:00'), CONCAT(CURDATE(), ' 10:00:00'), 150, 15, 45, 90, 'PROGRAMME'),
(4, CONCAT(CURDATE(), ' 14:00:00'), CONCAT(CURDATE(), ' 15:30:00'), 150, 15, 45, 90, 'PROGRAMME'),
(4, CONCAT(CURDATE(), ' 18:00:00'), CONCAT(CURDATE(), ' 19:30:00'), 150, 15, 45, 90, 'PROGRAMME'),

-- BIZERTE - TUNIS (ID trajet = 33)
(33, CONCAT(CURDATE(), ' 09:30:00'), CONCAT(CURDATE(), ' 11:00:00'), 150, 15, 45, 90, 'PROGRAMME'),
(33, CONCAT(CURDATE(), ' 15:00:00'), CONCAT(CURDATE(), ' 16:30:00'), 150, 15, 45, 90, 'PROGRAMME'),
(33, CONCAT(CURDATE(), ' 19:30:00'), CONCAT(CURDATE(), ' 21:00:00'), 150, 15, 45, 90, 'PROGRAMME'),

-- TUNIS - GABÈS (ID trajet = 3)
(3, CONCAT(CURDATE(), ' 07:30:00'), CONCAT(CURDATE(), ' 12:30:00'), 180, 18, 54, 108, 'PROGRAMME'),
(3, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 07:30:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 12:30:00'), 180, 18, 54, 108, 'PROGRAMME'),
(3, CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 07:30:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 12:30:00'), 180, 18, 54, 108, 'PROGRAMME'),

-- GABÈS - TUNIS (ID trajet = 19)
(19, CONCAT(CURDATE(), ' 14:00:00'), CONCAT(CURDATE(), ' 19:00:00'), 180, 18, 54, 108, 'PROGRAMME'),
(19, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 14:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 19:00:00'), 180, 18, 54, 108, 'PROGRAMME'),
(19, CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 14:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 19:00:00'), 180, 18, 54, 108, 'PROGRAMME'),

-- SOUSSE - MONASTIR (ID trajet = 9)
(9, CONCAT(CURDATE(), ' 09:00:00'), CONCAT(CURDATE(), ' 09:45:00'), 150, 15, 45, 90, 'PROGRAMME'),
(9, CONCAT(CURDATE(), ' 12:00:00'), CONCAT(CURDATE(), ' 12:45:00'), 150, 15, 45, 90, 'PROGRAMME'),
(9, CONCAT(CURDATE(), ' 16:00:00'), CONCAT(CURDATE(), ' 16:45:00'), 150, 15, 45, 90, 'PROGRAMME'),
(9, CONCAT(CURDATE(), ' 19:00:00'), CONCAT(CURDATE(), ' 19:45:00'), 150, 15, 45, 90, 'PROGRAMME'),

-- SOUSSE - MAHDIA (ID trajet = 10)
(10, CONCAT(CURDATE(), ' 11:00:00'), CONCAT(CURDATE(), ' 12:00:00'), 150, 15, 45, 90, 'PROGRAMME'),
(10, CONCAT(CURDATE(), ' 17:00:00'), CONCAT(CURDATE(), ' 18:00:00'), 150, 15, 45, 90, 'PROGRAMME'),

-- TUNIS - KAIROUAN (ID trajet = 5)
(5, CONCAT(CURDATE(), ' 09:00:00'), CONCAT(CURDATE(), ' 11:30:00'), 180, 18, 54, 108, 'PROGRAMME'),
(5, CONCAT(CURDATE(), ' 15:00:00'), CONCAT(CURDATE(), ' 17:30:00'), 180, 18, 54, 108, 'PROGRAMME'),

-- TUNIS - JENDOUBA (ID trajet = 7)
(7, CONCAT(CURDATE(), ' 08:00:00'), CONCAT(CURDATE(), ' 11:00:00'), 150, 15, 45, 90, 'PROGRAMME'),
(7, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 08:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 11:00:00'), 150, 15, 45, 90, 'PROGRAMME'),

-- JENDOUBA - TUNIS (ID trajet = 35)
(35, CONCAT(CURDATE(), ' 12:00:00'), CONCAT(CURDATE(), ' 15:00:00'), 150, 15, 45, 90, 'PROGRAMME'),
(35, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 12:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 15:00:00'), 150, 15, 45, 90, 'PROGRAMME');

-- Afficher les statistiques
SELECT 'Voyages créés avec succès!' as message;
SELECT COUNT(*) as total_voyages FROM voyages;
SELECT DATE(date_heure_depart) as date_voyage, COUNT(*) as nombre_voyages 
FROM voyages 
WHERE DATE(date_heure_depart) >= CURDATE() 
GROUP BY DATE(date_heure_depart) 
ORDER BY date_voyage 
LIMIT 5;
