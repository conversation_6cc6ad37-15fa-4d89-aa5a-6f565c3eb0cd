package com.trainticket.test;

import com.trainticket.service.RechercheService;
import com.trainticket.service.impl.RechercheServiceImpl;
import com.trainticket.dao.impl.VoyageDAOImpl;
import com.trainticket.dao.impl.TrajetDAOImpl;
import com.trainticket.dao.impl.GareDAOImpl;
import com.trainticket.model.Voyage;
import com.trainticket.model.Gare;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@WebServlet("/test/recherche-avancee")
public class TestRechercheAvanceeServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        response.setContentType("text/html; charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html><head><title>Test de Recherche Avancée</title>");
        out.println("<meta charset='UTF-8'>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 20px; }");
        out.println(".success { color: green; }");
        out.println(".error { color: red; }");
        out.println(".warning { color: orange; }");
        out.println("table { border-collapse: collapse; width: 100%; margin: 10px 0; }");
        out.println("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
        out.println("th { background-color: #f2f2f2; }");
        out.println(".test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }");
        out.println("</style>");
        out.println("</head><body>");
        out.println("<h1>Test de Recherche Avancée de Voyages</h1>");
        
        try {
            // Créer le service de recherche
            RechercheService rechercheService = new RechercheServiceImpl(
                new VoyageDAOImpl(),
                new TrajetDAOImpl(),
                new GareDAOImpl()
            );
            
            // Test 1: Vérifier les villes disponibles
            out.println("<div class='test-section'>");
            out.println("<h2>Test 1: Villes Disponibles</h2>");
            try {
                List<String> villes = rechercheService.getVillesDisponibles();
                out.println("<p class='success'>✅ " + villes.size() + " villes trouvées</p>");
                out.println("<ul>");
                for (String ville : villes) {
                    out.println("<li>" + ville + "</li>");
                }
                out.println("</ul>");
            } catch (Exception e) {
                out.println("<p class='error'>❌ Erreur: " + e.getMessage() + "</p>");
            }
            out.println("</div>");
            
            // Test 2: Recherche Tunis → Sousse
            out.println("<div class='test-section'>");
            out.println("<h2>Test 2: Recherche Tunis → Sousse</h2>");
            LocalDate dateTest = LocalDate.now().plusDays(1);
            out.println("<p><strong>Date de test:</strong> " + dateTest.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) + "</p>");
            
            try {
                List<Voyage> voyages = rechercheService.rechercherVoyages(
                    "Tunis", "Sousse", dateTest, 1, false
                );
                
                if (voyages.isEmpty()) {
                    out.println("<p class='warning'>⚠️ Aucun voyage trouvé</p>");
                    
                    // Tester avec d'autres dates
                    out.println("<h3>Test avec d'autres dates:</h3>");
                    for (int i = 0; i < 7; i++) {
                        LocalDate testDate = LocalDate.now().plusDays(i);
                        List<Voyage> testVoyages = rechercheService.rechercherVoyages(
                            "Tunis", "Sousse", testDate, 1, false
                        );
                        String status = testVoyages.isEmpty() ? "❌" : "✅";
                        out.println("<p>" + status + " " + testDate.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) + 
                                   ": " + testVoyages.size() + " voyage(s)</p>");
                    }
                } else {
                    out.println("<p class='success'>✅ " + voyages.size() + " voyage(s) trouvé(s)</p>");
                    afficherVoyages(out, voyages);
                }
                
            } catch (Exception e) {
                out.println("<p class='error'>❌ Erreur: " + e.getMessage() + "</p>");
                e.printStackTrace(out);
            }
            out.println("</div>");
            
            // Test 3: Recherche avec différentes villes
            out.println("<div class='test-section'>");
            out.println("<h2>Test 3: Recherche avec Différentes Villes</h2>");
            
            String[][] testCases = {
                {"Tunis", "Sfax"},
                {"Sfax", "Sousse"},
                {"Tunis", "Gabès"},
                {"Sousse", "Monastir"},
                {"Bizerte", "Tunis"}
            };
            
            for (String[] testCase : testCases) {
                String depart = testCase[0];
                String arrivee = testCase[1];
                
                try {
                    List<Voyage> voyages = rechercheService.rechercherVoyages(
                        depart, arrivee, dateTest, 1, false
                    );
                    
                    String status = voyages.isEmpty() ? "❌" : "✅";
                    out.println("<p>" + status + " " + depart + " → " + arrivee + ": " + 
                               voyages.size() + " voyage(s)</p>");
                    
                } catch (Exception e) {
                    out.println("<p class='error'>❌ " + depart + " → " + arrivee + ": Erreur - " + 
                               e.getMessage() + "</p>");
                }
            }
            out.println("</div>");
            
            // Test 4: Test avec nombre de passagers
            out.println("<div class='test-section'>");
            out.println("<h2>Test 4: Test avec Nombre de Passagers</h2>");
            
            for (int nbPassagers = 1; nbPassagers <= 5; nbPassagers++) {
                try {
                    List<Voyage> voyages = rechercheService.rechercherVoyages(
                        "Tunis", "Sousse", dateTest, nbPassagers, false
                    );
                    
                    String status = voyages.isEmpty() ? "❌" : "✅";
                    out.println("<p>" + status + " " + nbPassagers + " passager(s): " + 
                               voyages.size() + " voyage(s)</p>");
                    
                } catch (Exception e) {
                    out.println("<p class='error'>❌ " + nbPassagers + " passager(s): Erreur - " + 
                               e.getMessage() + "</p>");
                }
            }
            out.println("</div>");
            
        } catch (Exception e) {
            out.println("<div class='test-section'>");
            out.println("<h2 class='error'>Erreur Générale</h2>");
            out.println("<p class='error'>❌ " + e.getMessage() + "</p>");
            out.println("<pre>");
            e.printStackTrace(out);
            out.println("</pre>");
            out.println("</div>");
        }
        
        out.println("<hr>");
        out.println("<p><a href='../recherche/search'>Aller à la page de recherche</a></p>");
        out.println("<p><a href='fix-encoding'>Corriger l'encodage</a></p>");
        out.println("</body></html>");
    }
    
    private void afficherVoyages(PrintWriter out, List<Voyage> voyages) {
        out.println("<table>");
        out.println("<tr><th>Trajet</th><th>Départ</th><th>Arrivée</th><th>Prix</th><th>Places</th><th>Type</th></tr>");
        
        for (Voyage voyage : voyages) {
            out.println("<tr>");
            out.println("<td>" + voyage.getTrajet().getNumeroTrajet() + "</td>");
            out.println("<td>" + voyage.getDateHeureDepart() + "</td>");
            out.println("<td>" + voyage.getDateHeureArrivee() + "</td>");
            out.println("<td>" + voyage.getTrajet().getPrixBase() + " TND</td>");
            out.println("<td>" + voyage.getPlacesDisponibles() + "</td>");
            out.println("<td>" + voyage.getTrajet().getType() + "</td>");
            out.println("</tr>");
        }
        out.println("</table>");
    }
}
