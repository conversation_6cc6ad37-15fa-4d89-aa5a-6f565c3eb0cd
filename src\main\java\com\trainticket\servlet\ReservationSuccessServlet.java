package com.trainticket.servlet;

import com.trainticket.model.Billet;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * Servlet pour afficher la page de succès de réservation
 */
@WebServlet(name = "ReservationSuccessServlet", urlPatterns = {"/reservation/success"})
public class ReservationSuccessServlet extends HttpServlet {
    
    @Override
    public void init() throws ServletException {
        super.init();
        System.out.println("ReservationSuccessServlet initialisé");
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        try {
            HttpSession session = request.getSession();
            
            // Récupérer le billet confirmé de la session
            Billet billetConfirme = (Billet) session.getAttribute("billetConfirme");
            String successMessage = (String) session.getAttribute("successMessage");
            
            if (billetConfirme == null) {
                // Si pas de billet en session, rediriger vers l'accueil
                response.sendRedirect(request.getContextPath() + "/");
                return;
            }
            
            // Ajouter les données à la requête
            request.setAttribute("billetConfirme", billetConfirme);
            request.setAttribute("successMessage", successMessage);
            
            // Nettoyer la session (optionnel - on peut garder pour permettre le rafraîchissement)
            // session.removeAttribute("billetConfirme");
            // session.removeAttribute("successMessage");
            
            // Rediriger vers la page de succès
            request.getRequestDispatcher("/WEB-INF/views/reservation/success.jsp")
                   .forward(request, response);
            
        } catch (Exception e) {
            System.err.println("Erreur lors de l'affichage de la page de succès: " + e.getMessage());
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, 
                             "Erreur lors du chargement de la page de confirmation");
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        doGet(request, response);
    }
}
