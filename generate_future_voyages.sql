-- Script pour générer des voyages pour les 30 prochains jours
-- Partie 3: Génération automatique des voyages futurs

USE train_ticket_db;

-- ========================================
-- PROCÉDURE POUR GÉNÉRER DES VOYAGES FUTURS
-- ========================================

DELIMITER //

DROP PROCEDURE IF EXISTS GenerateVoyagesFuture//

CREATE PROCEDURE GenerateVoyagesFuture()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_trajet_id INT;
    DECLARE v_duree_minutes INT;
    DECLARE v_capacite_total INT;
    DECLARE v_date_counter INT DEFAULT 3; -- Commencer à J+3
    DECLARE v_max_days INT DEFAULT 30;
    
    -- Curseur pour parcourir les trajets principaux
    DECLARE trajet_cursor CURSOR FOR 
        SELECT id, duree_minutes, capacite_total 
        FROM trajets 
        WHERE active = TRUE 
        AND id IN (1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 32, 33);
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- Boucle pour chaque jour futur
    WHILE v_date_counter <= v_max_days DO
        
        SET done = FALSE;
        OPEN trajet_cursor;
        
        -- Boucle pour chaque trajet
        read_loop: LOOP
            FETCH trajet_cursor INTO v_trajet_id, v_duree_minutes, v_capacite_total;
            IF done THEN
                LEAVE read_loop;
            END IF;
            
            -- Générer des voyages selon le type de trajet
            CASE v_trajet_id
                -- Trajets principaux (Tunis-Sousse, Sousse-Tunis) : 5 voyages par jour
                WHEN 1, 11 THEN
                    INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 06:00:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('06:00:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME'),
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 09:30:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('09:30:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME'),
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 13:00:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('13:00:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME'),
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 16:30:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('16:30:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME'),
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 19:00:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('19:00:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME');
                
                -- Trajets express (Tunis-Sfax, Sfax-Tunis) : 3 voyages par jour
                WHEN 2, 14 THEN
                    INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 08:00:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('08:00:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME'),
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 13:00:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('13:00:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME'),
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 17:00:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('17:00:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME');
                
                -- Trajets longue distance (Tunis-Gabès, Gabès-Tunis) : 2 voyages par jour
                WHEN 3, 18 THEN
                    INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 07:30:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('07:30:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME'),
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 15:00:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('15:00:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME');
                
                -- Trajets régionaux : 3 voyages par jour
                WHEN 4, 5, 6, 9, 10, 15, 32, 33 THEN
                    INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 09:00:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('09:00:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME'),
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 14:00:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('14:00:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME'),
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 18:00:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('18:00:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME');
                
                -- Trajets spéciaux : 2 voyages par jour
                WHEN 7, 13, 16, 17, 19, 20 THEN
                    INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 10:00:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('10:00:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME'),
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 16:00:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('16:00:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME');
                
                -- Trajets courts : 4 voyages par jour
                ELSE
                    INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 08:00:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('08:00:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME'),
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 12:00:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('12:00:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME'),
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 16:00:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('16:00:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME'),
                    (v_trajet_id, CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' 19:00:00'), 
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL v_date_counter DAY), ' ', ADDTIME('19:00:00', SEC_TO_TIME(v_duree_minutes * 60))), 
                     v_capacite_total, ROUND(v_capacite_total * 0.1), ROUND(v_capacite_total * 0.3), ROUND(v_capacite_total * 0.6), 'PROGRAMME');
            END CASE;
            
        END LOOP;
        
        CLOSE trajet_cursor;
        SET v_date_counter = v_date_counter + 1;
        
    END WHILE;
    
END//

DELIMITER ;

-- Exécuter la procédure
CALL GenerateVoyagesFuture();

-- Afficher les statistiques
SELECT 'Génération des voyages futurs terminée!' as message;
SELECT COUNT(*) as total_voyages FROM voyages;
SELECT DATE(date_heure_depart) as date_voyage, COUNT(*) as nombre_voyages 
FROM voyages 
WHERE DATE(date_heure_depart) >= CURDATE() 
GROUP BY DATE(date_heure_depart) 
ORDER BY date_voyage 
LIMIT 10;

-- Supprimer la procédure temporaire
DROP PROCEDURE IF EXISTS GenerateVoyagesFuture;
