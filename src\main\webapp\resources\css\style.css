/* Train Ticket Management System - Styles CSS */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Styles généraux */
body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--dark-color);
}

.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.hero-section h1 {
    color: white !important;
}

/* Icônes de fonctionnalités */
.feature-icon {
    width: 80px;
    height: 80px;
    font-size: 2rem;
}

/* Cards personnalisées */
.card {
    border: none;
    border-radius: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border: none;
}

/* Formulaires */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Boutons */
.btn {
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
    border: none;
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
    border: none;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.dropdown-menu {
    border-radius: 10px;
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Tables */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr {
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Badges */
.badge {
    font-size: 0.8rem;
    padding: 0.5em 0.8em;
    border-radius: 20px;
}

/* Alertes */
.alert {
    border-radius: 10px;
    border: none;
}

.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-info {
    background: linear-gradient(45deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* Voyage cards */
.voyage-card {
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.voyage-card:hover {
    border-left-color: var(--success-color);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.voyage-time {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--primary-color);
}

.voyage-duration {
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.voyage-price {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--success-color);
}

/* Statuts */
.statut-achete {
    background-color: var(--success-color);
}

.statut-utilise {
    background-color: var(--info-color);
}

.statut-annule {
    background-color: var(--danger-color);
}

.statut-rembourse {
    background-color: var(--warning-color);
}

.statut-programme {
    background-color: var(--primary-color);
}

.statut-en-cours {
    background-color: var(--warning-color);
}

.statut-termine {
    background-color: var(--secondary-color);
}

/* Classes de voyage */
.classe-premiere {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #856404;
}

.classe-deuxieme {
    background: linear-gradient(45deg, #c0c0c0, #e5e5e5);
    color: #495057;
}

.classe-economique {
    background: linear-gradient(45deg, #cd7f32, #d4a574);
    color: #fff;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2rem;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .voyage-card {
        margin-bottom: 1rem;
    }

    .table-responsive {
        font-size: 0.9rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.6s ease-out;
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Sidebar admin */
.admin-sidebar {
    background: linear-gradient(180deg, #343a40, #495057);
    min-height: 100vh;
}

.admin-sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    transition: all 0.3s ease;
}

.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
    color: white;
    background-color: rgba(255,255,255,0.1);
    border-radius: 5px;
}

/* Dashboard cards */
.dashboard-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
}

.dashboard-card .card-body {
    padding: 2rem;
}

.dashboard-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
}

/* Préférences */
.preferences-section {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1rem 0;
}

.preference-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.preference-item i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

/* Utilitaires */
.text-muted-light {
    color: #6c757d !important;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.bg-gradient-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

.bg-gradient-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Styles pour la page d'accueil dynamique */

/* Section statistiques */
.stat-item {
    padding: 1rem;
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: scale(1.05);
}

.stat-item h3 {
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.stat-item p {
    margin-bottom: 0;
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Trajets populaires */
.trajet-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.trajet-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.trajet-card .card-body {
    padding: 1.5rem;
}

.trajet-card .card-title {
    color: var(--dark-color);
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.trajet-card .card-title i {
    margin-right: 0.5rem;
}

.trajet-route {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
}

.trajet-info {
    font-size: 0.9rem;
    line-height: 1.6;
}

.trajet-price {
    font-size: 1.3rem;
    font-weight: bold;
    color: var(--success-color);
}

/* Badges pour types de trajets */
.badge.bg-danger {
    background: linear-gradient(45deg, #dc3545, #c82333) !important;
}

.badge.bg-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800) !important;
    color: #212529 !important;
}

.badge.bg-secondary {
    background: linear-gradient(45deg, #6c757d, #545b62) !important;
}

/* Animation pour les statistiques */
@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-item h3 {
    animation: countUp 0.8s ease-out;
}

/* Styles pour l'administration */

/* Sidebar admin */
.admin-sidebar {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    min-height: 100vh;
    padding: 0;
}

.admin-sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 0.75rem 1rem;
    border-radius: 0;
    transition: all 0.3s ease;
}

.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
    color: white;
    background: rgba(255,255,255,0.1);
    transform: translateX(5px);
}

.admin-sidebar .nav-link i {
    width: 20px;
    margin-right: 0.5rem;
}

/* Cartes du tableau de bord */
.dashboard-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 15px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.dashboard-card .card-body {
    position: relative;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dashboard-card:hover::before {
    opacity: 1;
}

/* Alertes personnalisées */
.alert-sm {
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

/* Tableaux admin */
.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

/* Progress bars */
.progress {
    background-color: rgba(0,0,0,0.1);
}

.progress-bar {
    background: linear-gradient(45deg, #28a745, #20c997);
}

/* Animations pour les cartes admin */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-card {
    animation: slideInUp 0.6s ease-out;
}

/* Responsive pour les nouvelles sections */
@media (max-width: 768px) {
    .stat-item h3 {
        font-size: 2rem !important;
    }

    .stat-item p {
        font-size: 1rem;
    }

    .trajet-card {
        margin-bottom: 1rem;
    }

    .trajet-card .card-body {
        padding: 1rem;
    }

    .admin-sidebar {
        min-height: auto;
    }

    .dashboard-card {
        margin-bottom: 1rem;
    }

    .admin-sidebar .nav-link {
        text-align: center;
        padding: 0.5rem;
    }
}
