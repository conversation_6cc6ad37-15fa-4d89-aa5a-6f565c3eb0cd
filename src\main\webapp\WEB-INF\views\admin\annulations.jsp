<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Annulations - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="${pageContext.request.contextPath}/resources/css/style.css" rel="stylesheet">
</head>
<body>
    <%@ include file="../common/navbar.jsp" %>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar Admin -->
            <div class="col-md-2">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h6 class="mb-0"><i class="fas fa-cogs"></i> Administration</h6>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="${pageContext.request.contextPath}/admin/dashboard" 
                           class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt"></i> Tableau de bord
                        </a>
                        <a href="${pageContext.request.contextPath}/admin/annulations/list" 
                           class="list-group-item list-group-item-action active">
                            <i class="fas fa-times-circle"></i> Demandes d'annulation
                        </a>
                        <a href="${pageContext.request.contextPath}/admin/manage-users" 
                           class="list-group-item list-group-item-action">
                            <i class="fas fa-users"></i> Utilisateurs
                        </a>
                        <a href="${pageContext.request.contextPath}/admin/manage-voyages" 
                           class="list-group-item list-group-item-action">
                            <i class="fas fa-train"></i> Voyages
                        </a>
                    </div>
                </div>
            </div>

            <!-- Contenu principal -->
            <div class="col-md-10">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-times-circle text-warning"></i> Gestion des Demandes d'Annulation</h2>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="refreshPage()">
                            <i class="fas fa-sync-alt"></i> Actualiser
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="exportData()">
                            <i class="fas fa-download"></i> Exporter
                        </button>
                    </div>
                </div>

                <c:if test="${not empty error}">
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> ${error}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </c:if>

                <c:if test="${not empty success}">
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> ${success}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </c:if>

                <!-- Filtres -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="get" action="${pageContext.request.contextPath}/admin/annulations/list">
                            <div class="row">
                                <div class="col-md-3">
                                    <select class="form-select" name="statut">
                                        <option value="">Tous les statuts</option>
                                        <option value="EN_ATTENTE" ${statutFilter == 'EN_ATTENTE' ? 'selected' : ''}>En attente</option>
                                        <option value="APPROUVEE" ${statutFilter == 'APPROUVEE' ? 'selected' : ''}>Approuvées</option>
                                        <option value="REJETEE" ${statutFilter == 'REJETEE' ? 'selected' : ''}>Rejetées</option>
                                        <option value="REMBOURSEE" ${statutFilter == 'REMBOURSEE' ? 'selected' : ''}>Remboursées</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i> Filtrer
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Liste des demandes -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Demandes d'annulation</h5>
                    </div>
                    <div class="card-body">
                        <c:choose>
                            <c:when test="${empty demandes}">
                                <div class="text-center py-5">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Aucune demande d'annulation</h5>
                                    <p class="text-muted">Il n'y a pas de demandes d'annulation à traiter.</p>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>ID</th>
                                                <th>Utilisateur</th>
                                                <th>Billet</th>
                                                <th>Voyage</th>
                                                <th>Date demande</th>
                                                <th>Statut</th>
                                                <th>Montant</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <c:forEach var="demande" items="${demandes}">
                                                <tr>
                                                    <td><strong>#${demande.id}</strong></td>
                                                    <td>
                                                        <strong>${demande.utilisateur.nomComplet}</strong><br>
                                                        <small class="text-muted">${demande.utilisateur.email}</small>
                                                    </td>
                                                    <td>
                                                        <strong>${demande.billet.numeroBillet}</strong><br>
                                                        <small class="text-muted">${demande.billet.classeLibelle}</small>
                                                    </td>
                                                    <td>
                                                        <strong>${demande.billet.voyage.trajet.gareDepart.ville}</strong>
                                                        <i class="fas fa-arrow-right mx-1"></i>
                                                        <strong>${demande.billet.voyage.trajet.gareArrivee.ville}</strong><br>
                                                        <small class="text-muted">
                                                            <fmt:formatDate value="${demande.billet.voyage.dateHeureDepart}" 
                                                                          pattern="dd/MM/yyyy HH:mm"/>
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <fmt:formatDate value="${demande.dateDemande}" 
                                                                      pattern="dd/MM/yyyy HH:mm"/>
                                                    </td>
                                                    <td>
                                                        <c:choose>
                                                            <c:when test="${demande.statut == 'EN_ATTENTE'}">
                                                                <span class="badge bg-warning">
                                                                    <i class="fas fa-clock"></i> En attente
                                                                </span>
                                                            </c:when>
                                                            <c:when test="${demande.statut == 'APPROUVEE'}">
                                                                <span class="badge bg-success">
                                                                    <i class="fas fa-check"></i> Approuvée
                                                                </span>
                                                            </c:when>
                                                            <c:when test="${demande.statut == 'REJETEE'}">
                                                                <span class="badge bg-danger">
                                                                    <i class="fas fa-times"></i> Rejetée
                                                                </span>
                                                            </c:when>
                                                            <c:when test="${demande.statut == 'REMBOURSEE'}">
                                                                <span class="badge bg-info">
                                                                    <i class="fas fa-money-bill"></i> Remboursée
                                                                </span>
                                                            </c:when>
                                                        </c:choose>
                                                    </td>
                                                    <td>
                                                        <c:if test="${demande.montantRemboursement != null}">
                                                            <strong class="text-success">
                                                                <fmt:formatNumber value="${demande.montantRemboursement}" 
                                                                                type="currency" currencySymbol="DH"/>
                                                            </strong>
                                                            <c:if test="${demande.fraisAnnulation > 0}">
                                                                <br><small class="text-muted">
                                                                    Frais: <fmt:formatNumber value="${demande.fraisAnnulation}" 
                                                                                           type="currency" currencySymbol="DH"/>
                                                                </small>
                                                            </c:if>
                                                        </c:if>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <button type="button" class="btn btn-sm btn-outline-info" 
                                                                    onclick="showDetails(${demande.id})">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <c:if test="${demande.statut == 'EN_ATTENTE'}">
                                                                <button type="button" class="btn btn-sm btn-outline-success" 
                                                                        onclick="approveRequest(${demande.id})">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                        onclick="rejectRequest(${demande.id})">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                            </c:if>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </tbody>
                                    </table>
                                </div>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour approbation -->
    <div class="modal fade" id="approveModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">Approuver la demande</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="${pageContext.request.contextPath}/admin/annulations/approve" method="post">
                    <div class="modal-body">
                        <input type="hidden" id="approveDemandeId" name="demandeId">
                        <div class="mb-3">
                            <label for="approveCommentaire" class="form-label">Commentaire (optionnel)</label>
                            <textarea class="form-control" id="approveCommentaire" name="commentaire" rows="3"
                                    placeholder="Commentaire sur l'approbation..."></textarea>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            L'approbation de cette demande entraînera l'annulation du billet et le remboursement du montant calculé.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check"></i> Approuver
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal pour rejet -->
    <div class="modal fade" id="rejectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">Rejeter la demande</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="${pageContext.request.contextPath}/admin/annulations/reject" method="post">
                    <div class="modal-body">
                        <input type="hidden" id="rejectDemandeId" name="demandeId">
                        <div class="mb-3">
                            <label for="rejectCommentaire" class="form-label">Motif du rejet <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="rejectCommentaire" name="commentaire" rows="3"
                                    placeholder="Expliquez pourquoi cette demande est rejetée..." required></textarea>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            Le rejet de cette demande est définitif. Aucun remboursement ne sera effectué.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-times"></i> Rejeter
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <%@ include file="../common/footer.jsp" %>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showDetails(demandeId) {
            window.open('${pageContext.request.contextPath}/admin/annulations/details?id=' + demandeId, '_blank');
        }

        function approveRequest(demandeId) {
            document.getElementById('approveDemandeId').value = demandeId;
            new bootstrap.Modal(document.getElementById('approveModal')).show();
        }

        function rejectRequest(demandeId) {
            document.getElementById('rejectDemandeId').value = demandeId;
            new bootstrap.Modal(document.getElementById('rejectModal')).show();
        }

        function refreshPage() {
            location.reload();
        }

        function exportData() {
            alert('Fonctionnalité d\'export en cours de développement');
        }
    </script>
</body>
</html>
