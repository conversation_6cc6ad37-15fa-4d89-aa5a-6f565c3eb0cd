package com.trainticket.dao;

import com.trainticket.model.Billet;
import com.trainticket.model.Utilisateur;
import com.trainticket.model.Voyage;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface BilletDAO extends BaseDAO<Billet, Long> {

    /**
     * Trouver un billet par son numéro
     * @param numeroBillet le numéro du billet
     * @return Optional contenant le billet si trouvé
     */
    Optional<Billet> findByNumeroBillet(String numeroBillet);

    /**
     * Trouver les billets d'un utilisateur
     * @param utilisateur l'utilisateur
     * @return liste des billets de cet utilisateur
     */
    List<Billet> findByUtilisateur(Utilisateur utilisateur);

    /**
     * Trouver les billets d'un voyage
     * @param voyage le voyage
     * @return liste des billets pour ce voyage
     */
    List<Billet> findByVoyage(Voyage voyage);

    /**
     * Trouver les billets par statut
     * @param statut le statut du billet
     * @return liste des billets avec ce statut
     */
    List<Billet> findByStatut(Billet.StatutBillet statut);

    /**
     * Trouver les billets d'un utilisateur par statut
     * @param utilisateur l'utilisateur
     * @param statut le statut du billet
     * @return liste des billets de cet utilisateur avec ce statut
     */
    List<Billet> findByUtilisateurAndStatut(Utilisateur utilisateur, Billet.StatutBillet statut);

    /**
     * Trouver les billets achetés d'un utilisateur
     * @param utilisateur l'utilisateur
     * @return liste des billets achetés
     */
    List<Billet> findPurchasedBillets(Utilisateur utilisateur);

    /**
     * Trouver l'historique des billets d'un utilisateur
     * @param utilisateur l'utilisateur
     * @return liste de tous les billets de cet utilisateur
     */
    List<Billet> findUserHistory(Utilisateur utilisateur);

    /**
     * Trouver les billets par classe
     * @param classe la classe du billet
     * @return liste des billets de cette classe
     */
    List<Billet> findByClasse(Voyage.ClasseBillet classe);

    /**
     * Trouver les billets par date d'achat
     * @param date la date d'achat
     * @return liste des billets achetés à cette date
     */
    List<Billet> findByDateAchat(LocalDate date);

    /**
     * Trouver les billets dans une période
     * @param dateDebut date de début
     * @param dateFin date de fin
     * @return liste des billets achetés dans cette période
     */
    List<Billet> findByDateRange(LocalDateTime dateDebut, LocalDateTime dateFin);

    /**
     * Trouver les billets annulables
     * @param utilisateur l'utilisateur
     * @return liste des billets que l'utilisateur peut annuler
     */
    List<Billet> findCancellableBillets(Utilisateur utilisateur);

    /**
     * Trouver les billets utilisables
     * @param utilisateur l'utilisateur
     * @return liste des billets que l'utilisateur peut utiliser
     */
    List<Billet> findUsableBillets(Utilisateur utilisateur);

    /**
     * Compter les billets par statut
     * @param statut le statut
     * @return nombre de billets avec ce statut
     */
    long countByStatut(Billet.StatutBillet statut);

    /**
     * Compter les billets d'un utilisateur
     * @param utilisateur l'utilisateur
     * @return nombre de billets de cet utilisateur
     */
    long countByUtilisateur(Utilisateur utilisateur);

    /**
     * Compter les billets d'un voyage
     * @param voyage le voyage
     * @return nombre de billets pour ce voyage
     */
    long countByVoyage(Voyage voyage);

    /**
     * Vérifier si un numéro de billet existe
     * @param numeroBillet le numéro du billet
     * @return true si le billet existe, false sinon
     */
    boolean existsByNumeroBillet(String numeroBillet);

    /**
     * Calculer le chiffre d'affaires total
     * @return montant total des ventes
     */
    Double calculateTotalRevenue();

    /**
     * Calculer le chiffre d'affaires par période
     * @param dateDebut date de début
     * @param dateFin date de fin
     * @return montant des ventes dans cette période
     */
    Double calculateRevenueByPeriod(LocalDateTime dateDebut, LocalDateTime dateFin);

    /**
     * Trouver les billets avec pagination
     * @param page numéro de page (commence à 0)
     * @param size taille de la page
     * @return liste paginée des billets
     */
    List<Billet> findWithPagination(int page, int size);

    // Méthodes pour les statistiques financières
    Double calculateTodayRevenue();
    Double calculateMonthRevenue();
    List<Billet> findRecentBillets(int limit);
}
