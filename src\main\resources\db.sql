-- Script de création de la base de données pour le système de gestion de tickets de train

-- Création de la base de données
CREATE DATABASE IF NOT EXISTS train_ticket_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE train_ticket_db;

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS utilisateurs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(100) NOT NULL UNIQUE,
    mot_de_passe VARCHAR(255) NOT NULL,
    nom VARCHAR(50) NOT NULL,
    prenom VARCHAR(50) NOT NULL,
    telephone VARCHAR(15),
    adresse VARCHAR(200),
    role ENUM('USER', 'ADMIN') NOT NULL DEFAULT 'USER',
    statut ENUM('ACTIF', 'BLOQUE', 'SUSPENDU') NOT NULL DEFAULT 'ACTIF',
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    derniere_connexion DATETIME,
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_statut (statut)
);

-- Table des gares
CREATE TABLE IF NOT EXISTS gares (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL UNIQUE,
    ville VARCHAR(100) NOT NULL,
    code_gare VARCHAR(10),
    adresse VARCHAR(200),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    active BOOLEAN NOT NULL DEFAULT TRUE,
    INDEX idx_ville (ville),
    INDEX idx_code_gare (code_gare),
    INDEX idx_active (active)
);

-- Table des trajets
CREATE TABLE IF NOT EXISTS trajets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    numero_trajet VARCHAR(50) NOT NULL UNIQUE,
    gare_depart_id BIGINT NOT NULL,
    gare_arrivee_id BIGINT NOT NULL,
    duree_minutes INT NOT NULL,
    prix_base DECIMAL(10,2) NOT NULL,
    capacite_total INT NOT NULL DEFAULT 200,
    active BOOLEAN NOT NULL DEFAULT TRUE,
    type ENUM('NORMAL', 'DIRECT', 'EXPRESS') NOT NULL DEFAULT 'NORMAL',
    description VARCHAR(500),
    FOREIGN KEY (gare_depart_id) REFERENCES gares(id) ON DELETE RESTRICT,
    FOREIGN KEY (gare_arrivee_id) REFERENCES gares(id) ON DELETE RESTRICT,
    INDEX idx_numero_trajet (numero_trajet),
    INDEX idx_gare_depart (gare_depart_id),
    INDEX idx_gare_arrivee (gare_arrivee_id),
    INDEX idx_active (active),
    INDEX idx_type (type)
);

-- Table des voyages
CREATE TABLE IF NOT EXISTS voyages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    trajet_id BIGINT NOT NULL,
    date_heure_depart DATETIME NOT NULL,
    date_heure_arrivee DATETIME NOT NULL,
    places_disponibles INT NOT NULL,
    places_premiere_classe INT DEFAULT 20,
    places_deuxieme_classe INT DEFAULT 60,
    places_economique INT DEFAULT 120,
    statut ENUM('PROGRAMME', 'EN_COURS', 'TERMINE', 'ANNULE', 'RETARDE') NOT NULL DEFAULT 'PROGRAMME',
    commentaires VARCHAR(500),
    FOREIGN KEY (trajet_id) REFERENCES trajets(id) ON DELETE RESTRICT,
    INDEX idx_trajet (trajet_id),
    INDEX idx_date_depart (date_heure_depart),
    INDEX idx_statut (statut),
    INDEX idx_places_disponibles (places_disponibles)
);

-- Table des billets
CREATE TABLE IF NOT EXISTS billets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    numero_billet VARCHAR(50) NOT NULL UNIQUE,
    utilisateur_id BIGINT NOT NULL,
    voyage_id BIGINT NOT NULL,
    classe ENUM('PREMIERE', 'DEUXIEME', 'ECONOMIQUE') NOT NULL,
    prix DECIMAL(10,2) NOT NULL,
    statut ENUM('ACHETE', 'UTILISE', 'ANNULE', 'REMBOURSE') NOT NULL DEFAULT 'ACHETE',
    date_achat DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_utilisation DATETIME,
    date_annulation DATETIME,
    numero_siege VARCHAR(10),
    -- Préférences embarquées
    preference_fenetre ENUM('FENETRE', 'COULOIR', 'INDIFFERENT'),
    wagon_non_fumeur BOOLEAN DEFAULT TRUE,
    espace_famille BOOLEAN DEFAULT FALSE,
    acces_handicape BOOLEAN DEFAULT FALSE,
    wifi_requis BOOLEAN DEFAULT FALSE,
    prise_electrique BOOLEAN DEFAULT FALSE,
    commentaires_speciaux VARCHAR(200),
    commentaires VARCHAR(500),
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id) ON DELETE RESTRICT,
    FOREIGN KEY (voyage_id) REFERENCES voyages(id) ON DELETE RESTRICT,
    INDEX idx_numero_billet (numero_billet),
    INDEX idx_utilisateur (utilisateur_id),
    INDEX idx_voyage (voyage_id),
    INDEX idx_statut (statut),
    INDEX idx_date_achat (date_achat),
    INDEX idx_classe (classe)
);

-- Insertion de données de test

-- Insertion des gares tunisiennes
INSERT INTO gares (nom, ville, code_gare, adresse, latitude, longitude, active) VALUES
('Gare Centrale de Tunis', 'Tunis', 'TUN', 'Place Barcelone, Tunis', 36.8008, 10.1817, TRUE),
('Gare de Sfax', 'Sfax', 'SFX', 'Avenue Habib Bourguiba, Sfax', 34.7406, 10.7603, TRUE),
('Gare de Sousse', 'Sousse', 'SOU', 'Boulevard Hassouna Ayachi, Sousse', 35.8256, 10.6369, TRUE),
('Gare de Bizerte', 'Bizerte', 'BIZ', 'Avenue Habib Bourguiba, Bizerte', 37.2744, 9.8739, TRUE),
('Gare de Gabès', 'Gabès', 'GAB', 'Avenue Farhat Hached, Gabès', 33.8815, 10.0982, TRUE),
('Gare de Monastir', 'Monastir', 'MON', 'Avenue de la République, Monastir', 35.7643, 10.8113, TRUE),
('Gare de Kairouan', 'Kairouan', 'KAI', 'Avenue de la République, Kairouan', 35.6781, 10.0963, TRUE),
('Gare de Mahdia', 'Mahdia', 'MAH', 'Avenue 7 Novembre, Mahdia', 35.5047, 11.0622, TRUE),
('Gare de Nabeul', 'Nabeul', 'NAB', 'Avenue Habib Bourguiba, Nabeul', 36.4561, 10.7376, TRUE),
('Gare de Gafsa', 'Gafsa', 'GAF', 'Avenue Habib Bourguiba, Gafsa', 34.4250, 8.7842, TRUE),
('Gare de Béja', 'Béja', 'BEJ', 'Avenue de la Liberté, Béja', 36.7256, 9.1817, TRUE),
('Gare de Jendouba', 'Jendouba', 'JEN', 'Avenue Habib Bourguiba, Jendouba', 36.5014, 8.7800, TRUE);

-- Insertion des trajets tunisiens
INSERT INTO trajets (numero_trajet, gare_depart_id, gare_arrivee_id, duree_minutes, prix_base, capacite_total, type, description) VALUES
-- Trajets principaux Nord-Sud
('TN001', 1, 2, 180, 25.50, 200, 'EXPRESS', 'Express Tunis - Sfax'),
('TN002', 2, 1, 180, 25.50, 200, 'EXPRESS', 'Express Sfax - Tunis'),
('TN003', 1, 3, 120, 18.00, 180, 'DIRECT', 'Train direct Tunis - Sousse'),
('TN004', 3, 1, 120, 18.00, 180, 'DIRECT', 'Train direct Sousse - Tunis'),
('TN005', 1, 5, 240, 32.00, 160, 'NORMAL', 'Train Tunis - Gabès'),
('TN006', 5, 1, 240, 32.00, 160, 'NORMAL', 'Train Gabès - Tunis'),

-- Trajets côtiers
('TN101', 3, 2, 90, 15.00, 150, 'NORMAL', 'Train Sousse - Sfax'),
('TN102', 2, 3, 90, 15.00, 150, 'NORMAL', 'Train Sfax - Sousse'),
('TN103', 3, 6, 45, 8.50, 140, 'NORMAL', 'Train Sousse - Monastir'),
('TN104', 6, 3, 45, 8.50, 140, 'NORMAL', 'Train Monastir - Sousse'),
('TN105', 6, 8, 60, 12.00, 120, 'NORMAL', 'Train Monastir - Mahdia'),
('TN106', 8, 6, 60, 12.00, 120, 'NORMAL', 'Train Mahdia - Monastir'),

-- Trajets vers le Nord
('TN201', 1, 4, 90, 14.00, 160, 'NORMAL', 'Train Tunis - Bizerte'),
('TN202', 4, 1, 90, 14.00, 160, 'NORMAL', 'Train Bizerte - Tunis'),
('TN203', 1, 9, 75, 12.50, 140, 'DIRECT', 'Train direct Tunis - Nabeul'),
('TN204', 9, 1, 75, 12.50, 140, 'DIRECT', 'Train direct Nabeul - Tunis'),

-- Trajets vers l'intérieur
('TN301', 1, 7, 150, 22.00, 140, 'NORMAL', 'Train Tunis - Kairouan'),
('TN302', 7, 1, 150, 22.00, 140, 'NORMAL', 'Train Kairouan - Tunis'),
('TN303', 2, 10, 120, 18.50, 120, 'NORMAL', 'Train Sfax - Gafsa'),
('TN304', 10, 2, 120, 18.50, 120, 'NORMAL', 'Train Gafsa - Sfax'),

-- Trajets vers l'Ouest
('TN401', 1, 11, 180, 26.00, 130, 'NORMAL', 'Train Tunis - Béja'),
('TN402', 11, 1, 180, 26.00, 130, 'NORMAL', 'Train Béja - Tunis'),
('TN403', 11, 12, 90, 15.50, 110, 'NORMAL', 'Train Béja - Jendouba'),
('TN404', 12, 11, 90, 15.50, 110, 'NORMAL', 'Train Jendouba - Béja'),

-- Trajets régionaux Sud
('TN501', 2, 5, 90, 16.00, 120, 'NORMAL', 'Train Sfax - Gabès'),
('TN502', 5, 2, 90, 16.00, 120, 'NORMAL', 'Train Gabès - Sfax'),
('TN503', 5, 10, 150, 24.00, 100, 'NORMAL', 'Train Gabès - Gafsa'),
('TN504', 10, 5, 150, 24.00, 100, 'NORMAL', 'Train Gafsa - Gabès');

-- Insertion d'un utilisateur administrateur par défaut
INSERT INTO utilisateurs (email, mot_de_passe, nom, prenom, role, statut) VALUES
('<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBPdkKSMpAi/K2', 'Admin', 'System', 'ADMIN', 'ACTIF');
-- Mot de passe: Admin123!

-- Insertion d'un utilisateur test
INSERT INTO utilisateurs (email, mot_de_passe, nom, prenom, telephone, role, statut) VALUES
('<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBPdkKSMpAi/K2', 'Test', 'User', '0612345678', 'USER', 'ACTIF');
-- Mot de passe: Admin123!

-- Insertion de quelques voyages pour les tests (dates actualisées)
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES
-- Express Tunis - Sfax (TN001)
(1, '2025-05-27 06:00:00', '2025-05-27 09:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(1, '2025-05-27 10:00:00', '2025-05-27 13:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(1, '2025-05-27 15:00:00', '2025-05-27 18:00:00', 200, 20, 60, 120, 'PROGRAMME'),
-- Express Sfax - Tunis (TN002)
(2, '2025-05-27 07:30:00', '2025-05-27 10:30:00', 200, 20, 60, 120, 'PROGRAMME'),
(2, '2025-05-27 14:00:00', '2025-05-27 17:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(2, '2025-05-27 19:00:00', '2025-05-27 22:00:00', 200, 20, 60, 120, 'PROGRAMME'),
-- Direct Tunis - Sousse (TN003)
(3, '2025-05-27 08:00:00', '2025-05-27 10:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-05-27 12:00:00', '2025-05-27 14:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-05-27 16:30:00', '2025-05-27 18:30:00', 180, 18, 54, 108, 'PROGRAMME'),
-- Direct Sousse - Tunis (TN004)
(4, '2025-05-27 09:15:00', '2025-05-27 11:15:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-05-27 13:45:00', '2025-05-27 15:45:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-05-27 18:00:00', '2025-05-27 20:00:00', 180, 18, 54, 108, 'PROGRAMME'),
-- Tunis - Bizerte (TN201)
(13, '2025-05-27 07:00:00', '2025-05-27 08:30:00', 160, 16, 48, 96, 'PROGRAMME'),
(13, '2025-05-27 17:30:00', '2025-05-27 19:00:00', 160, 16, 48, 96, 'PROGRAMME'),
-- Tunis - Nabeul (TN203)
(15, '2025-05-27 08:30:00', '2025-05-27 09:45:00', 140, 14, 42, 84, 'PROGRAMME'),
(15, '2025-05-27 16:00:00', '2025-05-27 17:15:00', 140, 14, 42, 84, 'PROGRAMME'),
-- Sousse - Monastir (TN103)
(9, '2025-05-27 09:00:00', '2025-05-27 09:45:00', 140, 14, 42, 84, 'PROGRAMME'),
(9, '2025-05-27 14:30:00', '2025-05-27 15:15:00', 140, 14, 42, 84, 'PROGRAMME'),
(9, '2025-05-27 18:45:00', '2025-05-27 19:30:00', 140, 14, 42, 84, 'PROGRAMME');

-- Création des vues utiles

-- Vue pour les voyages avec informations complètes
CREATE OR REPLACE VIEW vue_voyages_complets AS
SELECT
    v.id as voyage_id,
    v.date_heure_depart,
    v.date_heure_arrivee,
    v.places_disponibles,
    v.statut as statut_voyage,
    t.numero_trajet,
    t.duree_minutes,
    t.prix_base,
    t.type as type_trajet,
    gd.nom as gare_depart_nom,
    gd.ville as ville_depart,
    ga.nom as gare_arrivee_nom,
    ga.ville as ville_arrivee
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE v.statut IN ('PROGRAMME', 'EN_COURS')
AND t.active = TRUE
AND gd.active = TRUE
AND ga.active = TRUE;

-- Vue pour les billets avec informations complètes
CREATE OR REPLACE VIEW vue_billets_complets AS
SELECT
    b.id as billet_id,
    b.numero_billet,
    b.classe,
    b.prix,
    b.statut as statut_billet,
    b.date_achat,
    b.numero_siege,
    u.nom as utilisateur_nom,
    u.prenom as utilisateur_prenom,
    u.email as utilisateur_email,
    v.date_heure_depart,
    v.date_heure_arrivee,
    t.numero_trajet,
    gd.nom as gare_depart_nom,
    gd.ville as ville_depart,
    ga.nom as gare_arrivee_nom,
    ga.ville as ville_arrivee
FROM billets b
JOIN utilisateurs u ON b.utilisateur_id = u.id
JOIN voyages v ON b.voyage_id = v.id
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id;
