package com.trainticket.test;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

@WebServlet("/mysql-diagnostic")
public class MySQLDiagnosticServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>🔧 Diagnostic MySQL</title>");
        out.println("<meta charset='UTF-8'>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }");
        out.println(".container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }");
        out.println(".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        out.println(".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        out.println(".info { color: #0066cc; background: #cce7ff; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        out.println(".warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        out.println("h1 { color: #333; text-align: center; }");
        out.println("h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }");
        out.println("pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }");
        out.println(".btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        out.println("<div class='container'>");
        out.println("<h1>🔧 Diagnostic de connexion MySQL</h1>");
        
        out.println("<h2>📋 Test des différentes configurations</h2>");
        
        String[] urls = {
            "********************************************************************************************************",
            "********************************************************************************************************&allowEmptyPasswords=true",
            "********************************************************************************************************&useUnicode=true&characterEncoding=UTF-8",
            "*************************************************************************************",
            "********************************************************************************************************"
        };
        
        String[] users = {"root"};
        String[] passwords = {"", "root", "password", "admin"};
        
        Connection workingConn = null;
        String workingConfig = "";
        
        for (String url : urls) {
            for (String user : users) {
                for (String password : passwords) {
                    try {
                        Class.forName("com.mysql.cj.jdbc.Driver");
                        Connection testConn = DriverManager.getConnection(url, user, password);
                        
                        out.println("<div class='success'>");
                        out.println("<h4>✅ CONNEXION RÉUSSIE!</h4>");
                        out.println("<p><strong>URL:</strong> " + url + "</p>");
                        out.println("<p><strong>Utilisateur:</strong> " + user + "</p>");
                        out.println("<p><strong>Mot de passe:</strong> " + (password.isEmpty() ? "(vide)" : "***") + "</p>");
                        out.println("</div>");
                        
                        if (workingConn == null) {
                            workingConn = testConn;
                            workingConfig = "URL: " + url + ", User: " + user + ", Password: " + (password.isEmpty() ? "(vide)" : "***");
                        } else {
                            testConn.close();
                        }
                        
                        break; // Sortir de la boucle des mots de passe
                        
                    } catch (Exception e) {
                        out.println("<div class='error'>");
                        out.println("<p><strong>❌ Échec:</strong> " + url + " | " + user + " | " + (password.isEmpty() ? "(vide)" : "***") + "</p>");
                        out.println("<p><strong>Erreur:</strong> " + e.getMessage() + "</p>");
                        out.println("</div>");
                    }
                }
                if (workingConn != null) break; // Sortir de la boucle des utilisateurs
            }
            if (workingConn != null) break; // Sortir de la boucle des URLs
        }
        
        if (workingConn != null) {
            try {
                out.println("<h2>🎉 Configuration fonctionnelle trouvée!</h2>");
                out.println("<div class='success'>");
                out.println("<p><strong>Configuration:</strong> " + workingConfig + "</p>");
                out.println("</div>");
                
                out.println("<h2>📊 Test de la base de données</h2>");
                
                Statement stmt = workingConn.createStatement();
                
                // Tester l'accès aux tables
                try {
                    ResultSet rs = stmt.executeQuery("SHOW TABLES");
                    out.println("<div class='info'>");
                    out.println("<h4>Tables disponibles:</h4>");
                    out.println("<ul>");
                    while (rs.next()) {
                        out.println("<li>" + rs.getString(1) + "</li>");
                    }
                    out.println("</ul>");
                    out.println("</div>");
                    rs.close();
                } catch (Exception e) {
                    out.println("<div class='error'>Erreur lors de la lecture des tables: " + e.getMessage() + "</div>");
                }
                
                // Tester l'accès aux gares
                try {
                    ResultSet rs = stmt.executeQuery("SELECT COUNT(*) as total FROM gares");
                    if (rs.next()) {
                        int total = rs.getInt("total");
                        out.println("<div class='info'>Nombre de gares dans la base: " + total + "</div>");
                        
                        if (total > 0) {
                            ResultSet rsGares = stmt.executeQuery("SELECT DISTINCT ville FROM gares ORDER BY ville");
                            out.println("<div class='info'>");
                            out.println("<h4>Villes actuelles:</h4>");
                            out.println("<ul>");
                            while (rsGares.next()) {
                                out.println("<li>" + rsGares.getString("ville") + "</li>");
                            }
                            out.println("</ul>");
                            out.println("</div>");
                            rsGares.close();
                        }
                    }
                    rs.close();
                } catch (Exception e) {
                    out.println("<div class='error'>Erreur lors de la lecture des gares: " + e.getMessage() + "</div>");
                }
                
                out.println("<h2>🔧 Code de connexion à utiliser</h2>");
                out.println("<div class='info'>");
                out.println("<h4>Utilisez cette configuration dans vos servlets:</h4>");
                out.println("<pre>");
                out.println("String url = \"" + workingConfig.split(", User:")[0].replace("URL: ", "") + "\";");
                out.println("String username = \"" + workingConfig.split(", User: ")[1].split(", Password:")[0] + "\";");
                String pwd = workingConfig.split(", Password: ")[1];
                out.println("String password = \"" + (pwd.equals("(vide)") ? "" : pwd.replace("***", "root")) + "\";");
                out.println("</pre>");
                out.println("</div>");
                
                stmt.close();
                workingConn.close();
                
            } catch (Exception e) {
                out.println("<div class='error'>Erreur lors du test de la base: " + e.getMessage() + "</div>");
            }
        } else {
            out.println("<div class='error'>");
            out.println("<h2>❌ Aucune configuration fonctionnelle trouvée!</h2>");
            out.println("<p>Vérifiez que:</p>");
            out.println("<ul>");
            out.println("<li>MySQL est démarré</li>");
            out.println("<li>La base 'train_ticket_db' existe</li>");
            out.println("<li>L'utilisateur 'root' a les bonnes permissions</li>");
            out.println("<li>Le port 3306 est accessible</li>");
            out.println("</ul>");
            out.println("</div>");
        }
        
        out.println("<p>");
        out.println("<a href='/train-ticket/' class='btn'>🏠 Accueil</a>");
        out.println("<a href='/train-ticket/simple-tunisia-update' class='btn'>🔄 Mise à jour</a>");
        out.println("<a href='/train-ticket/check-data' class='btn'>📊 Vérifier données</a>");
        out.println("</p>");
        out.println("</div>");
        out.println("</body>");
        out.println("</html>");
    }
}
