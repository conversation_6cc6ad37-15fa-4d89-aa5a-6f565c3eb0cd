package com.trainticket.dao.impl;

import com.trainticket.dao.TrajetDAO;
import com.trainticket.model.Trajet;
import com.trainticket.model.Gare;
import com.trainticket.util.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.query.Query;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public class TrajetDAOImpl extends BaseDAOImpl<Trajet, Long> implements TrajetDAO {
    
    @Override
    public Optional<Trajet> findByNumeroTrajet(String numeroTrajet) {
        String hql = "FROM Trajet t WHERE t.numeroTrajet = ?0";
        return executeSingleResultQuery(hql, numeroTrajet);
    }
    
    @Override
    public List<Trajet> findByGares(Gare gareDepart, Gare gareArrivee) {
        String hql = "FROM Trajet t WHERE t.gareDepart = ?0 AND t.gareArrivee = ?1 " +
                    "AND t.active = true ORDER BY t.dureeMinutes, t.prixBase";
        return executeQuery(hql, gareDepart, gareArrivee);
    }
    
    @Override
    public List<Trajet> findByVilles(String villeDepart, String villeArrivee) {
        String hql = "FROM Trajet t " +
                    "JOIN t.gareDepart gd " +
                    "JOIN t.gareArrivee ga " +
                    "WHERE LOWER(gd.ville) = LOWER(?0) " +
                    "AND LOWER(ga.ville) = LOWER(?1) " +
                    "AND t.active = true " +
                    "AND gd.active = true " +
                    "AND ga.active = true " +
                    "ORDER BY t.dureeMinutes, t.prixBase";
        return executeQuery(hql, villeDepart, villeArrivee);
    }
    
    @Override
    public List<Trajet> findByGareDepart(Gare gareDepart) {
        String hql = "FROM Trajet t WHERE t.gareDepart = ?0 AND t.active = true " +
                    "ORDER BY t.gareArrivee.ville, t.gareArrivee.nom";
        return executeQuery(hql, gareDepart);
    }
    
    @Override
    public List<Trajet> findByGareArrivee(Gare gareArrivee) {
        String hql = "FROM Trajet t WHERE t.gareArrivee = ?0 AND t.active = true " +
                    "ORDER BY t.gareDepart.ville, t.gareDepart.nom";
        return executeQuery(hql, gareArrivee);
    }
    
    @Override
    public List<Trajet> findByType(Trajet.TypeTrajet type) {
        String hql = "FROM Trajet t WHERE t.type = ?0 AND t.active = true " +
                    "ORDER BY t.gareDepart.ville, t.gareArrivee.ville";
        return executeQuery(hql, type);
    }
    
    @Override
    public List<Trajet> findActiveTrajets() {
        String hql = "FROM Trajet t WHERE t.active = true " +
                    "ORDER BY t.gareDepart.ville, t.gareArrivee.ville, t.numeroTrajet";
        return executeQuery(hql);
    }
    
    @Override
    public List<Trajet> findByPriceRange(BigDecimal prixMin, BigDecimal prixMax) {
        String hql = "FROM Trajet t WHERE t.prixBase BETWEEN ?0 AND ?1 " +
                    "AND t.active = true ORDER BY t.prixBase";
        return executeQuery(hql, prixMin, prixMax);
    }
    
    @Override
    public List<Trajet> findByMaxDuration(int dureeMaxMinutes) {
        String hql = "FROM Trajet t WHERE t.dureeMinutes <= ?0 AND t.active = true " +
                    "ORDER BY t.dureeMinutes";
        return executeQuery(hql, dureeMaxMinutes);
    }
    
    @Override
    public List<Trajet> searchTrajets(String searchTerm) {
        String hql = "FROM Trajet t WHERE " +
                    "LOWER(t.numeroTrajet) LIKE LOWER(?0) OR " +
                    "LOWER(t.description) LIKE LOWER(?0) OR " +
                    "LOWER(t.gareDepart.nom) LIKE LOWER(?0) OR " +
                    "LOWER(t.gareArrivee.nom) LIKE LOWER(?0) OR " +
                    "LOWER(t.gareDepart.ville) LIKE LOWER(?0) OR " +
                    "LOWER(t.gareArrivee.ville) LIKE LOWER(?0) " +
                    "ORDER BY t.numeroTrajet";
        String searchPattern = "%" + searchTerm + "%";
        return executeQuery(hql, searchPattern);
    }
    
    @Override
    public boolean existsByNumeroTrajet(String numeroTrajet) {
        String hql = "SELECT COUNT(t) FROM Trajet t WHERE t.numeroTrajet = ?0";
        return executeCountQuery(hql, numeroTrajet) > 0;
    }
    
    @Override
    public long countActiveTrajets() {
        String hql = "SELECT COUNT(t) FROM Trajet t WHERE t.active = true";
        return executeCountQuery(hql);
    }
    
    @Override
    public List<Trajet> findMostPopularTrajets(int limit) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "SELECT t FROM Trajet t " +
                        "LEFT JOIN t.voyages v " +
                        "LEFT JOIN v.billets b " +
                        "WHERE t.active = true " +
                        "GROUP BY t " +
                        "ORDER BY COUNT(b) DESC";
            
            Query<Trajet> query = session.createQuery(hql, Trajet.class);
            query.setMaxResults(limit);
            return query.getResultList();
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des trajets populaires", e);
            throw new RuntimeException("Erreur lors de la récupération", e);
        }
    }
    
    /**
     * Trouver les trajets directs entre deux villes
     * @param villeDepart ville de départ
     * @param villeArrivee ville d'arrivée
     * @return liste des trajets directs
     */
    public List<Trajet> findDirectTrajetsByVilles(String villeDepart, String villeArrivee) {
        String hql = "FROM Trajet t " +
                    "JOIN t.gareDepart gd " +
                    "JOIN t.gareArrivee ga " +
                    "WHERE LOWER(gd.ville) = LOWER(?0) " +
                    "AND LOWER(ga.ville) = LOWER(?1) " +
                    "AND t.type IN ('DIRECT', 'EXPRESS') " +
                    "AND t.active = true " +
                    "AND gd.active = true " +
                    "AND ga.active = true " +
                    "ORDER BY t.dureeMinutes, t.prixBase";
        return executeQuery(hql, villeDepart, villeArrivee);
    }
    
    /**
     * Compter les trajets par type
     * @param type le type de trajet
     * @return nombre de trajets de ce type
     */
    public long countByType(Trajet.TypeTrajet type) {
        String hql = "SELECT COUNT(t) FROM Trajet t WHERE t.type = ?0 AND t.active = true";
        return executeCountQuery(hql, type);
    }
    
    /**
     * Trouver les trajets les moins chers
     * @param limit nombre maximum de trajets à retourner
     * @return liste des trajets les moins chers
     */
    public List<Trajet> findCheapestTrajets(int limit) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "FROM Trajet t WHERE t.active = true ORDER BY t.prixBase ASC";
            Query<Trajet> query = session.createQuery(hql, Trajet.class);
            query.setMaxResults(limit);
            return query.getResultList();
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des trajets les moins chers", e);
            throw new RuntimeException("Erreur lors de la récupération", e);
        }
    }
    
    /**
     * Trouver les trajets les plus rapides
     * @param limit nombre maximum de trajets à retourner
     * @return liste des trajets les plus rapides
     */
    public List<Trajet> findFastestTrajets(int limit) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "FROM Trajet t WHERE t.active = true ORDER BY t.dureeMinutes ASC";
            Query<Trajet> query = session.createQuery(hql, Trajet.class);
            query.setMaxResults(limit);
            return query.getResultList();
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des trajets les plus rapides", e);
            throw new RuntimeException("Erreur lors de la récupération", e);
        }
    }
    
    /**
     * Mettre à jour le statut actif d'un trajet
     * @param trajetId l'ID du trajet
     * @param active le nouveau statut
     */
    public void updateActiveStatus(Long trajetId, boolean active) {
        String hql = "UPDATE Trajet t SET t.active = ?0 WHERE t.id = ?1";
        executeUpdateQuery(hql, active, trajetId);
    }
    
    /**
     * Calculer la distance moyenne des trajets
     * @return distance moyenne en minutes
     */
    public Double calculateAverageDuration() {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "SELECT AVG(t.dureeMinutes) FROM Trajet t WHERE t.active = true";
            Query<Double> query = session.createQuery(hql, Double.class);
            return query.getSingleResult();
        } catch (Exception e) {
            logger.error("Erreur lors du calcul de la durée moyenne", e);
            return 0.0;
        }
    }
    
    /**
     * Calculer le prix moyen des trajets
     * @return prix moyen
     */
    public BigDecimal calculateAveragePrice() {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "SELECT AVG(t.prixBase) FROM Trajet t WHERE t.active = true";
            Query<BigDecimal> query = session.createQuery(hql, BigDecimal.class);
            BigDecimal result = query.getSingleResult();
            return result != null ? result : BigDecimal.ZERO;
        } catch (Exception e) {
            logger.error("Erreur lors du calcul du prix moyen", e);
            return BigDecimal.ZERO;
        }
    }
}
