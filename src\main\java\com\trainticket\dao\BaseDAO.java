package com.trainticket.dao;

import java.util.List;
import java.util.Optional;

/**
 * Interface de base pour tous les DAO
 * @param <T> Type de l'entité
 * @param <ID> Type de l'identifiant
 */
public interface BaseDAO<T, ID> {
    
    /**
     * Sauvegarder une entité
     * @param entity l'entité à sauvegarder
     * @return l'entité sauvegardée
     */
    T save(T entity);
    
    /**
     * Mettre à jour une entité
     * @param entity l'entité à mettre à jour
     * @return l'entité mise à jour
     */
    T update(T entity);
    
    /**
     * Supprimer une entité par son ID
     * @param id l'identifiant de l'entité
     */
    void deleteById(ID id);
    
    /**
     * Supprimer une entité
     * @param entity l'entité à supprimer
     */
    void delete(T entity);
    
    /**
     * Trouver une entité par son ID
     * @param id l'identifiant de l'entité
     * @return Optional contenant l'entité si trouvée
     */
    Optional<T> findById(ID id);
    
    /**
     * Trouver toutes les entités
     * @return liste de toutes les entités
     */
    List<T> findAll();
    
    /**
     * Compter le nombre total d'entités
     * @return le nombre d'entités
     */
    long count();
    
    /**
     * Vérifier si une entité existe par son ID
     * @param id l'identifiant de l'entité
     * @return true si l'entité existe, false sinon
     */
    boolean existsById(ID id);
}
