package com.trainticket.servlet;

import com.trainticket.dao.BilletDAO;
import com.trainticket.dao.impl.BilletDAOImpl;
import com.trainticket.model.Billet;
import com.trainticket.model.Utilisateur;
import com.trainticket.service.PdfService;
import com.trainticket.service.impl.PdfServiceImpl;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Optional;

@WebServlet("/user/download-ticket/*")
public class PdfDownloadServlet extends HttpServlet {

    private final BilletDAO billetDAO;
    private final PdfService pdfService;

    public PdfDownloadServlet() {
        this.billetDAO = new BilletDAOImpl();
        this.pdfService = new PdfServiceImpl();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("utilisateur") == null) {
            response.sendRedirect(request.getContextPath() + "/auth/login");
            return;
        }

        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");
        String pathInfo = request.getPathInfo();

        if (pathInfo == null || pathInfo.length() <= 1) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID du billet manquant");
            return;
        }

        try {
            // Extraire l'ID du billet depuis l'URL
            String billetIdStr = pathInfo.substring(1); // Enlever le '/' initial
            Long billetId = Long.parseLong(billetIdStr);

            // Récupérer le billet
            Optional<Billet> optionalBillet = billetDAO.findById(billetId);
            if (!optionalBillet.isPresent()) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "Billet non trouvé");
                return;
            }

            Billet billet = optionalBillet.get();

            // Vérifier que le billet appartient à l'utilisateur connecté
            if (!billet.getUtilisateur().getId().equals(utilisateur.getId())) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès non autorisé à ce billet");
                return;
            }

            // Vérifier que le billet est dans un état téléchargeable
            if (billet.getStatut() == Billet.StatutBillet.ANNULE) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Ce billet a été annulé");
                return;
            }

            // Générer le PDF
            byte[] pdfBytes = pdfService.genererBilletPdf(billet);

            // Configurer la réponse pour le téléchargement
            response.setContentType("application/pdf");
            response.setContentLength(pdfBytes.length);
            
            String fileName = "billet_" + billet.getNumeroBillet() + ".pdf";
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            // Écrire le PDF dans la réponse
            response.getOutputStream().write(pdfBytes);
            response.getOutputStream().flush();

        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID du billet invalide");
        } catch (Exception e) {
            System.err.println("Erreur lors de la génération du PDF: " + e.getMessage());
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, 
                             "Erreur lors de la génération du PDF");
        }
    }
}
