Write-Host "========================================" -ForegroundColor Green
Write-Host "    TRAIN TICKET MANAGEMENT SYSTEM" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "Démarrage du serveur Jetty..." -ForegroundColor Yellow
Write-Host ""

# Arrêter tous les processus Java existants
try {
    Get-Process -Name "java" -ErrorAction SilentlyContinue | Stop-Process -Force
    Write-Host "Processus Java existants arrêtés." -ForegroundColor Blue
} catch {
    Write-Host "Aucun processus Java à arrêter." -ForegroundColor Blue
}

Start-Sleep -Seconds 2

# Démarrer le serveur
Write-Host "Compilation et démarrage..." -ForegroundColor Yellow
& .\mvnw.cmd jetty:run

Write-Host ""
Write-Host "Le serveur est maintenant arrêté." -ForegroundColor Red
Read-Host "Appuyez sur Entrée pour continuer"
