<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<%-- Chargement des données dynamiques si pas déjà présentes --%>
<c:if test="${empty conseilsSecurite}">
    <jsp:useBean id="authController" class="com.trainticket.controller.AuthController" scope="request" />
    <%
        // Charger les données si elles ne sont pas déjà présentes
        com.trainticket.controller.AuthController controller =
            (com.trainticket.controller.AuthController) pageContext.getAttribute("authController");
        controller.loadProfileData(request);
    %>
</c:if>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mon Profil - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="${pageContext.request.contextPath}/resources/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <jsp:include page="../common/navbar.jsp" />

    <div class="container my-5">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="avatar-circle bg-primary text-white mx-auto mb-3">
                            <i class="fas fa-user fa-3x"></i>
                        </div>
                        <h5 class="card-title">${utilisateur.prenom} ${utilisateur.nom}</h5>
                        <p class="text-muted">${utilisateur.email}</p>
                        <span class="badge bg-${utilisateur.statut == 'ACTIF' ? 'success' : 'warning'}">
                            ${utilisateur.statut}
                        </span>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Statistiques</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Réservations actives</span>
                            <span class="badge bg-primary">${nombreReservationsActives}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Voyages effectués</span>
                            <span class="badge bg-success">${nombreVoyagesEffectues}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Membre depuis</span>
                            <small class="text-muted">
                                <fmt:formatDate value="${utilisateur.dateCreation}" pattern="MMM yyyy" />
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contenu principal -->
            <div class="col-lg-9">
                <!-- En-tête -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-user-circle"></i> Mon Profil</h2>
                    <div>
                        <a href="${pageContext.request.contextPath}/user/edit-profile" class="btn btn-outline-primary">
                            <i class="fas fa-edit"></i> Modifier
                        </a>
                        <a href="${pageContext.request.contextPath}/user/change-password" class="btn btn-outline-secondary">
                            <i class="fas fa-key"></i> Mot de passe
                        </a>
                    </div>
                </div>

                <!-- Informations personnelles -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-id-card"></i> Informations personnelles</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Prénom</label>
                                <p class="fw-bold">${utilisateur.prenom}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Nom</label>
                                <p class="fw-bold">${utilisateur.nom}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Email</label>
                                <p class="fw-bold">
                                    <i class="fas fa-envelope text-primary"></i> ${utilisateur.email}
                                </p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Téléphone</label>
                                <p class="fw-bold">
                                    <c:choose>
                                        <c:when test="${not empty utilisateur.telephone}">
                                            <i class="fas fa-phone text-success"></i> ${utilisateur.telephone}
                                        </c:when>
                                        <c:otherwise>
                                            <span class="text-muted">Non renseigné</span>
                                        </c:otherwise>
                                    </c:choose>
                                </p>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label text-muted">Adresse</label>
                                <p class="fw-bold">
                                    <c:choose>
                                        <c:when test="${not empty utilisateur.adresse}">
                                            <i class="fas fa-map-marker-alt text-info"></i> ${utilisateur.adresse}
                                        </c:when>
                                        <c:otherwise>
                                            <span class="text-muted">Non renseignée</span>
                                        </c:otherwise>
                                    </c:choose>
                                </p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Rôle</label>
                                <p>
                                    <span class="badge bg-${utilisateur.role == 'ADMIN' ? 'danger' : 'primary'}">
                                        <i class="fas fa-${utilisateur.role == 'ADMIN' ? 'crown' : 'user'}"></i>
                                        ${utilisateur.role}
                                    </span>
                                </p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Dernière connexion</label>
                                <p class="fw-bold">
                                    <c:choose>
                                        <c:when test="${not empty utilisateur.derniereConnexion}">
                                            <i class="fas fa-clock text-warning"></i>
                                            <fmt:formatDate value="${utilisateur.derniereConnexion}"
                                                          pattern="dd/MM/yyyy à HH:mm" />
                                        </c:when>
                                        <c:otherwise>
                                            <span class="text-muted">Première connexion</span>
                                        </c:otherwise>
                                    </c:choose>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions rapides -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bolt"></i> Actions rapides</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <a href="${pageContext.request.contextPath}/recherche/search"
                                   class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <i class="fas fa-search fa-2x mb-2"></i>
                                    <span>Rechercher un voyage</span>
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="${pageContext.request.contextPath}/user/reservations"
                                   class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <i class="fas fa-ticket-alt fa-2x mb-2"></i>
                                    <span>Mes réservations</span>
                                    <c:if test="${nombreReservationsActives > 0}">
                                        <span class="badge bg-success mt-1">${nombreReservationsActives}</span>
                                    </c:if>
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="${pageContext.request.contextPath}/user/history"
                                   class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <i class="fas fa-history fa-2x mb-2"></i>
                                    <span>Historique</span>
                                </a>
                            </div>
                        </div>
                        <div class="row g-3 mt-2">
                            <div class="col-md-4">
                                <a href="${pageContext.request.contextPath}/user/annulation/list"
                                   class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <i class="fas fa-times-circle fa-2x mb-2"></i>
                                    <span>Demandes d'annulation</span>
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="${pageContext.request.contextPath}/user/tickets"
                                   class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <i class="fas fa-download fa-2x mb-2"></i>
                                    <span>Télécharger billets</span>
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="${pageContext.request.contextPath}/user/preferences"
                                   class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <i class="fas fa-cog fa-2x mb-2"></i>
                                    <span>Préférences</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Préférences -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cog"></i> Préférences</h5>
                    </div>
                    <div class="card-body">
                        <form id="preferencesForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                        <label class="form-check-label" for="emailNotifications">
                                            Notifications par email
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="smsNotifications">
                                        <label class="form-check-label" for="smsNotifications">
                                            Notifications par SMS
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="promotionalEmails" checked>
                                        <label class="form-check-label" for="promotionalEmails">
                                            Offres promotionnelles
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="travelReminders" checked>
                                        <label class="form-check-label" for="travelReminders">
                                            Rappels de voyage
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Sauvegarder les préférences
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Sécurité -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-shield-alt"></i> Sécurité</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Mot de passe</h6>
                                <p class="text-muted">Dernière modification :
                                    <span class="fw-bold">Il y a 30 jours</span>
                                </p>
                                <a href="${pageContext.request.contextPath}/user/change-password"
                                   class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-key"></i> Changer le mot de passe
                                </a>
                            </div>
                            <div class="col-md-6">
                                <h6>Sessions actives</h6>
                                <p class="text-muted">Vous êtes connecté sur
                                    <span class="fw-bold">1 appareil</span>
                                </p>
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="logoutAllSessions()">
                                    <i class="fas fa-sign-out-alt"></i> Déconnecter tous les appareils
                                </button>
                            </div>
                        </div>

                        <!-- Conseils de sécurité -->
                        <c:if test="${not empty conseilsSecurite}">
                            <hr class="my-4">
                            <h6><i class="fas fa-lightbulb text-warning"></i> Conseils de sécurité</h6>
                            <div class="row">
                                <c:forEach var="conseil" items="${conseilsSecurite}" varStatus="status">
                                    <div class="col-md-6 mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-shield-alt text-primary me-1"></i>
                                            ${conseil}
                                        </small>
                                    </div>
                                </c:forEach>
                            </div>
                        </c:if>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <jsp:include page="../common/footer.jsp" />

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/main.js"></script>

    <script>
        // Gestion du formulaire de préférences
        document.getElementById('preferencesForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Simulation de sauvegarde
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sauvegarde...';
            submitBtn.disabled = true;

            setTimeout(() => {
                submitBtn.innerHTML = '<i class="fas fa-check"></i> Sauvegardé !';
                submitBtn.classList.remove('btn-primary');
                submitBtn.classList.add('btn-success');

                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.classList.remove('btn-success');
                    submitBtn.classList.add('btn-primary');
                    submitBtn.disabled = false;
                }, 2000);
            }, 1000);
        });

        // Fonction pour déconnecter toutes les sessions
        function logoutAllSessions() {
            if (confirm('Êtes-vous sûr de vouloir déconnecter tous les appareils ?')) {
                // Simulation de déconnexion
                alert('Toutes les sessions ont été fermées. Vous allez être redirigé.');
                window.location.href = '${pageContext.request.contextPath}/auth/logout';
            }
        }

        // Animation pour les cartes
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>

    <style>
        .avatar-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .btn-outline-primary:hover,
        .btn-outline-success:hover,
        .btn-outline-info:hover {
            transform: translateY(-1px);
        }
    </style>
</body>
</html>
