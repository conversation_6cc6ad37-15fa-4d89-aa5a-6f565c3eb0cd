package com.trainticket.dao;

import com.trainticket.model.Trajet;
import com.trainticket.model.Gare;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public interface TrajetDAO extends BaseDAO<Trajet, Long> {
    
    /**
     * Trouver un trajet par son numéro
     * @param numeroTrajet le numéro du trajet
     * @return Optional contenant le trajet si trouvé
     */
    Optional<Trajet> findByNumeroTrajet(String numeroTrajet);
    
    /**
     * Trouver les trajets entre deux gares
     * @param gareDepart la gare de départ
     * @param gareArrivee la gare d'arrivée
     * @return liste des trajets entre ces gares
     */
    List<Trajet> findByGares(Gare gareDepart, Gare gareArrivee);
    
    /**
     * Trouver les trajets entre deux villes
     * @param villeDepart la ville de départ
     * @param villeArrivee la ville d'arrivée
     * @return liste des trajets entre ces villes
     */
    List<Trajet> findByVilles(String villeDepart, String villeArrivee);
    
    /**
     * Trouver les trajets partant d'une gare
     * @param gareDepart la gare de départ
     * @return liste des trajets partant de cette gare
     */
    List<Trajet> findByGareDepart(Gare gareDepart);
    
    /**
     * Trouver les trajets arrivant à une gare
     * @param gareArrivee la gare d'arrivée
     * @return liste des trajets arrivant à cette gare
     */
    List<Trajet> findByGareArrivee(Gare gareArrivee);
    
    /**
     * Trouver les trajets par type
     * @param type le type de trajet
     * @return liste des trajets de ce type
     */
    List<Trajet> findByType(Trajet.TypeTrajet type);
    
    /**
     * Trouver tous les trajets actifs
     * @return liste des trajets actifs
     */
    List<Trajet> findActiveTrajets();
    
    /**
     * Trouver les trajets dans une fourchette de prix
     * @param prixMin prix minimum
     * @param prixMax prix maximum
     * @return liste des trajets dans cette fourchette
     */
    List<Trajet> findByPriceRange(BigDecimal prixMin, BigDecimal prixMax);
    
    /**
     * Trouver les trajets par durée maximale
     * @param dureeMaxMinutes durée maximale en minutes
     * @return liste des trajets de durée inférieure ou égale
     */
    List<Trajet> findByMaxDuration(int dureeMaxMinutes);
    
    /**
     * Rechercher des trajets par numéro ou description
     * @param searchTerm terme de recherche
     * @return liste des trajets correspondants
     */
    List<Trajet> searchTrajets(String searchTerm);
    
    /**
     * Vérifier si un numéro de trajet existe
     * @param numeroTrajet le numéro du trajet
     * @return true si le trajet existe, false sinon
     */
    boolean existsByNumeroTrajet(String numeroTrajet);
    
    /**
     * Compter les trajets actifs
     * @return nombre de trajets actifs
     */
    long countActiveTrajets();
    
    /**
     * Trouver les trajets les plus populaires
     * @param limit nombre maximum de trajets à retourner
     * @return liste des trajets les plus populaires
     */
    List<Trajet> findMostPopularTrajets(int limit);
}
