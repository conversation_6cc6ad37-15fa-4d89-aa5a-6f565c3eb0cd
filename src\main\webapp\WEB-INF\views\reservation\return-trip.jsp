<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voyage de Retour - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="${pageContext.request.contextPath}/resources/css/style.css" rel="stylesheet">
</head>
<body>
    <%@ include file="../common/navbar.jsp" %>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <!-- En-tête -->
                <div class="text-center mb-4">
                    <div class="alert alert-success">
                        <h4><i class="fas fa-check-circle"></i> Réservation confirmée !</h4>
                        <p class="mb-0">Votre billet pour ${voyageAller.trajet.gareDepart.ville} → ${voyageAller.trajet.gareArrivee.ville} a été réservé avec succès.</p>
                    </div>
                </div>

                <!-- Proposition de voyage de retour -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-undo"></i> 
                            Souhaitez-vous réserver votre voyage de retour ?
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Votre voyage aller</h6>
                                <div class="border rounded p-3 bg-light">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>${voyageAller.trajet.gareDepart.ville}</strong>
                                            <i class="fas fa-arrow-right mx-2"></i>
                                            <strong>${voyageAller.trajet.gareArrivee.ville}</strong>
                                        </div>
                                        <span class="badge bg-success">Confirmé</span>
                                    </div>
                                    <small class="text-muted">
                                        <fmt:formatDate value="${voyageAller.dateHeureDepart}" pattern="dd/MM/yyyy à HH:mm"/>
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Destinations disponibles depuis ${voyageAller.trajet.gareArrivee.ville}</h6>
                                <c:choose>
                                    <c:when test="${not empty destinationsDisponibles}">
                                        <div class="list-group">
                                            <c:forEach var="destination" items="${destinationsDisponibles}">
                                                <button type="button" class="list-group-item list-group-item-action destination-option" 
                                                        data-destination="${destination}">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <span>
                                                            <i class="fas fa-map-marker-alt text-primary"></i>
                                                            ${destination}
                                                        </span>
                                                        <i class="fas fa-chevron-right"></i>
                                                    </div>
                                                </button>
                                            </c:forEach>
                                        </div>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i>
                                            Aucune destination disponible depuis cette gare.
                                        </div>
                                    </c:otherwise>
                                </c:choose>
                            </div>
                        </div>

                        <c:if test="${not empty destinationsDisponibles}">
                            <!-- Formulaire de recherche de retour -->
                            <div id="returnSearchForm" class="mt-4" style="display: none;">
                                <hr>
                                <h6>Rechercher votre voyage de retour</h6>
                                <form action="${pageContext.request.contextPath}/recherche/search" method="post">
                                    <input type="hidden" name="villeDepart" id="selectedDestination" value="">
                                    <input type="hidden" name="villeArrivee" value="${voyageAller.trajet.gareDepart.ville}">
                                    
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label for="dateRetour" class="form-label">Date de retour</label>
                                            <input type="date" class="form-control" id="dateRetour" name="dateVoyage" 
                                                   min="${dateMinRetour}" required>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="nombrePassagersRetour" class="form-label">Passagers</label>
                                            <select class="form-select" id="nombrePassagersRetour" name="nombrePassagers">
                                                <option value="1" selected>1 passager</option>
                                                <option value="2">2 passagers</option>
                                                <option value="3">3 passagers</option>
                                                <option value="4">4 passagers</option>
                                                <option value="5">5 passagers</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">&nbsp;</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="voyagesDirectsRetour" 
                                                       name="voyagesDirects">
                                                <label class="form-check-label" for="voyagesDirectsRetour">
                                                    Voyages directs uniquement
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">&nbsp;</label>
                                            <button type="submit" class="btn btn-primary w-100">
                                                <i class="fas fa-search"></i> Rechercher
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </c:if>

                        <!-- Actions -->
                        <div class="mt-4 text-center">
                            <c:if test="${not empty destinationsDisponibles}">
                                <p class="text-muted">
                                    <i class="fas fa-lightbulb text-warning"></i>
                                    Sélectionnez une destination ci-dessus pour rechercher votre voyage de retour
                                </p>
                            </c:if>
                            
                            <div class="btn-group" role="group">
                                <a href="${pageContext.request.contextPath}/user/tickets" class="btn btn-success">
                                    <i class="fas fa-ticket-alt"></i> Voir mes billets
                                </a>
                                <a href="${pageContext.request.contextPath}/recherche/search" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i> Nouvelle recherche
                                </a>
                                <a href="${pageContext.request.contextPath}/" class="btn btn-outline-secondary">
                                    <i class="fas fa-home"></i> Accueil
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Informations utiles -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-info-circle"></i> Informations importantes</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success"></i> Votre billet est confirmé</li>
                                    <li><i class="fas fa-envelope text-info"></i> Confirmation envoyée par email</li>
                                    <li><i class="fas fa-download text-primary"></i> PDF téléchargeable dans votre espace</li>
                                    <li><i class="fas fa-clock text-warning"></i> Présentez-vous 30min avant le départ</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-gift"></i> Avantages du voyage de retour</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-percent text-success"></i> Réduction de 10% sur le retour</li>
                                    <li><i class="fas fa-calendar text-info"></i> Flexibilité des dates</li>
                                    <li><i class="fas fa-sync text-primary"></i> Modification gratuite</li>
                                    <li><i class="fas fa-shield text-warning"></i> Assurance incluse</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include file="../common/footer.jsp" %>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const destinationOptions = document.querySelectorAll('.destination-option');
            const returnSearchForm = document.getElementById('returnSearchForm');
            const selectedDestinationInput = document.getElementById('selectedDestination');
            const dateRetourInput = document.getElementById('dateRetour');

            // Définir la date minimum (lendemain du voyage aller)
            const voyageAllerDate = new Date('${voyageAller.dateHeureDepart}');
            const dateMin = new Date(voyageAllerDate);
            dateMin.setDate(dateMin.getDate() + 1);
            dateRetourInput.min = dateMin.toISOString().split('T')[0];
            dateRetourInput.value = dateMin.toISOString().split('T')[0];

            destinationOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Retirer la sélection précédente
                    destinationOptions.forEach(opt => opt.classList.remove('active'));
                    
                    // Ajouter la sélection actuelle
                    this.classList.add('active');
                    
                    // Mettre à jour la destination sélectionnée
                    const destination = this.dataset.destination;
                    selectedDestinationInput.value = destination;
                    
                    // Afficher le formulaire de recherche
                    returnSearchForm.style.display = 'block';
                    returnSearchForm.scrollIntoView({ behavior: 'smooth' });
                });
            });

            // Animation d'entrée
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>

    <style>
        .destination-option {
            transition: all 0.3s ease;
        }
        
        .destination-option:hover {
            background-color: #f8f9fa;
            border-color: #007bff;
        }
        
        .destination-option.active {
            background-color: #e3f2fd;
            border-color: #007bff;
            color: #007bff;
        }
        
        .card {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: none;
        }
        
        .card-header {
            border-bottom: 2px solid rgba(255,255,255,0.1);
        }
    </style>
</body>
</html>
