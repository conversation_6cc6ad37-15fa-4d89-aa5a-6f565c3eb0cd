package com.trainticket.servlet;

import com.trainticket.controller.SearchController;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Servlet pour la page de formulaire de recherche
 * Charge les données dynamiques et redirige vers search-form.jsp
 */
@WebServlet(name = "SearchFormServlet", urlPatterns = {"/search", "/recherche"})
public class SearchFormServlet extends HttpServlet {
    
    private SearchController searchController;
    
    @Override
    public void init() throws ServletException {
        super.init();
        this.searchController = new SearchController();
        System.out.println("SearchFormServlet initialisé");
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        try {
            // Charger les données dynamiques pour la recherche
            searchController.loadSearchData(request);
            searchController.loadSearchStatistics(request);
            
            // Rediriger vers la page de recherche
            request.getRequestDispatcher("/WEB-INF/views/search/search-form.jsp").forward(request, response);
            
        } catch (Exception e) {
            System.err.println("Erreur lors du traitement de la page de recherche: " + e.getMessage());
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, 
                             "Erreur lors du chargement de la page de recherche");
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        doGet(request, response);
    }
}
