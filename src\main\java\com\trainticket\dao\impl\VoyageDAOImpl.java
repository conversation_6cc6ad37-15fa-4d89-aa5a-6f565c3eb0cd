package com.trainticket.dao.impl;

import com.trainticket.dao.VoyageDAO;
import com.trainticket.model.Voyage;
import com.trainticket.model.Trajet;
import com.trainticket.util.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.query.Query;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

public class VoyageDAOImpl extends BaseDAOImpl<Voyage, Long> implements VoyageDAO {

    @Override
    public List<Voyage> findByTrajet(Trajet trajet) {
        String hql = "FROM Voyage v WHERE v.trajet = ?0 ORDER BY v.dateHeureDepart";
        return executeQuery(hql, trajet);
    }

    @Override
    public List<Voyage> findByDate(LocalDate date) {
        String hql = "FROM Voyage v WHERE DATE(v.dateHeureDepart) = ?0 " +
                    "ORDER BY v.dateHeureDepart";
        return executeQuery(hql, date);
    }

    @Override
    public List<Voyage> findByDateRange(LocalDateTime dateDebut, LocalDateTime dateFin) {
        String hql = "FROM Voyage v WHERE v.dateHeureDepart BETWEEN ?0 AND ?1 " +
                    "ORDER BY v.dateHeureDepart";
        return executeQuery(hql, dateDebut, dateFin);
    }

    @Override
    public List<Voyage> findByStatut(Voyage.StatutVoyage statut) {
        String hql = "FROM Voyage v WHERE v.statut = ?0 ORDER BY v.dateHeureDepart";
        return executeQuery(hql, statut);
    }

    @Override
    public List<Voyage> findAvailableVoyages() {
        String hql = "FROM Voyage v WHERE v.placesDisponibles > 0 " +
                    "AND v.statut = 'PROGRAMME' " +
                    "AND v.dateHeureDepart > CURRENT_TIMESTAMP " +
                    "ORDER BY v.dateHeureDepart";
        return executeQuery(hql);
    }

    @Override
    public List<Voyage> searchVoyages(String villeDepart, String villeArrivee, LocalDate date) {
        logger.info("Recherche de voyages: {} -> {} le {}", villeDepart, villeArrivee, date);

        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "SELECT v FROM Voyage v " +
                        "JOIN v.trajet t " +
                        "JOIN t.gareDepart gd " +
                        "JOIN t.gareArrivee ga " +
                        "WHERE LOWER(TRIM(gd.ville)) = LOWER(TRIM(:villeDepart)) " +
                        "AND LOWER(TRIM(ga.ville)) = LOWER(TRIM(:villeArrivee)) " +
                        "AND DATE(v.dateHeureDepart) = :date " +
                        "AND v.placesDisponibles > 0 " +
                        "AND v.statut = :statut " +
                        "AND t.active = true " +
                        "AND gd.active = true " +
                        "AND ga.active = true " +
                        "ORDER BY v.dateHeureDepart";

            Query<Voyage> query = session.createQuery(hql, Voyage.class);
            query.setParameter("villeDepart", villeDepart);
            query.setParameter("villeArrivee", villeArrivee);
            query.setParameter("date", date);
            query.setParameter("statut", Voyage.StatutVoyage.PROGRAMME);

            List<Voyage> results = query.getResultList();
            logger.info("Nombre de voyages trouvés: {}", results.size());

            return results;
        } catch (Exception e) {
            logger.error("Erreur lors de la recherche de voyages", e);
            throw new RuntimeException("Erreur lors de la recherche", e);
        }
    }

    @Override
    public List<Voyage> searchDirectVoyages(String villeDepart, String villeArrivee, LocalDate date) {
        logger.info("Recherche de voyages directs: {} -> {} le {}", villeDepart, villeArrivee, date);

        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "SELECT v FROM Voyage v " +
                        "JOIN v.trajet t " +
                        "JOIN t.gareDepart gd " +
                        "JOIN t.gareArrivee ga " +
                        "WHERE LOWER(TRIM(gd.ville)) = LOWER(TRIM(:villeDepart)) " +
                        "AND LOWER(TRIM(ga.ville)) = LOWER(TRIM(:villeArrivee)) " +
                        "AND DATE(v.dateHeureDepart) = :date " +
                        "AND v.placesDisponibles > 0 " +
                        "AND v.statut = :statut " +
                        "AND t.type IN (:types) " +
                        "AND t.active = true " +
                        "AND gd.active = true " +
                        "AND ga.active = true " +
                        "ORDER BY v.dateHeureDepart";

            Query<Voyage> query = session.createQuery(hql, Voyage.class);
            query.setParameter("villeDepart", villeDepart);
            query.setParameter("villeArrivee", villeArrivee);
            query.setParameter("date", date);
            query.setParameter("statut", Voyage.StatutVoyage.PROGRAMME);
            query.setParameterList("types", Arrays.asList(Trajet.TypeTrajet.DIRECT, Trajet.TypeTrajet.EXPRESS));

            List<Voyage> results = query.getResultList();
            logger.info("Nombre de voyages directs trouvés: {}", results.size());

            return results;
        } catch (Exception e) {
            logger.error("Erreur lors de la recherche de voyages directs", e);
            throw new RuntimeException("Erreur lors de la recherche", e);
        }
    }

    /**
     * Recherche avancée de voyages avec critères multiples
     */
    public List<Voyage> searchVoyagesAvance(String villeDepart, String villeArrivee,
                                           LocalDate date, int nombrePassagers,
                                           Voyage.ClasseBillet classe) {
        logger.info("Recherche avancée: {} -> {} le {} pour {} passager(s), classe: {}",
                   villeDepart, villeArrivee, date, nombrePassagers, classe);

        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            StringBuilder hqlBuilder = new StringBuilder();
            hqlBuilder.append("SELECT v FROM Voyage v ")
                     .append("JOIN v.trajet t ")
                     .append("JOIN t.gareDepart gd ")
                     .append("JOIN t.gareArrivee ga ")
                     .append("WHERE LOWER(TRIM(gd.ville)) = LOWER(TRIM(:villeDepart)) ")
                     .append("AND LOWER(TRIM(ga.ville)) = LOWER(TRIM(:villeArrivee)) ")
                     .append("AND DATE(v.dateHeureDepart) = :date ")
                     .append("AND v.placesDisponibles >= :nombrePassagers ")
                     .append("AND v.statut = :statut ")
                     .append("AND t.active = true ")
                     .append("AND gd.active = true ")
                     .append("AND ga.active = true ");

            // Ajouter condition pour la classe si spécifiée
            if (classe != null) {
                switch (classe) {
                    case PREMIERE:
                        hqlBuilder.append("AND v.placesPremiereClasse >= :nombrePassagers ");
                        break;
                    case DEUXIEME:
                        hqlBuilder.append("AND v.placesDeuxiemeClasse >= :nombrePassagers ");
                        break;
                    case ECONOMIQUE:
                        hqlBuilder.append("AND v.placesEconomique >= :nombrePassagers ");
                        break;
                }
            }

            hqlBuilder.append("ORDER BY v.dateHeureDepart");

            Query<Voyage> query = session.createQuery(hqlBuilder.toString(), Voyage.class);
            query.setParameter("villeDepart", villeDepart);
            query.setParameter("villeArrivee", villeArrivee);
            query.setParameter("date", date);
            query.setParameter("nombrePassagers", nombrePassagers);
            query.setParameter("statut", Voyage.StatutVoyage.PROGRAMME);

            List<Voyage> results = query.getResultList();
            logger.info("Nombre de voyages trouvés (recherche avancée): {}", results.size());

            return results;
        } catch (Exception e) {
            logger.error("Erreur lors de la recherche avancée de voyages", e);
            throw new RuntimeException("Erreur lors de la recherche avancée", e);
        }
    }

    @Override
    public List<Voyage> findVoyagesWithAvailableSeats(Voyage.ClasseBillet classe) {
        String hql;
        switch (classe) {
            case PREMIERE:
                hql = "FROM Voyage v WHERE v.placesPremiereClasse > 0 " +
                     "AND v.statut = 'PROGRAMME' " +
                     "AND v.dateHeureDepart > CURRENT_TIMESTAMP " +
                     "ORDER BY v.dateHeureDepart";
                break;
            case DEUXIEME:
                hql = "FROM Voyage v WHERE v.placesDeuxiemeClasse > 0 " +
                     "AND v.statut = 'PROGRAMME' " +
                     "AND v.dateHeureDepart > CURRENT_TIMESTAMP " +
                     "ORDER BY v.dateHeureDepart";
                break;
            case ECONOMIQUE:
                hql = "FROM Voyage v WHERE v.placesEconomique > 0 " +
                     "AND v.statut = 'PROGRAMME' " +
                     "AND v.dateHeureDepart > CURRENT_TIMESTAMP " +
                     "ORDER BY v.dateHeureDepart";
                break;
            default:
                return findAvailableVoyages();
        }
        return executeQuery(hql);
    }

    @Override
    public List<Voyage> findUpcomingVoyages() {
        String hql = "FROM Voyage v WHERE v.dateHeureDepart > CURRENT_TIMESTAMP " +
                    "AND v.statut = 'PROGRAMME' " +
                    "ORDER BY v.dateHeureDepart";
        return executeQuery(hql);
    }

    @Override
    public List<Voyage> findOngoingVoyages() {
        String hql = "FROM Voyage v WHERE v.dateHeureDepart <= CURRENT_TIMESTAMP " +
                    "AND v.dateHeureArrivee > CURRENT_TIMESTAMP " +
                    "AND v.statut = 'EN_COURS' " +
                    "ORDER BY v.dateHeureDepart";
        return executeQuery(hql);
    }

    @Override
    public List<Voyage> findCompletedVoyages() {
        String hql = "FROM Voyage v WHERE v.dateHeureArrivee <= CURRENT_TIMESTAMP " +
                    "AND v.statut = 'TERMINE' " +
                    "ORDER BY v.dateHeureDepart DESC";
        return executeQuery(hql);
    }

    @Override
    public List<Voyage> findByTrajetAndDate(Trajet trajet, LocalDate date) {
        String hql = "FROM Voyage v WHERE v.trajet = ?0 " +
                    "AND DATE(v.dateHeureDepart) = ?1 " +
                    "ORDER BY v.dateHeureDepart";
        return executeQuery(hql, trajet, date);
    }

    @Override
    public long countByStatut(Voyage.StatutVoyage statut) {
        String hql = "SELECT COUNT(v) FROM Voyage v WHERE v.statut = ?0";
        return executeCountQuery(hql, statut);
    }

    @Override
    public List<Voyage> findMostBookedVoyages(int limit) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "SELECT v FROM Voyage v " +
                        "LEFT JOIN v.billets b " +
                        "GROUP BY v " +
                        "ORDER BY COUNT(b) DESC";

            Query<Voyage> query = session.createQuery(hql, Voyage.class);
            query.setMaxResults(limit);
            return query.getResultList();
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des voyages les plus réservés", e);
            throw new RuntimeException("Erreur lors de la récupération", e);
        }
    }

    @Override
    public void updateExpiredVoyagesStatus() {
        String hql = "UPDATE Voyage v SET v.statut = 'TERMINE' " +
                    "WHERE v.dateHeureArrivee <= CURRENT_TIMESTAMP " +
                    "AND v.statut IN ('PROGRAMME', 'EN_COURS')";
        executeUpdateQuery(hql);

        String hql2 = "UPDATE Voyage v SET v.statut = 'EN_COURS' " +
                     "WHERE v.dateHeureDepart <= CURRENT_TIMESTAMP " +
                     "AND v.dateHeureArrivee > CURRENT_TIMESTAMP " +
                     "AND v.statut = 'PROGRAMME'";
        executeUpdateQuery(hql2);
    }

    @Override
    public List<Voyage> findWithPagination(int page, int size) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "FROM Voyage v ORDER BY v.dateHeureDepart DESC";
            Query<Voyage> query = session.createQuery(hql, Voyage.class);
            query.setFirstResult(page * size);
            query.setMaxResults(size);
            return query.getResultList();
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération paginée des voyages", e);
            throw new RuntimeException("Erreur lors de la récupération", e);
        }
    }

    /**
     * Trouver les voyages d'aujourd'hui
     * @return liste des voyages d'aujourd'hui
     */
    public List<Voyage> findTodayVoyages() {
        return findByDate(LocalDate.now());
    }

    /**
     * Trouver les voyages de demain
     * @return liste des voyages de demain
     */
    public List<Voyage> findTomorrowVoyages() {
        return findByDate(LocalDate.now().plusDays(1));
    }

    /**
     * Trouver les voyages de la semaine prochaine
     * @return liste des voyages de la semaine prochaine
     */
    public List<Voyage> findNextWeekVoyages() {
        LocalDateTime startOfNextWeek = LocalDate.now().plusWeeks(1).atStartOfDay();
        LocalDateTime endOfNextWeek = startOfNextWeek.plusDays(7);
        return findByDateRange(startOfNextWeek, endOfNextWeek);
    }

    /**
     * Calculer le taux d'occupation moyen
     * @return taux d'occupation en pourcentage
     */
    public Double calculateAverageOccupancyRate() {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "SELECT AVG((t.capaciteTotal - v.placesDisponibles) * 100.0 / t.capaciteTotal) " +
                        "FROM Voyage v JOIN v.trajet t " +
                        "WHERE v.statut IN ('EN_COURS', 'TERMINE')";
            Query<Double> query = session.createQuery(hql, Double.class);
            Double result = query.getSingleResult();
            return result != null ? result : 0.0;
        } catch (Exception e) {
            logger.error("Erreur lors du calcul du taux d'occupation", e);
            return 0.0;
        }
    }

    /**
     * Trouver les voyages avec peu de places disponibles
     * @param seuilPlaces seuil de places disponibles
     * @return liste des voyages avec peu de places
     */
    public List<Voyage> findVoyagesWithLowAvailability(int seuilPlaces) {
        String hql = "FROM Voyage v WHERE v.placesDisponibles <= ?0 " +
                    "AND v.placesDisponibles > 0 " +
                    "AND v.statut = 'PROGRAMME' " +
                    "AND v.dateHeureDepart > CURRENT_TIMESTAMP " +
                    "ORDER BY v.placesDisponibles, v.dateHeureDepart";
        return executeQuery(hql, seuilPlaces);
    }
}
