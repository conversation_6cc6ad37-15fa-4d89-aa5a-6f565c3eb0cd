package com.trainticket.service;

import java.util.List;
import java.util.Optional;

/**
 * Interface de base pour tous les services
 * @param <T> Type de l'entité
 * @param <ID> Type de l'identifiant
 */
public interface BaseService<T, ID> {
    
    /**
     * Sauvegarder une entité
     * @param entity l'entité à sauvegarder
     * @return l'entité sauvegardée
     * @throws ServiceException en cas d'erreur
     */
    T save(T entity) throws ServiceException;
    
    /**
     * Mettre à jour une entité
     * @param entity l'entité à mettre à jour
     * @return l'entité mise à jour
     * @throws ServiceException en cas d'erreur
     */
    T update(T entity) throws ServiceException;
    
    /**
     * Supprimer une entité par son ID
     * @param id l'identifiant de l'entité
     * @throws ServiceException en cas d'erreur
     */
    void deleteById(ID id) throws ServiceException;
    
    /**
     * Trouver une entité par son ID
     * @param id l'identifiant de l'entité
     * @return Optional contenant l'entité si trouvée
     * @throws ServiceException en cas d'erreur
     */
    Optional<T> findById(ID id) throws ServiceException;
    
    /**
     * Trouver toutes les entités
     * @return liste de toutes les entités
     * @throws ServiceException en cas d'erreur
     */
    List<T> findAll() throws ServiceException;
    
    /**
     * Compter le nombre total d'entités
     * @return le nombre d'entités
     * @throws ServiceException en cas d'erreur
     */
    long count() throws ServiceException;
    
    /**
     * Vérifier si une entité existe par son ID
     * @param id l'identifiant de l'entité
     * @return true si l'entité existe, false sinon
     * @throws ServiceException en cas d'erreur
     */
    boolean existsById(ID id) throws ServiceException;
    
    /**
     * Exception personnalisée pour les services
     */
    class ServiceException extends Exception {
        public ServiceException(String message) {
            super(message);
        }
        
        public ServiceException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
