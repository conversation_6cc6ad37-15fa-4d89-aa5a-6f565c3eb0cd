<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test de Recherche - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Test de Recherche de Voyages</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test 1: Recherche avec résultats</h5>
                    </div>
                    <div class="card-body">
                        <form action="${pageContext.request.contextPath}/recherche/search" method="post">
                            <div class="mb-3">
                                <label class="form-label">Ville de départ</label>
                                <input type="text" class="form-control" name="villeDepart" value="Tunis" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Ville d'arrivée</label>
                                <input type="text" class="form-control" name="villeArrivee" value="Sousse" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Date de voyage</label>
                                <input type="date" class="form-control" name="dateVoyage" value="2024-12-25" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Nombre de passagers</label>
                                <input type="number" class="form-control" name="nombrePassagers" value="1" min="1" max="10" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Rechercher</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test 2: Recherche sans résultats</h5>
                    </div>
                    <div class="card-body">
                        <form action="${pageContext.request.contextPath}/recherche/search" method="post">
                            <div class="mb-3">
                                <label class="form-label">Ville de départ</label>
                                <input type="text" class="form-control" name="villeDepart" value="VilleInexistante" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Ville d'arrivée</label>
                                <input type="text" class="form-control" name="villeArrivee" value="AutreVilleInexistante" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Date de voyage</label>
                                <input type="date" class="form-control" name="dateVoyage" value="2024-12-25" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Nombre de passagers</label>
                                <input type="number" class="form-control" name="nombrePassagers" value="1" min="1" max="10" required>
                            </div>
                            <button type="submit" class="btn btn-warning">Tester aucun résultat</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test 3: Recherche avec villes existantes mais aucun trajet</h5>
                    </div>
                    <div class="card-body">
                        <form action="${pageContext.request.contextPath}/recherche/search" method="post">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">Ville de départ</label>
                                    <input type="text" class="form-control" name="villeDepart" value="Tunis" required>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Ville d'arrivée</label>
                                    <input type="text" class="form-control" name="villeArrivee" value="Gafsa" required>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Date de voyage</label>
                                    <input type="date" class="form-control" name="dateVoyage" value="2024-12-25" required>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Passagers</label>
                                    <input type="number" class="form-control" name="nombrePassagers" value="1" min="1" max="10" required>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button type="submit" class="btn btn-info">Tester trajet inexistant</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="${pageContext.request.contextPath}/" class="btn btn-secondary">Retour à l'accueil</a>
            <a href="${pageContext.request.contextPath}/recherche/search" class="btn btn-primary">Page de recherche normale</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
