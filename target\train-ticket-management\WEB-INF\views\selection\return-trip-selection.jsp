<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voyage de Retour - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .voyage-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .voyage-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 15px rgba(0,123,255,0.1);
        }
        .voyage-card.selected {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .skip-option {
            border: 2px dashed #6c757d;
            border-radius: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .skip-option:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <%@ include file="../common/header.jsp" %>

    <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="${pageContext.request.contextPath}/">Accueil</a></li>
                <li class="breadcrumb-item"><a href="${pageContext.request.contextPath}/recherche/search">Recherche</a></li>
                <li class="breadcrumb-item"><a href="javascript:history.back()">Préférences</a></li>
                <li class="breadcrumb-item active">Voyage de retour</li>
            </ol>
        </nav>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0"><i class="fas fa-undo"></i> Souhaitez-vous un voyage de retour ?</h4>
                        <small>Complétez votre voyage avec un billet de retour</small>
                    </div>
                    <div class="card-body">
                        <!-- Option : Passer -->
                        <div class="skip-option p-4 mb-4 text-center">
                            <form method="post" action="${pageContext.request.contextPath}/selection/select-return" style="display: inline;">
                                <input type="hidden" name="action" value="skip">
                                <i class="fas fa-times-circle fa-3x text-muted mb-3"></i>
                                <h5>Non merci, juste l'aller</h5>
                                <p class="text-muted">Je ne souhaite pas réserver de voyage de retour maintenant</p>
                                <button type="submit" class="btn btn-outline-secondary">
                                    Passer cette étape <i class="fas fa-arrow-right"></i>
                                </button>
                            </form>
                        </div>

                        <div class="text-center mb-4">
                            <strong class="text-muted">OU</strong>
                        </div>

                        <!-- Voyages de retour disponibles -->
                        <h5 class="mb-3">
                            <i class="fas fa-train"></i> Voyages de retour disponibles
                            <small class="text-muted">(${villeDepart} → ${villeArrivee})</small>
                        </h5>

                        <c:choose>
                            <c:when test="${empty voyagesRetour}">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    Aucun voyage de retour disponible pour les prochains jours.
                                    <br>Vous pourrez réserver votre retour séparément plus tard.
                                </div>
                                <div class="text-center">
                                    <form method="post" action="${pageContext.request.contextPath}/selection/select-return">
                                        <input type="hidden" name="action" value="skip">
                                        <button type="submit" class="btn btn-primary">
                                            Continuer sans retour <i class="fas fa-arrow-right"></i>
                                        </button>
                                    </form>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <form id="returnTripForm" method="post" action="${pageContext.request.contextPath}/selection/select-return">
                                    <input type="hidden" name="action" value="select">
                                    
                                    <div class="row">
                                        <c:forEach var="voyage" items="${voyagesRetour}" varStatus="status">
                                            <div class="col-md-6 mb-3">
                                                <div class="voyage-card p-3" data-voyage-id="${voyage.id}">
                                                    <input type="radio" name="voyageRetourId" value="${voyage.id}" class="d-none" required>
                                                    
                                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                                        <div>
                                                            <h6 class="mb-1">
                                                                <fmt:formatDate value="${voyage.dateHeureDepart}" pattern="dd/MM/yyyy"/>
                                                            </h6>
                                                            <small class="text-muted">${voyage.trajet.numeroTrajet}</small>
                                                        </div>
                                                        <span class="badge bg-primary">
                                                            <c:choose>
                                                                <c:when test="${voyage.trajet.type == 'EXPRESS'}">Express</c:when>
                                                                <c:when test="${voyage.trajet.type == 'DIRECT'}">Direct</c:when>
                                                                <c:otherwise>Normal</c:otherwise>
                                                            </c:choose>
                                                        </span>
                                                    </div>

                                                    <div class="row mb-2">
                                                        <div class="col-6">
                                                            <small class="text-muted">Départ</small>
                                                            <div class="fw-bold">
                                                                <fmt:formatDate value="${voyage.dateHeureDepart}" pattern="HH:mm"/>
                                                            </div>
                                                            <small>${voyage.trajet.gareDepart.nom}</small>
                                                        </div>
                                                        <div class="col-6 text-end">
                                                            <small class="text-muted">Arrivée</small>
                                                            <div class="fw-bold">
                                                                <fmt:formatDate value="${voyage.dateHeureArrivee}" pattern="HH:mm"/>
                                                            </div>
                                                            <small>${voyage.trajet.gareArrivee.nom}</small>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-2">
                                                        <div class="col-6">
                                                            <small class="text-muted">Durée</small>
                                                            <div>${voyage.trajet.dureeMinutes} min</div>
                                                        </div>
                                                        <div class="col-6 text-end">
                                                            <small class="text-muted">Places</small>
                                                            <div>${voyage.placesDisponibles}</div>
                                                        </div>
                                                    </div>

                                                    <!-- Sélection de classe pour le retour -->
                                                    <div class="mt-3">
                                                        <small class="text-muted">Classe :</small>
                                                        <select name="classeRetour" class="form-select form-select-sm" required>
                                                            <option value="">Choisir une classe</option>
                                                            <c:if test="${voyage.placesPremiereClasse > 0}">
                                                                <option value="PREMIERE">
                                                                    1ère Classe - <fmt:formatNumber value="${voyage.trajet.prixBase * 1.5}" pattern="#,##0.00"/> TND
                                                                </option>
                                                            </c:if>
                                                            <c:if test="${voyage.placesDeuxiemeClasse > 0}">
                                                                <option value="DEUXIEME">
                                                                    2ème Classe - <fmt:formatNumber value="${voyage.trajet.prixBase * 1.2}" pattern="#,##0.00"/> TND
                                                                </option>
                                                            </c:if>
                                                            <c:if test="${voyage.placesEconomique > 0}">
                                                                <option value="ECONOMIQUE">
                                                                    Économique - <fmt:formatNumber value="${voyage.trajet.prixBase}" pattern="#,##0.00"/> TND
                                                                </option>
                                                            </c:if>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </c:forEach>
                                    </div>

                                    <div class="d-flex justify-content-between mt-4">
                                        <a href="javascript:history.back()" class="btn btn-outline-secondary">
                                            <i class="fas fa-arrow-left"></i> Retour
                                        </a>
                                        <button type="submit" class="btn btn-success" disabled id="continueBtn">
                                            Continuer avec retour <i class="fas fa-arrow-right"></i>
                                        </button>
                                    </div>
                                </form>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Résumé du voyage aller -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-plane-departure"></i> Votre voyage aller</h6>
                    </div>
                    <div class="card-body">
                        <c:if test="${not empty voyageAller}">
                            <div class="mb-2">
                                <strong>${voyageAller.trajet.gareDepart.ville} → ${voyageAller.trajet.gareArrivee.ville}</strong>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">
                                    <fmt:formatDate value="${voyageAller.dateHeureDepart}" pattern="dd/MM/yyyy à HH:mm"/>
                                </small>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">
                                    Classe : 
                                    <c:choose>
                                        <c:when test="${sessionScope.selectedClasse == 'PREMIERE'}">1ère Classe</c:when>
                                        <c:when test="${sessionScope.selectedClasse == 'DEUXIEME'}">2ème Classe</c:when>
                                        <c:otherwise>Économique</c:otherwise>
                                    </c:choose>
                                </small>
                            </div>
                            <div>
                                <small class="text-muted">
                                    ${sessionScope.nombrePassagers} passager(s)
                                </small>
                            </div>
                        </c:if>
                    </div>
                </div>

                <!-- Avantages du voyage de retour -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-gift"></i> Avantages</h6>
                    </div>
                    <div class="card-body">
                        <div class="small">
                            <p><i class="fas fa-check text-success"></i> <strong>Réservation groupée</strong><br>
                            Gérez vos deux voyages ensemble</p>
                            
                            <p><i class="fas fa-check text-success"></i> <strong>Gain de temps</strong><br>
                            Plus besoin de refaire une recherche</p>
                            
                            <p><i class="fas fa-check text-success"></i> <strong>Flexibilité</strong><br>
                            Vous pourrez modifier ou annuler séparément</p>
                        </div>
                    </div>
                </div>

                <!-- Note importante -->
                <div class="alert alert-info mt-3">
                    <small>
                        <i class="fas fa-info-circle"></i>
                        <strong>Note :</strong> Vous pouvez toujours réserver votre retour plus tard si vous changez d'avis.
                    </small>
                </div>
            </div>
        </div>
    </div>

    <%@ include file="../common/footer.jsp" %>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const voyageCards = document.querySelectorAll('.voyage-card');
            const continueBtn = document.getElementById('continueBtn');

            voyageCards.forEach(card => {
                const radioInput = card.querySelector('input[type="radio"]');
                const classeSelect = card.querySelector('select[name="classeRetour"]');

                card.addEventListener('click', function(e) {
                    if (e.target.tagName !== 'SELECT' && e.target.tagName !== 'OPTION') {
                        // Désélectionner toutes les cartes
                        voyageCards.forEach(c => {
                            c.classList.remove('selected');
                            c.querySelector('input[type="radio"]').checked = false;
                        });

                        // Sélectionner cette carte
                        this.classList.add('selected');
                        radioInput.checked = true;

                        checkFormValidity();
                    }
                });

                classeSelect.addEventListener('change', function() {
                    checkFormValidity();
                });
            });

            function checkFormValidity() {
                const selectedCard = document.querySelector('.voyage-card.selected');
                if (selectedCard) {
                    const classeSelect = selectedCard.querySelector('select[name="classeRetour"]');
                    if (classeSelect.value) {
                        continueBtn.disabled = false;
                    } else {
                        continueBtn.disabled = true;
                    }
                } else {
                    continueBtn.disabled = true;
                }
            }

            // Animation des cartes
            voyageCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('selected')) {
                        this.style.transform = 'translateY(-2px)';
                    }
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
