package com.trainticket.dao.impl;

import com.trainticket.dao.GareDAO;
import com.trainticket.model.Gare;
import com.trainticket.util.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.query.Query;

import java.util.List;
import java.util.Optional;

public class GareDAOImpl extends BaseDAOImpl<Gare, Long> implements GareDAO {
    
    @Override
    public Optional<Gare> findByNom(String nom) {
        String hql = "FROM Gare g WHERE g.nom = ?0";
        return executeSingleResultQuery(hql, nom);
    }
    
    @Override
    public Optional<Gare> findByCodeGare(String codeGare) {
        String hql = "FROM Gare g WHERE g.codeGare = ?0";
        return executeSingleResultQuery(hql, codeGare);
    }
    
    @Override
    public List<Gare> findByVille(String ville) {
        String hql = "FROM Gare g WHERE LOWER(g.ville) = LOWER(?0) ORDER BY g.nom";
        return executeQuery(hql, ville);
    }
    
    @Override
    public List<Gare> findActiveGares() {
        String hql = "FROM Gare g WHERE g.active = true ORDER BY g.ville, g.nom";
        return executeQuery(hql);
    }
    
    @Override
    public List<Gare> searchByNameOrCity(String searchTerm) {
        String hql = "FROM Gare g WHERE " +
                    "LOWER(g.nom) LIKE LOWER(?0) OR " +
                    "LOWER(g.ville) LIKE LOWER(?0) OR " +
                    "LOWER(g.codeGare) LIKE LOWER(?0) " +
                    "ORDER BY g.ville, g.nom";
        String searchPattern = "%" + searchTerm + "%";
        return executeQuery(hql, searchPattern);
    }
    
    @Override
    public List<Gare> findGaresInRadius(double latitude, double longitude, double radiusKm) {
        // Formule de Haversine pour calculer la distance
        String hql = "FROM Gare g WHERE g.latitude IS NOT NULL AND g.longitude IS NOT NULL " +
                    "AND (6371 * acos(cos(radians(?0)) * cos(radians(g.latitude)) * " +
                    "cos(radians(g.longitude) - radians(?1)) + sin(radians(?0)) * " +
                    "sin(radians(g.latitude)))) <= ?2 " +
                    "ORDER BY g.ville, g.nom";
        return executeQuery(hql, latitude, longitude, radiusKm);
    }
    
    @Override
    public List<String> findDistinctCities() {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "SELECT DISTINCT g.ville FROM Gare g WHERE g.active = true ORDER BY g.ville";
            Query<String> query = session.createQuery(hql, String.class);
            return query.getResultList();
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des villes distinctes", e);
            throw new RuntimeException("Erreur lors de la récupération", e);
        }
    }
    
    @Override
    public boolean existsByNom(String nom) {
        String hql = "SELECT COUNT(g) FROM Gare g WHERE g.nom = ?0";
        return executeCountQuery(hql, nom) > 0;
    }
    
    @Override
    public boolean existsByCodeGare(String codeGare) {
        String hql = "SELECT COUNT(g) FROM Gare g WHERE g.codeGare = ?0";
        return executeCountQuery(hql, codeGare) > 0;
    }
    
    @Override
    public long countActiveGares() {
        String hql = "SELECT COUNT(g) FROM Gare g WHERE g.active = true";
        return executeCountQuery(hql);
    }
    
    /**
     * Trouver les gares avec coordonnées GPS
     * @return liste des gares ayant des coordonnées
     */
    public List<Gare> findGaresWithCoordinates() {
        String hql = "FROM Gare g WHERE g.latitude IS NOT NULL AND g.longitude IS NOT NULL " +
                    "ORDER BY g.ville, g.nom";
        return executeQuery(hql);
    }
    
    /**
     * Trouver les gares sans coordonnées GPS
     * @return liste des gares n'ayant pas de coordonnées
     */
    public List<Gare> findGaresWithoutCoordinates() {
        String hql = "FROM Gare g WHERE g.latitude IS NULL OR g.longitude IS NULL " +
                    "ORDER BY g.ville, g.nom";
        return executeQuery(hql);
    }
    
    /**
     * Compter les gares par ville
     * @param ville la ville
     * @return nombre de gares dans cette ville
     */
    public long countByVille(String ville) {
        String hql = "SELECT COUNT(g) FROM Gare g WHERE LOWER(g.ville) = LOWER(?0)";
        return executeCountQuery(hql, ville);
    }
    
    /**
     * Trouver les gares les plus proches d'un point
     * @param latitude latitude du point
     * @param longitude longitude du point
     * @param limit nombre maximum de gares à retourner
     * @return liste des gares les plus proches
     */
    public List<Gare> findNearestGares(double latitude, double longitude, int limit) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "FROM Gare g WHERE g.latitude IS NOT NULL AND g.longitude IS NOT NULL " +
                        "ORDER BY (6371 * acos(cos(radians(:lat)) * cos(radians(g.latitude)) * " +
                        "cos(radians(g.longitude) - radians(:lng)) + sin(radians(:lat)) * " +
                        "sin(radians(g.latitude))))";
            
            Query<Gare> query = session.createQuery(hql, Gare.class);
            query.setParameter("lat", latitude);
            query.setParameter("lng", longitude);
            query.setMaxResults(limit);
            
            return query.getResultList();
        } catch (Exception e) {
            logger.error("Erreur lors de la recherche des gares les plus proches", e);
            throw new RuntimeException("Erreur lors de la recherche", e);
        }
    }
    
    /**
     * Mettre à jour le statut actif d'une gare
     * @param gareId l'ID de la gare
     * @param active le nouveau statut
     */
    public void updateActiveStatus(Long gareId, boolean active) {
        String hql = "UPDATE Gare g SET g.active = ?0 WHERE g.id = ?1";
        executeUpdateQuery(hql, active, gareId);
    }
    
    /**
     * Recherche avancée de gares
     * @param nom nom (peut être partiel)
     * @param ville ville (peut être partiel)
     * @param codeGare code de gare (peut être partiel)
     * @param activeOnly inclure seulement les gares actives
     * @return liste des gares correspondantes
     */
    public List<Gare> advancedSearch(String nom, String ville, String codeGare, boolean activeOnly) {
        StringBuilder hql = new StringBuilder("FROM Gare g WHERE 1=1");
        
        if (nom != null && !nom.trim().isEmpty()) {
            hql.append(" AND LOWER(g.nom) LIKE LOWER('%").append(nom).append("%')");
        }
        
        if (ville != null && !ville.trim().isEmpty()) {
            hql.append(" AND LOWER(g.ville) LIKE LOWER('%").append(ville).append("%')");
        }
        
        if (codeGare != null && !codeGare.trim().isEmpty()) {
            hql.append(" AND LOWER(g.codeGare) LIKE LOWER('%").append(codeGare).append("%')");
        }
        
        if (activeOnly) {
            hql.append(" AND g.active = true");
        }
        
        hql.append(" ORDER BY g.ville, g.nom");
        
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            Query<Gare> query = session.createQuery(hql.toString(), Gare.class);
            return query.getResultList();
        } catch (Exception e) {
            logger.error("Erreur lors de la recherche avancée de gares", e);
            throw new RuntimeException("Erreur lors de la recherche", e);
        }
    }
}
