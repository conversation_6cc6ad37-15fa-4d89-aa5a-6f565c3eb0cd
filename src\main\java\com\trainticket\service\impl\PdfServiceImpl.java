package com.trainticket.service.impl;

import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Cell;
// import com.itextpdf.layout.property.TextAlignment;
// import com.itextpdf.layout.property.UnitValue;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.io.font.constants.StandardFonts;
import com.trainticket.model.Billet;
import com.trainticket.service.PdfService;

import java.io.ByteArrayOutputStream;
import java.time.format.DateTimeFormatter;

public class PdfServiceImpl implements PdfService {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");

    @Override
    public byte[] genererBilletPdf(Billet billet) throws Exception {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        genererBilletPdf(billet, outputStream);
        return outputStream.toByteArray();
    }

    @Override
    public void genererBilletPdf(Billet billet, ByteArrayOutputStream outputStream) throws Exception {
        PdfWriter writer = new PdfWriter(outputStream);
        PdfDocument pdfDoc = new PdfDocument(writer);
        Document document = new Document(pdfDoc);

        // Police
        PdfFont font = PdfFontFactory.createFont(StandardFonts.HELVETICA);
        PdfFont boldFont = PdfFontFactory.createFont(StandardFonts.HELVETICA_BOLD);

        // Titre
        Paragraph titre = new Paragraph("BILLET DE TRAIN")
                .setFont(boldFont)
                .setFontSize(20)
                .setFontColor(ColorConstants.BLUE);
        document.add(titre);

        // Numéro de billet
        Paragraph numeroBillet = new Paragraph("Numéro de billet: " + billet.getNumeroBillet())
                .setFont(boldFont)
                .setFontSize(14);
        document.add(numeroBillet);

        document.add(new Paragraph("\n"));

        // Informations du voyage
        Table voyageTable = new Table(2);
        // voyageTable.setWidth(UnitValue.createPercentValue(100));

        // Informations de base
        voyageTable.addCell(createCell("Passager:", boldFont));
        voyageTable.addCell(createCell(billet.getUtilisateur().getNomComplet(), font));

        voyageTable.addCell(createCell("De:", boldFont));
        voyageTable.addCell(createCell(billet.getVoyage().getTrajet().getGareDepart().getNom() +
                                     " (" + billet.getVoyage().getTrajet().getGareDepart().getVille() + ")", font));

        voyageTable.addCell(createCell("À:", boldFont));
        voyageTable.addCell(createCell(billet.getVoyage().getTrajet().getGareArrivee().getNom() +
                                     " (" + billet.getVoyage().getTrajet().getGareArrivee().getVille() + ")", font));

        voyageTable.addCell(createCell("Date de départ:", boldFont));
        voyageTable.addCell(createCell(billet.getVoyage().getDateHeureDepart().format(DATETIME_FORMATTER), font));

        voyageTable.addCell(createCell("Date d'arrivée:", boldFont));
        voyageTable.addCell(createCell(billet.getVoyage().getDateHeureArrivee().format(DATETIME_FORMATTER), font));

        voyageTable.addCell(createCell("Classe:", boldFont));
        voyageTable.addCell(createCell(billet.getClasseLibelle(), font));

        if (billet.getNumeroSiege() != null) {
            voyageTable.addCell(createCell("Siège:", boldFont));
            voyageTable.addCell(createCell(billet.getNumeroSiege(), font));
        }

        voyageTable.addCell(createCell("Prix:", boldFont));
        voyageTable.addCell(createCell(billet.getPrix() + " DH", font));

        voyageTable.addCell(createCell("Statut:", boldFont));
        voyageTable.addCell(createCell(billet.getStatut().toString(), font));

        document.add(voyageTable);

        // Préférences si disponibles
        if (billet.getPreferences() != null) {
            document.add(new Paragraph("\n"));
            Paragraph preferencesTitle = new Paragraph("Préférences:")
                    .setFont(boldFont)
                    .setFontSize(12);
            document.add(preferencesTitle);

            if (billet.getPreferences().isFenetre()) {
                document.add(new Paragraph("• Place côté fenêtre").setFont(font));
            }
            if (billet.getPreferences().isEspaceFamille()) {
                document.add(new Paragraph("• Espace famille").setFont(font));
            }
            if (billet.getPreferences().isWagonNonFumeur()) {
                document.add(new Paragraph("• Wagon non-fumeur").setFont(font));
            }
        }

        // Informations importantes
        document.add(new Paragraph("\n"));
        Paragraph infos = new Paragraph("INFORMATIONS IMPORTANTES:")
                .setFont(boldFont)
                .setFontSize(12)
                .setFontColor(ColorConstants.RED);
        document.add(infos);

        document.add(new Paragraph("• Présentez-vous en gare 30 minutes avant le départ").setFont(font));
        document.add(new Paragraph("• Ce billet est nominatif et non transférable").setFont(font));
        document.add(new Paragraph("• Une pièce d'identité sera demandée lors du contrôle").setFont(font));
        document.add(new Paragraph("• Annulation possible jusqu'à 2h avant le départ").setFont(font));

        // Date d'émission
        document.add(new Paragraph("\n"));
        Paragraph emission = new Paragraph("Billet émis le: " + billet.getDateAchat().format(DATETIME_FORMATTER))
                .setFont(font)
                .setFontSize(10);
        document.add(emission);

        document.close();
    }

    private Cell createCell(String content, PdfFont font) {
        return new Cell().add(new Paragraph(content).setFont(font)).setPadding(5);
    }
}
