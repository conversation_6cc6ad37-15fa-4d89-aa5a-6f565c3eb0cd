package com.trainticket.service;

import com.trainticket.model.Billet;
import com.trainticket.model.Utilisateur;
import com.trainticket.model.Voyage;
import com.trainticket.model.Preference;

import java.math.BigDecimal;
import java.util.List;

/**
 * Service de gestion des réservations
 */
public interface ReservationService {
    
    /**
     * Créer une réservation
     * @param utilisateur l'utilisateur qui réserve
     * @param voyage le voyage à réserver
     * @param classe la classe souhaitée
     * @param preferences les préférences du passager
     * @return le billet créé
     * @throws ReservationException si la réservation échoue
     */
    Billet creerReservation(Utilisateur utilisateur, Voyage voyage, 
                           Voyage.ClasseBillet classe, Preference preferences) 
                           throws ReservationException;
    
    /**
     * Créer plusieurs réservations (voyage en groupe)
     * @param utilisateur l'utilisateur qui réserve
     * @param voyage le voyage à réserver
     * @param reservations liste des détails de réservation
     * @return liste des billets créés
     * @throws ReservationException si la réservation échoue
     */
    List<Billet> creerReservationsGroupees(Utilisateur utilisateur, Voyage voyage, 
                                          List<DetailsReservation> reservations) 
                                          throws ReservationException;
    
    /**
     * Annuler une réservation
     * @param billetId l'ID du billet à annuler
     * @param utilisateur l'utilisateur qui annule
     * @return true si l'annulation a réussi, false sinon
     * @throws ReservationException si l'annulation échoue
     */
    boolean annulerReservation(Long billetId, Utilisateur utilisateur) 
                              throws ReservationException;
    
    /**
     * Modifier une réservation
     * @param billetId l'ID du billet à modifier
     * @param nouveauVoyage le nouveau voyage
     * @param nouvelleClasse la nouvelle classe
     * @param utilisateur l'utilisateur qui modifie
     * @return le billet modifié
     * @throws ReservationException si la modification échoue
     */
    Billet modifierReservation(Long billetId, Voyage nouveauVoyage, 
                              Voyage.ClasseBillet nouvelleClasse, Utilisateur utilisateur) 
                              throws ReservationException;
    
    /**
     * Calculer le prix d'une réservation
     * @param voyage le voyage
     * @param classe la classe
     * @param preferences les préférences (peuvent affecter le prix)
     * @return le prix calculé
     * @throws ReservationException si le calcul échoue
     */
    BigDecimal calculerPrix(Voyage voyage, Voyage.ClasseBillet classe, 
                           Preference preferences) throws ReservationException;
    
    /**
     * Vérifier si une réservation est possible
     * @param voyage le voyage
     * @param classe la classe
     * @param nombrePlaces nombre de places souhaitées
     * @return true si la réservation est possible, false sinon
     */
    boolean verifierDisponibilite(Voyage voyage, Voyage.ClasseBillet classe, 
                                 int nombrePlaces);
    
    /**
     * Obtenir les réservations d'un utilisateur
     * @param utilisateur l'utilisateur
     * @return liste des billets de l'utilisateur
     * @throws ReservationException si la récupération échoue
     */
    List<Billet> getReservationsUtilisateur(Utilisateur utilisateur) 
                                           throws ReservationException;
    
    /**
     * Obtenir les réservations actives d'un utilisateur
     * @param utilisateur l'utilisateur
     * @return liste des billets actifs
     * @throws ReservationException si la récupération échoue
     */
    List<Billet> getReservationsActives(Utilisateur utilisateur) 
                                       throws ReservationException;
    
    /**
     * Obtenir l'historique des voyages d'un utilisateur
     * @param utilisateur l'utilisateur
     * @return liste des billets utilisés
     * @throws ReservationException si la récupération échoue
     */
    List<Billet> getHistoriqueVoyages(Utilisateur utilisateur) 
                                     throws ReservationException;
    
    /**
     * Valider un billet (marquer comme utilisé)
     * @param numeroBillet le numéro du billet
     * @return true si la validation a réussi, false sinon
     * @throws ReservationException si la validation échoue
     */
    boolean validerBillet(String numeroBillet) throws ReservationException;
    
    /**
     * Obtenir les détails d'un billet
     * @param numeroBillet le numéro du billet
     * @return le billet avec ses détails
     * @throws ReservationException si la récupération échoue
     */
    Billet getBilletDetails(String numeroBillet) throws ReservationException;
    
    /**
     * Vérifier si un billet peut être annulé
     * @param billet le billet à vérifier
     * @return true si le billet peut être annulé, false sinon
     */
    boolean peutEtreAnnule(Billet billet);
    
    /**
     * Vérifier si un billet peut être modifié
     * @param billet le billet à vérifier
     * @return true si le billet peut être modifié, false sinon
     */
    boolean peutEtreModifie(Billet billet);
    
    /**
     * Calculer les frais d'annulation
     * @param billet le billet à annuler
     * @return le montant des frais d'annulation
     */
    BigDecimal calculerFraisAnnulation(Billet billet);
    
    /**
     * Appliquer une promotion ou réduction
     * @param prix le prix de base
     * @param codePromo le code de promotion
     * @param utilisateur l'utilisateur (pour les réductions personnalisées)
     * @return le prix après réduction
     * @throws ReservationException si l'application échoue
     */
    BigDecimal appliquerPromotion(BigDecimal prix, String codePromo, 
                                 Utilisateur utilisateur) throws ReservationException;
    
    /**
     * Classe pour encapsuler les détails d'une réservation
     */
    class DetailsReservation {
        private Voyage.ClasseBillet classe;
        private Preference preferences;
        private String nomPassager;
        private String prenomPassager;
        
        public DetailsReservation() {}
        
        public DetailsReservation(Voyage.ClasseBillet classe, String nomPassager, String prenomPassager) {
            this.classe = classe;
            this.nomPassager = nomPassager;
            this.prenomPassager = prenomPassager;
        }
        
        // Getters et Setters
        public Voyage.ClasseBillet getClasse() { return classe; }
        public void setClasse(Voyage.ClasseBillet classe) { this.classe = classe; }
        
        public Preference getPreferences() { return preferences; }
        public void setPreferences(Preference preferences) { this.preferences = preferences; }
        
        public String getNomPassager() { return nomPassager; }
        public void setNomPassager(String nomPassager) { this.nomPassager = nomPassager; }
        
        public String getPrenomPassager() { return prenomPassager; }
        public void setPrenomPassager(String prenomPassager) { this.prenomPassager = prenomPassager; }
    }
    
    /**
     * Exception pour les erreurs de réservation
     */
    class ReservationException extends Exception {
        public ReservationException(String message) {
            super(message);
        }
        
        public ReservationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
