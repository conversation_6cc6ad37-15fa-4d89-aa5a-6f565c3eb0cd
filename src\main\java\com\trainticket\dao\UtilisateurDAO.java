package com.trainticket.dao;

import com.trainticket.model.Utilisateur;
import java.util.List;
import java.util.Optional;

public interface UtilisateurDAO extends BaseDAO<Utilisateur, Long> {

    /**
     * Trouver un utilisateur par son email
     * @param email l'email de l'utilisateur
     * @return Optional contenant l'utilisateur si trouvé
     */
    Optional<Utilisateur> findByEmail(String email);

    /**
     * Vérifier si un email existe déjà
     * @param email l'email à vérifier
     * @return true si l'email existe, false sinon
     */
    boolean existsByEmail(String email);

    /**
     * Trouver les utilisateurs par rôle
     * @param role le rôle des utilisateurs
     * @return liste des utilisateurs avec ce rôle
     */
    List<Utilisateur> findByRole(Utilisateur.Role role);

    /**
     * Trouver les utilisateurs par statut
     * @param statut le statut des utilisateurs
     * @return liste des utilisateurs avec ce statut
     */
    List<Utilisateur> findByStatut(Utilisateur.StatutCompte statut);

    /**
     * Rechercher des utilisateurs par nom ou prénom
     * @param searchTerm terme de recherche
     * @return liste des utilisateurs correspondants
     */
    List<Utilisateur> searchByName(String searchTerm);

    /**
     * Trouver les utilisateurs actifs
     * @return liste des utilisateurs actifs
     */
    List<Utilisateur> findActiveUsers();

    /**
     * Mettre à jour la dernière connexion d'un utilisateur
     * @param userId l'ID de l'utilisateur
     */
    void updateLastLogin(Long userId);

    /**
     * Compter les utilisateurs par rôle
     * @param role le rôle
     * @return nombre d'utilisateurs avec ce rôle
     */
    long countByRole(Utilisateur.Role role);

    /**
     * Trouver les utilisateurs avec pagination
     * @param page numéro de page (commence à 0)
     * @param size taille de la page
     * @return liste paginée des utilisateurs
     */
    List<Utilisateur> findWithPagination(int page, int size);

    // Méthodes pour les statistiques et l'administration
    long countByStatut(Utilisateur.StatutCompte statut);
    List<Utilisateur> findRecentUsers(int days);
    List<Utilisateur> advancedSearch(String email, String nom, Utilisateur.Role role, Utilisateur.StatutCompte statut);
}
