package com.trainticket.service;

import com.trainticket.model.Voyage;
import com.trainticket.model.Gare;
import com.trainticket.model.Trajet;

import java.time.LocalDate;
import java.util.List;

/**
 * Service de recherche de voyages et trajets
 */
public interface RechercheService {
    
    /**
     * Rechercher des voyages entre deux villes
     * @param villeDepart ville de départ
     * @param villeArrivee ville d'arrivée
     * @param dateVoyage date du voyage
     * @param nombrePassagers nombre de passagers
     * @param voyagesDirectsUniquement inclure seulement les voyages directs
     * @return liste des voyages disponibles
     * @throws SearchException si la recherche échoue
     */
    List<Voyage> rechercherVoyages(String villeDepart, String villeArrivee, 
                                  LocalDate dateVoyage, int nombrePassagers, 
                                  boolean voyagesDirectsUniquement) throws SearchException;
    
    /**
     * Rechercher des voyages directs
     * @param villeDepart ville de départ
     * @param villeArrivee ville d'arrivée
     * @param dateVoyage date du voyage
     * @return liste des voyages directs
     * @throws SearchException si la recherche échoue
     */
    List<Voyage> rechercherVoyagesDirects(String villeDepart, String villeArrivee, 
                                         LocalDate dateVoyage) throws SearchException;
    
    /**
     * Rechercher des voyages avec correspondance
     * @param villeDepart ville de départ
     * @param villeArrivee ville d'arrivée
     * @param dateVoyage date du voyage
     * @return liste des voyages avec correspondance
     * @throws SearchException si la recherche échoue
     */
    List<List<Voyage>> rechercherVoyagesAvecCorrespondance(String villeDepart, String villeArrivee, 
                                                          LocalDate dateVoyage) throws SearchException;
    
    /**
     * Obtenir toutes les villes disponibles
     * @return liste des villes
     * @throws SearchException si la récupération échoue
     */
    List<String> getVillesDisponibles() throws SearchException;
    
    /**
     * Obtenir toutes les gares actives
     * @return liste des gares actives
     * @throws SearchException si la récupération échoue
     */
    List<Gare> getGaresActives() throws SearchException;
    
    /**
     * Rechercher des gares par nom ou ville
     * @param searchTerm terme de recherche
     * @return liste des gares correspondantes
     * @throws SearchException si la recherche échoue
     */
    List<Gare> rechercherGares(String searchTerm) throws SearchException;
    
    /**
     * Obtenir les trajets populaires
     * @param limit nombre maximum de trajets à retourner
     * @return liste des trajets populaires
     * @throws SearchException si la récupération échoue
     */
    List<Trajet> getTrajetsPopulaires(int limit) throws SearchException;
    
    /**
     * Obtenir les voyages disponibles pour une date donnée
     * @param date la date
     * @return liste des voyages disponibles
     * @throws SearchException si la récupération échoue
     */
    List<Voyage> getVoyagesDisponibles(LocalDate date) throws SearchException;
    
    /**
     * Vérifier la disponibilité d'un voyage
     * @param voyageId l'ID du voyage
     * @param nombrePassagers nombre de passagers
     * @param classe classe souhaitée
     * @return true si le voyage est disponible, false sinon
     * @throws SearchException si la vérification échoue
     */
    boolean verifierDisponibilite(Long voyageId, int nombrePassagers, 
                                 Voyage.ClasseBillet classe) throws SearchException;
    
    /**
     * Obtenir les détails d'un voyage
     * @param voyageId l'ID du voyage
     * @return le voyage avec ses détails
     * @throws SearchException si la récupération échoue
     */
    Voyage getVoyageDetails(Long voyageId) throws SearchException;
    
    /**
     * Rechercher des voyages par critères avancés
     * @param criteres critères de recherche
     * @return liste des voyages correspondants
     * @throws SearchException si la recherche échoue
     */
    List<Voyage> rechercheAvancee(CriteresRecherche criteres) throws SearchException;
    
    /**
     * Obtenir les suggestions de destinations depuis une ville
     * @param villeDepart ville de départ
     * @return liste des destinations possibles
     * @throws SearchException si la récupération échoue
     */
    List<String> getSuggestionsDestinations(String villeDepart) throws SearchException;
    
    /**
     * Classe pour encapsuler les critères de recherche avancée
     */
    class CriteresRecherche {
        private String villeDepart;
        private String villeArrivee;
        private LocalDate dateVoyage;
        private LocalDate dateRetour;
        private int nombrePassagers;
        private Voyage.ClasseBillet classePreferee;
        private boolean voyagesDirectsUniquement;
        private Integer dureeMaxMinutes;
        private Double prixMax;
        private Trajet.TypeTrajet typeTrajet;
        
        // Constructeurs
        public CriteresRecherche() {}
        
        public CriteresRecherche(String villeDepart, String villeArrivee, LocalDate dateVoyage) {
            this.villeDepart = villeDepart;
            this.villeArrivee = villeArrivee;
            this.dateVoyage = dateVoyage;
            this.nombrePassagers = 1;
        }
        
        // Getters et Setters
        public String getVilleDepart() { return villeDepart; }
        public void setVilleDepart(String villeDepart) { this.villeDepart = villeDepart; }
        
        public String getVilleArrivee() { return villeArrivee; }
        public void setVilleArrivee(String villeArrivee) { this.villeArrivee = villeArrivee; }
        
        public LocalDate getDateVoyage() { return dateVoyage; }
        public void setDateVoyage(LocalDate dateVoyage) { this.dateVoyage = dateVoyage; }
        
        public LocalDate getDateRetour() { return dateRetour; }
        public void setDateRetour(LocalDate dateRetour) { this.dateRetour = dateRetour; }
        
        public int getNombrePassagers() { return nombrePassagers; }
        public void setNombrePassagers(int nombrePassagers) { this.nombrePassagers = nombrePassagers; }
        
        public Voyage.ClasseBillet getClassePreferee() { return classePreferee; }
        public void setClassePreferee(Voyage.ClasseBillet classePreferee) { this.classePreferee = classePreferee; }
        
        public boolean isVoyagesDirectsUniquement() { return voyagesDirectsUniquement; }
        public void setVoyagesDirectsUniquement(boolean voyagesDirectsUniquement) { 
            this.voyagesDirectsUniquement = voyagesDirectsUniquement; 
        }
        
        public Integer getDureeMaxMinutes() { return dureeMaxMinutes; }
        public void setDureeMaxMinutes(Integer dureeMaxMinutes) { this.dureeMaxMinutes = dureeMaxMinutes; }
        
        public Double getPrixMax() { return prixMax; }
        public void setPrixMax(Double prixMax) { this.prixMax = prixMax; }
        
        public Trajet.TypeTrajet getTypeTrajet() { return typeTrajet; }
        public void setTypeTrajet(Trajet.TypeTrajet typeTrajet) { this.typeTrajet = typeTrajet; }
    }
    
    /**
     * Exception pour les erreurs de recherche
     */
    class SearchException extends Exception {
        public SearchException(String message) {
            super(message);
        }
        
        public SearchException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
