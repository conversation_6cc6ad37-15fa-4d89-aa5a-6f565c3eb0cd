package com.trainticket.model;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;

@Embeddable
public class Preference {

    @Enumerated(EnumType.STRING)
    @Column(name = "preference_fenetre")
    private PreferenceFenetre preferenceFenetre;

    @Column(name = "wagon_non_fumeur")
    private Boolean wagonNonFumeur = true;

    @Column(name = "espace_famille")
    private Boolean espaceFamille = false;

    @Column(name = "acces_handicape")
    private Boolean accesHandicape = false;

    @Column(name = "wifi_requis")
    private Boolean wifiRequis = false;

    @Column(name = "prise_electrique")
    private Boolean priseElectrique = false;

    @Column(length = 200)
    private String commentairesSpeciaux;

    // Constructeurs
    public Preference() {}

    public Preference(PreferenceFenetre preferenceFenetre) {
        this.preferenceFenetre = preferenceFenetre;
    }

    // Getters et Setters
    public PreferenceFenetre getPreferenceFenetre() {
        return preferenceFenetre;
    }

    public void setPreferenceFenetre(PreferenceFenetre preferenceFenetre) {
        this.preferenceFenetre = preferenceFenetre;
    }

    public Boolean getWagonNonFumeur() {
        return wagonNonFumeur;
    }

    public void setWagonNonFumeur(Boolean wagonNonFumeur) {
        this.wagonNonFumeur = wagonNonFumeur;
    }

    public Boolean getEspaceFamille() {
        return espaceFamille;
    }

    public void setEspaceFamille(Boolean espaceFamille) {
        this.espaceFamille = espaceFamille;
    }

    public Boolean getAccesHandicape() {
        return accesHandicape;
    }

    public void setAccesHandicape(Boolean accesHandicape) {
        this.accesHandicape = accesHandicape;
    }

    public Boolean getWifiRequis() {
        return wifiRequis;
    }

    public void setWifiRequis(Boolean wifiRequis) {
        this.wifiRequis = wifiRequis;
    }

    public Boolean getPriseElectrique() {
        return priseElectrique;
    }

    public void setPriseElectrique(Boolean priseElectrique) {
        this.priseElectrique = priseElectrique;
    }

    public String getCommentairesSpeciaux() {
        return commentairesSpeciaux;
    }

    public void setCommentairesSpeciaux(String commentairesSpeciaux) {
        this.commentairesSpeciaux = commentairesSpeciaux;
    }

    // Méthodes utilitaires
    public boolean hasSpecialRequirements() {
        return accesHandicape || espaceFamille || wifiRequis || priseElectrique;
    }

    public boolean isFenetre() {
        return preferenceFenetre == PreferenceFenetre.FENETRE;
    }

    public boolean isEspaceFamille() {
        return espaceFamille != null && espaceFamille;
    }

    public boolean isWagonNonFumeur() {
        return wagonNonFumeur != null && wagonNonFumeur;
    }

    public String getPreferencesResume() {
        StringBuilder sb = new StringBuilder();

        if (preferenceFenetre != null) {
            sb.append(preferenceFenetre.getLibelle()).append(", ");
        }

        if (wagonNonFumeur) {
            sb.append("Non-fumeur, ");
        }

        if (espaceFamille) {
            sb.append("Espace famille, ");
        }

        if (accesHandicape) {
            sb.append("Accès handicapé, ");
        }

        if (wifiRequis) {
            sb.append("WiFi, ");
        }

        if (priseElectrique) {
            sb.append("Prise électrique, ");
        }

        String result = sb.toString();
        return result.isEmpty() ? "Aucune préférence" : result.substring(0, result.length() - 2);
    }

    // Enum
    public enum PreferenceFenetre {
        FENETRE("Côté fenêtre"),
        COULOIR("Côté couloir"),
        INDIFFERENT("Indifférent");

        private final String libelle;

        PreferenceFenetre(String libelle) {
            this.libelle = libelle;
        }

        public String getLibelle() {
            return libelle;
        }
    }

    @Override
    public String toString() {
        return "Preference{" +
                "preferenceFenetre=" + preferenceFenetre +
                ", wagonNonFumeur=" + wagonNonFumeur +
                ", espaceFamille=" + espaceFamille +
                ", accesHandicape=" + accesHandicape +
                ", wifiRequis=" + wifiRequis +
                ", priseElectrique=" + priseElectrique +
                '}';
    }
}
