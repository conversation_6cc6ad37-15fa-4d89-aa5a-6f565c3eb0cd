package com.trainticket.test;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@WebServlet("/test/date-check")
public class DateTestServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();

        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>Test de Date</title>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 40px; }");
        out.println(".info { color: blue; }");
        out.println(".success { color: green; }");
        out.println(".error { color: red; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        out.println("<h1>Test de Date Système</h1>");

        // Date actuelle
        LocalDate today = LocalDate.now();
        out.println("<div class='info'>Date actuelle du système: " + today + "</div>");
        out.println("<div class='info'>Date formatée: " + today.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) + "</div>");

        // Test des dates de recherche
        LocalDate searchDate = LocalDate.parse("2025-05-30");
        out.println("<br><h3>Test de validation de date</h3>");
        out.println("<div class='info'>Date de recherche: " + searchDate + "</div>");
        
        boolean isBeforeToday = searchDate.isBefore(today);
        out.println("<div class='" + (isBeforeToday ? "error" : "success") + "'>");
        out.println("searchDate.isBefore(today): " + isBeforeToday);
        out.println("</div>");

        if (isBeforeToday) {
            out.println("<div class='error'>❌ La date de recherche est considérée comme dans le passé!</div>");
        } else {
            out.println("<div class='success'>✅ La date de recherche est valide</div>");
        }

        // Test avec différentes dates
        out.println("<br><h3>Test avec différentes dates</h3>");
        String[] testDates = {"2025-05-29", "2025-05-30", "2025-05-31", "2025-06-01"};
        
        for (String dateStr : testDates) {
            LocalDate testDate = LocalDate.parse(dateStr);
            boolean isBefore = testDate.isBefore(today);
            out.println("<div class='" + (isBefore ? "error" : "success") + "'>");
            out.println(dateStr + " - isBefore(today): " + isBefore);
            out.println("</div>");
        }

        // Informations système
        out.println("<br><h3>Informations système</h3>");
        out.println("<div class='info'>Fuseau horaire: " + java.time.ZoneId.systemDefault() + "</div>");
        out.println("<div class='info'>Timestamp: " + System.currentTimeMillis() + "</div>");
        out.println("<div class='info'>Date/Heure complète: " + java.time.LocalDateTime.now() + "</div>");

        out.println("<br><a href='" + request.getContextPath() + "/'>Retour à l'accueil</a>");
        out.println("</body>");
        out.println("</html>");
    }
}
