-- Script pour corriger l'encodage des noms de villes
UPDATE gares SET 
    ville = 'Gabès',
    nom = 'Gare de Gabès',
    adresse = 'Avenue Farhat Hached, Gabès'
WHERE id = 10;

UPDATE gares SET 
    ville = 'Béja',
    nom = 'Gare de Béja'
WHERE ville LIKE '%B%ja%' OR nom LIKE '%B%ja%';

UPDATE gares SET 
    ville = 'Kairouan',
    nom = 'Gare de Kairouan'
WHERE ville LIKE '%Kairouan%' OR nom LIKE '%Kairouan%';

-- Vérifier les résultats
SELECT id, ville, nom, adresse FROM gares WHERE ville IN ('Gab<PERSON>', 'Béja', 'Kairouan') OR ville LIKE '%B%ja%' OR ville LIKE '%Gab%';
