package com.trainticket.service;

import com.trainticket.model.Billet;

import java.io.ByteArrayOutputStream;

/**
 * Service pour la génération de documents PDF
 */
public interface PdfService {
    
    /**
     * Génère un PDF pour un billet de train
     * @param billet le billet à convertir en PDF
     * @return le contenu du PDF sous forme de tableau d'octets
     * @throws Exception en cas d'erreur lors de la génération
     */
    byte[] genererBilletPdf(Billet billet) throws Exception;
    
    /**
     * Génère un PDF pour un billet et l'écrit dans un OutputStream
     * @param billet le billet à convertir en PDF
     * @param outputStream le flux de sortie
     * @throws Exception en cas d'erreur lors de la génération
     */
    void genererBilletPdf(Billet billet, ByteArrayOutputStream outputStream) throws Exception;
}
