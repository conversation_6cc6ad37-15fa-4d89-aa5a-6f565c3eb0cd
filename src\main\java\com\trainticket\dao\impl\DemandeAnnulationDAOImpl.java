package com.trainticket.dao.impl;

import com.trainticket.dao.DemandeAnnulationDAO;
import com.trainticket.model.DemandeAnnulation;
import com.trainticket.model.Utilisateur;
import com.trainticket.model.Billet;

import java.util.List;
import java.util.Optional;

public class DemandeAnnulationDAOImpl extends BaseDAOImpl<DemandeAnnulation, Long> implements DemandeAnnulationDAO {

    @Override
    public List<DemandeAnnulation> findByStatut(DemandeAnnulation.StatutDemande statut) {
        String hql = "FROM DemandeAnnulation d WHERE d.statut = :statut ORDER BY d.dateDemande DESC";
        try (org.hibernate.Session session = com.trainticket.util.HibernateUtil.getSessionFactory().openSession()) {
            org.hibernate.query.Query<DemandeAnnulation> query = session.createQuery(hql, DemandeAnnulation.class);
            query.setParameter("statut", statut);
            return query.getResultList();
        } catch (Exception e) {
            System.err.println("Erreur lors de la recherche par statut: " + e.getMessage());
            e.printStackTrace();
            return java.util.Collections.emptyList();
        }
    }

    @Override
    public List<DemandeAnnulation> findByUtilisateur(Utilisateur utilisateur) {
        String hql = "FROM DemandeAnnulation d WHERE d.utilisateur = :utilisateur ORDER BY d.dateDemande DESC";
        try (org.hibernate.Session session = com.trainticket.util.HibernateUtil.getSessionFactory().openSession()) {
            org.hibernate.query.Query<DemandeAnnulation> query = session.createQuery(hql, DemandeAnnulation.class);
            query.setParameter("utilisateur", utilisateur);
            return query.getResultList();
        } catch (Exception e) {
            System.err.println("Erreur lors de la recherche par utilisateur: " + e.getMessage());
            e.printStackTrace();
            return java.util.Collections.emptyList();
        }
    }

    @Override
    public Optional<DemandeAnnulation> findByBillet(Billet billet) {
        String hql = "FROM DemandeAnnulation d WHERE d.billet = :billet";
        try (org.hibernate.Session session = com.trainticket.util.HibernateUtil.getSessionFactory().openSession()) {
            org.hibernate.query.Query<DemandeAnnulation> query = session.createQuery(hql, DemandeAnnulation.class);
            query.setParameter("billet", billet);
            List<DemandeAnnulation> results = query.getResultList();
            return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
        } catch (Exception e) {
            System.err.println("Erreur lors de la recherche par billet: " + e.getMessage());
            e.printStackTrace();
            return Optional.empty();
        }
    }

    @Override
    public List<DemandeAnnulation> findDemandesEnAttente() {
        return findByStatut(DemandeAnnulation.StatutDemande.EN_ATTENTE);
    }

    @Override
    public List<DemandeAnnulation> findByAdminTraitant(Utilisateur admin) {
        String hql = "FROM DemandeAnnulation d WHERE d.adminTraitant = :admin ORDER BY d.dateTraitement DESC";
        try (org.hibernate.Session session = com.trainticket.util.HibernateUtil.getSessionFactory().openSession()) {
            org.hibernate.query.Query<DemandeAnnulation> query = session.createQuery(hql, DemandeAnnulation.class);
            query.setParameter("admin", admin);
            return query.getResultList();
        } catch (Exception e) {
            System.err.println("Erreur lors de la recherche par admin: " + e.getMessage());
            e.printStackTrace();
            return java.util.Collections.emptyList();
        }
    }

    @Override
    public long countByStatut(DemandeAnnulation.StatutDemande statut) {
        String hql = "SELECT COUNT(d) FROM DemandeAnnulation d WHERE d.statut = :statut";
        try (org.hibernate.Session session = com.trainticket.util.HibernateUtil.getSessionFactory().openSession()) {
            org.hibernate.query.Query<Long> query = session.createQuery(hql, Long.class);
            query.setParameter("statut", statut);
            Long result = query.getSingleResult();
            return result != null ? result : 0;
        } catch (Exception e) {
            System.err.println("Erreur lors du comptage par statut: " + e.getMessage());
            e.printStackTrace();
            return 0;
        }
    }
}
