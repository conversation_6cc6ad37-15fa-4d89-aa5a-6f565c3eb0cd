package com.trainticket.test;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * Test simple de connexion MySQL sans Hibernate
 */
@WebServlet(name = "SimpleConnectionTest", urlPatterns = {"/test/simple-db"})
public class SimpleConnectionTest extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();

        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>Test Simple de Connexion MySQL</title>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 40px; }");
        out.println(".success { color: green; font-weight: bold; }");
        out.println(".error { color: red; font-weight: bold; }");
        out.println(".info { color: blue; }");
        out.println("pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        out.println("<h1>🔍 Test Simple de Connexion MySQL</h1>");

        // Test de chargement du driver
        out.println("<h2>1. Test du Driver MySQL</h2>");
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            out.println("<p class='success'>✅ Driver MySQL chargé avec succès</p>");
        } catch (ClassNotFoundException e) {
            out.println("<p class='error'>❌ Driver MySQL non trouvé: " + e.getMessage() + "</p>");
            out.println("<p>Vérifiez que mysql-connector-java est dans le classpath</p>");
        }

        // Test de connexion sans base de données spécifique
        out.println("<h2>2. Test de Connexion au Serveur MySQL</h2>");
        String serverUrl = "*****************************************************************************************";
        testConnection(out, serverUrl, "root", "", "Serveur MySQL (sans mot de passe)");
        testConnection(out, serverUrl, "root", "root", "Serveur MySQL (mot de passe 'root')");
        testConnection(out, serverUrl, "root", "123", "Serveur MySQL (mot de passe '123')");

        // Test de connexion à la base de données spécifique
        out.println("<h2>3. Test de Connexion à la Base train_ticket_db</h2>");
        String dbUrl = "********************************************************************************************************";
        testConnection(out, dbUrl, "root", "", "Base train_ticket_db (sans mot de passe)");
        testConnection(out, dbUrl, "root", "root", "Base train_ticket_db (mot de passe 'root')");
        testConnection(out, dbUrl, "root", "123", "Base train_ticket_db (mot de passe '123')");

        out.println("<hr>");
        out.println("<p><a href='" + request.getContextPath() + "/'>&larr; Retour à l'accueil</a></p>");
        out.println("</body>");
        out.println("</html>");
    }

    private void testConnection(PrintWriter out, String url, String username, String password, String description) {
        try {
            Connection connection = DriverManager.getConnection(url, username, password);
            out.println("<p class='success'>✅ " + description + " : Connexion réussie</p>");
            out.println("<p class='info'>URL: " + url + "</p>");
            out.println("<p class='info'>Utilisateur: " + username + "</p>");
            
            // Test simple
            try {
                String catalog = connection.getCatalog();
                out.println("<p class='info'>Catalogue: " + (catalog != null ? catalog : "null") + "</p>");
            } catch (Exception e) {
                out.println("<p class='info'>Impossible de récupérer le catalogue: " + e.getMessage() + "</p>");
            }
            
            connection.close();
            out.println("<br>");
            
        } catch (SQLException e) {
            out.println("<p class='error'>❌ " + description + " : Échec</p>");
            out.println("<p class='error'>Erreur: " + e.getMessage() + "</p>");
            out.println("<p class='error'>Code d'erreur: " + e.getErrorCode() + "</p>");
            out.println("<br>");
        }
    }
}
