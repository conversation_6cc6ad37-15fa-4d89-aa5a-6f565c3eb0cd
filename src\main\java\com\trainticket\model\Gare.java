package com.trainticket.model;

import javax.persistence.*;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name = "gares")
public class Gare {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true, length = 100)
    private String nom;

    @Column(nullable = false, length = 100)
    private String ville;

    @Column(length = 10)
    private String codeGare;

    @Column(length = 200)
    private String adresse;

    @Column(precision = 10, scale = 8)
    private Double latitude;

    @Column(precision = 11, scale = 8)
    private Double longitude;

    @Column(nullable = false)
    private Boolean active = true;

    @OneToMany(mappedBy = "gareDepart", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Trajet> trajetsDepart;

    @OneToMany(mappedBy = "gareArrivee", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Trajet> trajetsArrivee;

    // Constructeurs
    public Gare() {}

    public Gare(String nom, String ville) {
        this.nom = nom;
        this.ville = ville;
    }

    public Gare(String nom, String ville, String codeGare) {
        this(nom, ville);
        this.codeGare = codeGare;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNom() {
        return nom;
    }

    public void setNom(String nom) {
        this.nom = nom;
    }

    public String getVille() {
        return ville;
    }

    public void setVille(String ville) {
        this.ville = ville;
    }

    public String getCodeGare() {
        return codeGare;
    }

    public void setCodeGare(String codeGare) {
        this.codeGare = codeGare;
    }

    public String getAdresse() {
        return adresse;
    }

    public void setAdresse(String adresse) {
        this.adresse = adresse;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public List<Trajet> getTrajetsDepart() {
        return trajetsDepart;
    }

    public void setTrajetsDepart(List<Trajet> trajetsDepart) {
        this.trajetsDepart = trajetsDepart;
    }

    public List<Trajet> getTrajetsArrivee() {
        return trajetsArrivee;
    }

    public void setTrajetsArrivee(List<Trajet> trajetsArrivee) {
        this.trajetsArrivee = trajetsArrivee;
    }

    // Méthodes utilitaires
    public String getNomComplet() {
        return nom + " (" + ville + ")";
    }

    public boolean hasCoordinates() {
        return latitude != null && longitude != null;
    }

    @Override
    public String toString() {
        return "Gare{" +
                "id=" + id +
                ", nom='" + nom + '\'' +
                ", ville='" + ville + '\'' +
                ", codeGare='" + codeGare + '\'' +
                ", active=" + active +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Gare gare = (Gare) o;
        return Objects.equals(id, gare.id) && Objects.equals(codeGare, gare.codeGare);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, codeGare);
    }
}
