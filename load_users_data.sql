-- Script pour charger des utilisateurs de test
-- Partie 4: Insertion des utilisateurs

USE train_ticket_db;

-- ========================================
-- INSERTION DES UTILISATEURS DE TEST
-- ========================================

-- Utilisateur admin
INSERT INTO utilisateurs (email, mot_de_passe, nom, prenom, telephone, adresse, role, active, date_creation) VALUES
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Admin', 'Système', '+216 70 123 456', 'Siège Social, Tunis', 'ADMIN', TRUE, NOW()),

-- Utilisateurs normaux
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', '<PERSON> <PERSON>', '<PERSON>', '+216 98 123 456', 'Avenue Bourguiba, Tunis', 'USER', TRUE, NOW()),
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Trabelsi', 'Fatma', '+216 97 234 567', 'Rue de la République, Sousse', 'USER', TRUE, NOW()),
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Gharbi', 'Mohamed', '+216 96 345 678', 'Avenue Habib Bourguiba, Sfax', 'USER', TRUE, NOW()),
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Sassi', 'Leila', '+216 95 456 789', 'Rue du 20 Mars, Gabès', 'USER', TRUE, NOW()),
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Khelifi', 'Sami', '+216 94 567 890', 'Avenue de la Liberté, Bizerte', 'USER', TRUE, NOW()),

-- Utilisateurs avec différentes villes
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Ben Salah', 'Mariem', '+216 93 678 901', 'Rue Ibn Khaldoun, Kairouan', 'USER', TRUE, NOW()),
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Jendoubi', 'Karim', '+216 92 789 012', 'Avenue Bourguiba, Jendouba', 'USER', TRUE, NOW()),
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Gafsi', 'Nadia', '+216 91 890 123', 'Rue de la Paix, Gafsa', 'USER', TRUE, NOW()),
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Tozeur', 'Youssef', '+216 90 901 234', 'Oasis Palm, Tozeur', 'USER', TRUE, NOW()),
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Monastiri', 'Amina', '+216 89 012 345', 'Marina Cap Monastir', 'USER', TRUE, NOW()),

-- Utilisateurs pour tests
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Test', 'Utilisateur', '+216 88 123 456', 'Adresse de test', 'USER', TRUE, NOW()),
('<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Ik0J/Hi9Cp5I6oo20VmU5kFX.NSRBSe', 'Demo', 'Compte', '+216 87 234 567', 'Adresse de démonstration', 'USER', TRUE, NOW());

-- Note: Le mot de passe pour tous les comptes est "password123" (hashé avec BCrypt)

SELECT 'Utilisateurs de test insérés avec succès!' as message;
SELECT COUNT(*) as nombre_utilisateurs FROM utilisateurs;
SELECT email, nom, prenom, role FROM utilisateurs ORDER BY role DESC, nom;
