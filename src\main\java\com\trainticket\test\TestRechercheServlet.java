package com.trainticket.test;

import com.trainticket.dao.GareDAO;
import com.trainticket.dao.VoyageDAO;
import com.trainticket.dao.impl.GareDAOImpl;
import com.trainticket.dao.impl.VoyageDAOImpl;
import com.trainticket.model.Gare;
import com.trainticket.model.Voyage;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Servlet de test pour vérifier la recherche de voyages
 */
@WebServlet(name = "TestRechercheServlet", urlPatterns = {"/test/recherche"})
public class TestRechercheServlet extends HttpServlet {

    private GareDAO gareDAO;
    private VoyageDAO voyageDAO;

    @Override
    public void init() throws ServletException {
        super.init();
        this.gareDAO = new GareDAOImpl();
        this.voyageDAO = new VoyageDAOImpl();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();

        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>Test de Recherche de Voyages</title>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 40px; }");
        out.println(".success { color: green; font-weight: bold; }");
        out.println(".error { color: red; font-weight: bold; }");
        out.println(".info { color: blue; }");
        out.println("table { border-collapse: collapse; width: 100%; margin: 20px 0; }");
        out.println("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
        out.println("th { background-color: #f2f2f2; }");
        out.println(".voyage-card { border: 1px solid #ccc; margin: 10px 0; padding: 15px; border-radius: 5px; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        out.println("<h1>🔍 Test de Recherche de Voyages</h1>");

        try {
            // Test 1: Lister toutes les gares
            out.println("<h2>1. Test des Gares Disponibles</h2>");
            List<Gare> gares = gareDAO.findAll();
            out.println("<p class='success'>✅ " + gares.size() + " gares trouvées</p>");

            out.println("<table>");
            out.println("<tr><th>ID</th><th>Nom</th><th>Ville</th><th>Code</th></tr>");
            for (Gare gare : gares) {
                out.println("<tr>");
                out.println("<td>" + gare.getId() + "</td>");
                out.println("<td>" + gare.getNom() + "</td>");
                out.println("<td>" + gare.getVille() + "</td>");
                out.println("<td>" + (gare.getCodeGare() != null ? gare.getCodeGare() : "N/A") + "</td>");
                out.println("</tr>");
            }
            out.println("</table>");

            // Test 2: Recherche de voyages Tunis -> Sousse aujourd'hui
            out.println("<h2>2. Test de Recherche : Tunis → Sousse (Aujourd'hui)</h2>");

            Gare tunis = gareDAO.findByVille("Tunis").stream().findFirst().orElse(null);
            Gare sousse = gareDAO.findByVille("Sousse").stream().findFirst().orElse(null);

            if (tunis != null && sousse != null) {
                LocalDate aujourdhui = LocalDate.now();
                List<Voyage> voyages = voyageDAO.searchVoyages("Tunis", "Sousse", aujourdhui);

                out.println("<p class='info'>Recherche de " + tunis.getNom() + " vers " + sousse.getNom() +
                           " le " + aujourdhui.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) + "</p>");
                out.println("<p class='success'>✅ " + voyages.size() + " voyages trouvés</p>");

                for (Voyage voyage : voyages) {
                    out.println("<div class='voyage-card'>");
                    out.println("<h4>🚂 " + voyage.getTrajet().getDescription() + "</h4>");
                    out.println("<p><strong>Départ:</strong> " + voyage.getDateHeureDepart().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")) + "</p>");
                    out.println("<p><strong>Arrivée:</strong> " + voyage.getDateHeureArrivee().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")) + "</p>");
                    out.println("<p><strong>Prix:</strong> " + voyage.getTrajet().getPrixBase() + " TND</p>");
                    out.println("<p><strong>Places disponibles:</strong> " + voyage.getPlacesDisponibles() + "</p>");
                    out.println("<p><strong>Statut:</strong> " + voyage.getStatut() + "</p>");
                    out.println("</div>");
                }
            } else {
                out.println("<p class='error'>❌ Gares Tunis ou Sousse non trouvées</p>");
            }

            // Test 3: Recherche de voyages Tunis -> Sfax demain
            out.println("<h2>3. Test de Recherche : Tunis → Sfax (Demain)</h2>");

            Gare sfax = gareDAO.findByVille("Sfax").stream().findFirst().orElse(null);

            if (tunis != null && sfax != null) {
                LocalDate demain = LocalDate.now().plusDays(1);
                List<Voyage> voyages = voyageDAO.searchVoyages("Tunis", "Sfax", demain);

                out.println("<p class='info'>Recherche de " + tunis.getNom() + " vers " + sfax.getNom() +
                           " le " + demain.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) + "</p>");
                out.println("<p class='success'>✅ " + voyages.size() + " voyages trouvés</p>");

                for (Voyage voyage : voyages) {
                    out.println("<div class='voyage-card'>");
                    out.println("<h4>🚂 " + voyage.getTrajet().getDescription() + "</h4>");
                    out.println("<p><strong>Départ:</strong> " + voyage.getDateHeureDepart().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")) + "</p>");
                    out.println("<p><strong>Arrivée:</strong> " + voyage.getDateHeureArrivee().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")) + "</p>");
                    out.println("<p><strong>Prix:</strong> " + voyage.getTrajet().getPrixBase() + " TND</p>");
                    out.println("<p><strong>Places disponibles:</strong> " + voyage.getPlacesDisponibles() + "</p>");
                    out.println("</div>");
                }
            } else {
                out.println("<p class='error'>❌ Gares Tunis ou Sfax non trouvées</p>");
            }

            // Test 4: Statistiques générales
            out.println("<h2>4. Statistiques Générales</h2>");

            List<String> villes = gareDAO.findDistinctCities();
            out.println("<p class='info'>🏙️ Villes desservies: " + villes.size() + "</p>");
            out.println("<p class='info'>Villes: " + String.join(", ", villes) + "</p>");

            // Compter les voyages par date
            out.println("<h3>Voyages par date (prochains jours)</h3>");
            for (int i = 0; i < 7; i++) {
                LocalDate date = LocalDate.now().plusDays(i);
                List<Voyage> voyagesDate = voyageDAO.findByDate(date);
                out.println("<p class='info'>" + date.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) +
                           ": " + voyagesDate.size() + " voyages</p>");
            }

        } catch (Exception e) {
            out.println("<p class='error'>❌ Erreur lors du test: " + e.getMessage() + "</p>");
            e.printStackTrace();
        }

        out.println("<hr>");
        out.println("<p><a href='" + request.getContextPath() + "/'>&larr; Retour à l'accueil</a></p>");
        out.println("<p><a href='" + request.getContextPath() + "/recherche'>&rarr; Aller à la recherche</a></p>");
        out.println("</body>");
        out.println("</html>");
    }
}
