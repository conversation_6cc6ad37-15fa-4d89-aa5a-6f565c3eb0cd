<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<%-- Chargement des données dynamiques si pas déjà présentes --%>
<c:if test="${empty messageAccueil}">
    <%
        try {
            // Créer et utiliser le contrôleur pour charger les données
            com.trainticket.controller.AuthController authController =
                new com.trainticket.controller.AuthController();
            authController.loadLoginData(request);
        } catch (Exception e) {
            // En cas d'erreur, définir des valeurs par défaut
            request.setAttribute("messageAccueil", "Bienvenue sur Train Ticket");
            request.setAttribute("nombreVillesDesservies", 15);
            request.setAttribute("nombreTrajetsDisponibles", 50);
            request.setAttribute("nombreUtilisateursInscrits", 1000);
            // System.err.println("Erreur lors du chargement des données de connexion: " + e.getMessage());
        }
    %>
</c:if>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="${pageContext.request.contextPath}/resources/css/style.css" rel="stylesheet">
    <style>
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        .login-header {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .form-floating {
            margin-bottom: 1rem;
        }
        .btn-login {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
        .stat-mini {
            padding: 0.5rem;
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            margin: 0.2rem;
        }
        .stat-mini h6 {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0;
        }
        .stat-mini small {
            font-size: 0.75rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h2><i class="fas fa-train"></i> Train Ticket</h2>
                <p class="mb-2">${messageAccueil}</p>
                <div class="row text-center mt-3">
                    <div class="col-4">
                        <div class="stat-mini">
                            <h6 class="mb-0">${nombreVillesDesservies}</h6>
                            <small>Villes</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-mini">
                            <h6 class="mb-0">${nombreTrajetsDisponibles}</h6>
                            <small>Trajets</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-mini">
                            <h6 class="mb-0">${nombreUtilisateursInscrits}+</h6>
                            <small>Utilisateurs</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="login-body">
                <!-- Messages d'erreur -->
                <c:if test="${not empty errorMessage}">
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> ${errorMessage}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </c:if>

                <!-- Messages de succès -->
                <c:if test="${param.message == 'logout'}">
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> Vous avez été déconnecté avec succès
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </c:if>

                <c:if test="${param.message == 'registered'}">
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> Inscription réussie ! Vous pouvez maintenant vous connecter
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </c:if>

                <form action="${pageContext.request.contextPath}/auth/login" method="post" id="loginForm">
                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email"
                               placeholder="<EMAIL>" value="${email}" required>
                        <label for="email"><i class="fas fa-envelope"></i> Adresse email</label>
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control" id="motDePasse" name="motDePasse"
                               placeholder="Mot de passe" required>
                        <label for="motDePasse"><i class="fas fa-lock"></i> Mot de passe</label>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="rememberMe" name="rememberMe">
                        <label class="form-check-label" for="rememberMe">
                            Se souvenir de moi
                        </label>
                    </div>

                    <!-- Champ caché pour l'URL de redirection -->
                    <c:if test="${not empty param.redirectUrl}">
                        <input type="hidden" name="redirectUrl" value="${param.redirectUrl}">
                    </c:if>

                    <button type="submit" class="btn btn-primary btn-login w-100 mb-3">
                        <i class="fas fa-sign-in-alt"></i> Se connecter
                    </button>
                </form>

                <div class="text-center">
                    <a href="${pageContext.request.contextPath}/auth/forgot-password" class="text-decoration-none">
                        <i class="fas fa-question-circle"></i> Mot de passe oublié ?
                    </a>
                </div>

                <hr class="my-4">

                <div class="text-center">
                    <p class="mb-2">Pas encore de compte ?</p>
                    <a href="${pageContext.request.contextPath}/auth/register"
                       class="btn btn-outline-primary w-100">
                        <i class="fas fa-user-plus"></i> Créer un compte
                    </a>
                </div>

                <div class="text-center mt-3">
                    <a href="${pageContext.request.contextPath}/" class="text-muted text-decoration-none">
                        <i class="fas fa-arrow-left"></i> Retour à l'accueil
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const email = document.getElementById('email').value;
            const password = document.getElementById('motDePasse').value;

            if (!email || !password) {
                e.preventDefault();
                alert('Veuillez remplir tous les champs');
                return;
            }

            // Validation email
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('Veuillez entrer une adresse email valide');
                return;
            }

            // Afficher un indicateur de chargement
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Connexion...';
            submitBtn.disabled = true;

            // En cas d'erreur, restaurer le bouton (sera géré par le rechargement de page)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 5000);
        });

        // Auto-focus sur le champ email
        document.getElementById('email').focus();

        // Gestion de la touche Entrée
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').submit();
            }
        });
    </script>
</body>
</html>
