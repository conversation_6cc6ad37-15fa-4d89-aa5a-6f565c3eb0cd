/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-05-29 16:26:11 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.views.auth;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;

public final class register_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fvar_005fitems;

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fvar_005fitems = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fvar_005fitems.release();
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write('\n');
      //  c:if
      org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent(null);
      // /WEB-INF/views/auth/register.jsp(6,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${empty avantagesInscription}", java.lang.Boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\n");
          out.write("    ");

        try {
            // Créer et utiliser le contrôleur pour charger les données
            com.trainticket.controller.AuthController authController =
                new com.trainticket.controller.AuthController();
            authController.loadRegisterData(request);
        } catch (Exception e) {
            // En cas d'erreur, définir des valeurs par défaut
            java.util.List<String> avantagesDefaut = java.util.Arrays.asList(
                "Réservation rapide et sécurisée",
                "Historique de vos voyages",
                "Offres exclusives"
            );
            request.setAttribute("avantagesInscription", avantagesDefaut);
            request.setAttribute("nombreUtilisateursInscrits", 1000);
            // System.err.println("Erreur lors du chargement des données d'inscription: " + e.getMessage());
        }
    
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
        return;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"fr\">\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
      out.write("    <title>Inscription - Train Ticket</title>\n");
      out.write("    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css\" rel=\"stylesheet\">\n");
      out.write("    <link href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\" rel=\"stylesheet\">\n");
      out.write("    <link href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/resources/css/style.css\" rel=\"stylesheet\">\n");
      out.write("    <style>\n");
      out.write("        .register-container {\n");
      out.write("            min-height: 100vh;\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("            padding: 2rem 0;\n");
      out.write("        }\n");
      out.write("        .register-card {\n");
      out.write("            background: white;\n");
      out.write("            border-radius: 20px;\n");
      out.write("            box-shadow: 0 20px 40px rgba(0,0,0,0.1);\n");
      out.write("            overflow: hidden;\n");
      out.write("            max-width: 600px;\n");
      out.write("            margin: 0 auto;\n");
      out.write("        }\n");
      out.write("        .register-header {\n");
      out.write("            background: linear-gradient(45deg, #007bff, #0056b3);\n");
      out.write("            color: white;\n");
      out.write("            padding: 2rem;\n");
      out.write("            text-align: center;\n");
      out.write("        }\n");
      out.write("        .register-body {\n");
      out.write("            padding: 2rem;\n");
      out.write("        }\n");
      out.write("        .password-strength {\n");
      out.write("            height: 5px;\n");
      out.write("            border-radius: 3px;\n");
      out.write("            margin-top: 5px;\n");
      out.write("            transition: all 0.3s ease;\n");
      out.write("        }\n");
      out.write("        .strength-weak { background-color: #dc3545; }\n");
      out.write("        .strength-medium { background-color: #ffc107; }\n");
      out.write("        .strength-strong { background-color: #28a745; }\n");
      out.write("    </style>\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <div class=\"register-container\">\n");
      out.write("        <div class=\"container\">\n");
      out.write("            <div class=\"register-card\">\n");
      out.write("                <div class=\"register-header\">\n");
      out.write("                    <h2><i class=\"fas fa-user-plus\"></i> Créer un compte</h2>\n");
      out.write("                    <p class=\"mb-3\">Rejoignez ");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${nombreUtilisateursInscrits}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("+ utilisateurs qui font confiance à Train Ticket</p>\n");
      out.write("\n");
      out.write("                    <!-- Avantages de l'inscription -->\n");
      out.write("                    <div class=\"row text-start\">\n");
      out.write("                        ");
      if (_jspx_meth_c_005fforEach_005f0(_jspx_page_context))
        return;
      out.write("\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("\n");
      out.write("                <div class=\"register-body\">\n");
      out.write("                    <!-- Messages d'erreur -->\n");
      out.write("                    ");
      if (_jspx_meth_c_005fif_005f2(_jspx_page_context))
        return;
      out.write("\n");
      out.write("\n");
      out.write("                    <form action=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/auth/register\" method=\"post\"\n");
      out.write("                          id=\"registerForm\" data-validate=\"true\">\n");
      out.write("\n");
      out.write("                        <div class=\"row\">\n");
      out.write("                            <div class=\"col-md-6\">\n");
      out.write("                                <div class=\"form-floating mb-3\">\n");
      out.write("                                    <input type=\"text\" class=\"form-control\" id=\"prenom\" name=\"prenom\"\n");
      out.write("                                           placeholder=\"Prénom\" value=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${prenom}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("\" required>\n");
      out.write("                                    <label for=\"prenom\"><i class=\"fas fa-user\"></i> Prénom</label>\n");
      out.write("                                    <div class=\"invalid-feedback\">\n");
      out.write("                                        Le prénom est requis (minimum 2 caractères)\n");
      out.write("                                    </div>\n");
      out.write("                                </div>\n");
      out.write("                            </div>\n");
      out.write("\n");
      out.write("                            <div class=\"col-md-6\">\n");
      out.write("                                <div class=\"form-floating mb-3\">\n");
      out.write("                                    <input type=\"text\" class=\"form-control\" id=\"nom\" name=\"nom\"\n");
      out.write("                                           placeholder=\"Nom\" value=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${nom}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("\" required>\n");
      out.write("                                    <label for=\"nom\"><i class=\"fas fa-user\"></i> Nom</label>\n");
      out.write("                                    <div class=\"invalid-feedback\">\n");
      out.write("                                        Le nom est requis (minimum 2 caractères)\n");
      out.write("                                    </div>\n");
      out.write("                                </div>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("\n");
      out.write("                        <div class=\"form-floating mb-3\">\n");
      out.write("                            <input type=\"email\" class=\"form-control\" id=\"email\" name=\"email\"\n");
      out.write("                                   placeholder=\"<EMAIL>\" value=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${email}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("\" required>\n");
      out.write("                            <label for=\"email\"><i class=\"fas fa-envelope\"></i> Adresse email</label>\n");
      out.write("                            <div class=\"invalid-feedback\">\n");
      out.write("                                Veuillez entrer une adresse email valide\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("\n");
      out.write("                        <div class=\"form-floating mb-3\">\n");
      out.write("                            <input type=\"tel\" class=\"form-control\" id=\"telephone\" name=\"telephone\"\n");
      out.write("                                   placeholder=\"Téléphone\" value=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${telephone}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("\"\n");
      out.write("                                   pattern=\"^(\\+212|0)[5-7][0-9]{8}$\">\n");
      out.write("                            <label for=\"telephone\"><i class=\"fas fa-phone\"></i> Téléphone (optionnel)</label>\n");
      out.write("                            <div class=\"form-text\">Format: 0612345678 ou +212612345678</div>\n");
      out.write("                            <div class=\"invalid-feedback\">\n");
      out.write("                                Format de téléphone invalide (format marocain)\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("\n");
      out.write("                        <div class=\"form-floating mb-3\">\n");
      out.write("                            <textarea class=\"form-control\" id=\"adresse\" name=\"adresse\"\n");
      out.write("                                      placeholder=\"Adresse\" style=\"height: 80px\">");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${adresse}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("</textarea>\n");
      out.write("                            <label for=\"adresse\"><i class=\"fas fa-map-marker-alt\"></i> Adresse (optionnel)</label>\n");
      out.write("                        </div>\n");
      out.write("\n");
      out.write("                        <div class=\"form-floating mb-3\">\n");
      out.write("                            <input type=\"password\" class=\"form-control\" id=\"motDePasse\" name=\"motDePasse\"\n");
      out.write("                                   placeholder=\"Mot de passe\" required>\n");
      out.write("                            <label for=\"motDePasse\"><i class=\"fas fa-lock\"></i> Mot de passe</label>\n");
      out.write("                            <div class=\"password-strength\" id=\"passwordStrength\"></div>\n");
      out.write("                            <div class=\"form-text\">\n");
      out.write("                                Le mot de passe doit contenir au moins 8 caractères, une majuscule,\n");
      out.write("                                une minuscule, un chiffre et un caractère spécial\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"invalid-feedback\">\n");
      out.write("                                Le mot de passe ne respecte pas les critères de sécurité\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("\n");
      out.write("                        <div class=\"form-floating mb-3\">\n");
      out.write("                            <input type=\"password\" class=\"form-control\" id=\"confirmMotDePasse\"\n");
      out.write("                                   name=\"confirmMotDePasse\" placeholder=\"Confirmer le mot de passe\" required>\n");
      out.write("                            <label for=\"confirmMotDePasse\"><i class=\"fas fa-lock\"></i> Confirmer le mot de passe</label>\n");
      out.write("                            <div class=\"invalid-feedback\">\n");
      out.write("                                Les mots de passe ne correspondent pas\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("\n");
      out.write("                        <div class=\"form-check mb-3\">\n");
      out.write("                            <input class=\"form-check-input\" type=\"checkbox\" id=\"acceptTerms\" required>\n");
      out.write("                            <label class=\"form-check-label\" for=\"acceptTerms\">\n");
      out.write("                                J'accepte les <a href=\"#\" data-bs-toggle=\"modal\" data-bs-target=\"#termsModal\">\n");
      out.write("                                conditions d'utilisation</a> et la\n");
      out.write("                                <a href=\"#\" data-bs-toggle=\"modal\" data-bs-target=\"#privacyModal\">\n");
      out.write("                                politique de confidentialité</a>\n");
      out.write("                            </label>\n");
      out.write("                            <div class=\"invalid-feedback\">\n");
      out.write("                                Vous devez accepter les conditions d'utilisation\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("\n");
      out.write("                        <div class=\"form-check mb-4\">\n");
      out.write("                            <input class=\"form-check-input\" type=\"checkbox\" id=\"newsletter\" name=\"newsletter\">\n");
      out.write("                            <label class=\"form-check-label\" for=\"newsletter\">\n");
      out.write("                                Je souhaite recevoir les offres et actualités par email\n");
      out.write("                            </label>\n");
      out.write("                        </div>\n");
      out.write("\n");
      out.write("                        <button type=\"submit\" class=\"btn btn-primary btn-lg w-100 mb-3\">\n");
      out.write("                            <i class=\"fas fa-user-plus\"></i> Créer mon compte\n");
      out.write("                        </button>\n");
      out.write("                    </form>\n");
      out.write("\n");
      out.write("                    <hr class=\"my-4\">\n");
      out.write("\n");
      out.write("                    <div class=\"text-center\">\n");
      out.write("                        <p class=\"mb-2\">Vous avez déjà un compte ?</p>\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/auth/login\"\n");
      out.write("                           class=\"btn btn-outline-primary w-100\">\n");
      out.write("                            <i class=\"fas fa-sign-in-alt\"></i> Se connecter\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("\n");
      out.write("                    <div class=\"text-center mt-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/\" class=\"text-muted text-decoration-none\">\n");
      out.write("                            <i class=\"fas fa-arrow-left\"></i> Retour à l'accueil\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("\n");
      out.write("    <!-- Modal Conditions d'utilisation -->\n");
      out.write("    <div class=\"modal fade\" id=\"termsModal\" tabindex=\"-1\">\n");
      out.write("        <div class=\"modal-dialog modal-lg\">\n");
      out.write("            <div class=\"modal-content\">\n");
      out.write("                <div class=\"modal-header\">\n");
      out.write("                    <h5 class=\"modal-title\">Conditions d'utilisation</h5>\n");
      out.write("                    <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\"></button>\n");
      out.write("                </div>\n");
      out.write("                <div class=\"modal-body\">\n");
      out.write("                    <h6>1. Acceptation des conditions</h6>\n");
      out.write("                    <p>En utilisant Train Ticket, vous acceptez ces conditions d'utilisation...</p>\n");
      out.write("\n");
      out.write("                    <h6>2. Utilisation du service</h6>\n");
      out.write("                    <p>Vous vous engagez à utiliser le service de manière responsable...</p>\n");
      out.write("\n");
      out.write("                    <h6>3. Réservations et paiements</h6>\n");
      out.write("                    <p>Les réservations sont soumises à disponibilité...</p>\n");
      out.write("                </div>\n");
      out.write("                <div class=\"modal-footer\">\n");
      out.write("                    <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Fermer</button>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("\n");
      out.write("    <!-- Modal Politique de confidentialité -->\n");
      out.write("    <div class=\"modal fade\" id=\"privacyModal\" tabindex=\"-1\">\n");
      out.write("        <div class=\"modal-dialog modal-lg\">\n");
      out.write("            <div class=\"modal-content\">\n");
      out.write("                <div class=\"modal-header\">\n");
      out.write("                    <h5 class=\"modal-title\">Politique de confidentialité</h5>\n");
      out.write("                    <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\"></button>\n");
      out.write("                </div>\n");
      out.write("                <div class=\"modal-body\">\n");
      out.write("                    <h6>1. Collecte des données</h6>\n");
      out.write("                    <p>Nous collectons les informations que vous nous fournissez...</p>\n");
      out.write("\n");
      out.write("                    <h6>2. Utilisation des données</h6>\n");
      out.write("                    <p>Vos données sont utilisées pour traiter vos réservations...</p>\n");
      out.write("\n");
      out.write("                    <h6>3. Protection des données</h6>\n");
      out.write("                    <p>Nous mettons en place des mesures de sécurité appropriées...</p>\n");
      out.write("                </div>\n");
      out.write("                <div class=\"modal-footer\">\n");
      out.write("                    <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Fermer</button>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("\n");
      out.write("    <!-- Scripts -->\n");
      out.write("    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js\"></script>\n");
      out.write("    <script>\n");
      out.write("        // Validation du formulaire\n");
      out.write("        document.getElementById('registerForm').addEventListener('submit', function(e) {\n");
      out.write("            const form = this;\n");
      out.write("\n");
      out.write("            // Validation des mots de passe\n");
      out.write("            const password = document.getElementById('motDePasse').value;\n");
      out.write("            const confirmPassword = document.getElementById('confirmMotDePasse').value;\n");
      out.write("\n");
      out.write("            if (password !== confirmPassword) {\n");
      out.write("                e.preventDefault();\n");
      out.write("                document.getElementById('confirmMotDePasse').classList.add('is-invalid');\n");
      out.write("                return;\n");
      out.write("            }\n");
      out.write("\n");
      out.write("            // Validation de la force du mot de passe\n");
      out.write("            if (!isStrongPassword(password)) {\n");
      out.write("                e.preventDefault();\n");
      out.write("                document.getElementById('motDePasse').classList.add('is-invalid');\n");
      out.write("                return;\n");
      out.write("            }\n");
      out.write("\n");
      out.write("            // Validation des conditions\n");
      out.write("            if (!document.getElementById('acceptTerms').checked) {\n");
      out.write("                e.preventDefault();\n");
      out.write("                document.getElementById('acceptTerms').classList.add('is-invalid');\n");
      out.write("                return;\n");
      out.write("            }\n");
      out.write("\n");
      out.write("            // Afficher un indicateur de chargement\n");
      out.write("            const submitBtn = form.querySelector('button[type=\"submit\"]');\n");
      out.write("            const originalText = submitBtn.innerHTML;\n");
      out.write("            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin\"></i> Création du compte...';\n");
      out.write("            submitBtn.disabled = true;\n");
      out.write("        });\n");
      out.write("\n");
      out.write("        // Vérification de la force du mot de passe\n");
      out.write("        document.getElementById('motDePasse').addEventListener('input', function() {\n");
      out.write("            const password = this.value;\n");
      out.write("            const strengthBar = document.getElementById('passwordStrength');\n");
      out.write("            const strength = getPasswordStrength(password);\n");
      out.write("\n");
      out.write("            strengthBar.className = 'password-strength';\n");
      out.write("\n");
      out.write("            if (password.length > 0) {\n");
      out.write("                if (strength < 3) {\n");
      out.write("                    strengthBar.classList.add('strength-weak');\n");
      out.write("                } else if (strength < 5) {\n");
      out.write("                    strengthBar.classList.add('strength-medium');\n");
      out.write("                } else {\n");
      out.write("                    strengthBar.classList.add('strength-strong');\n");
      out.write("                }\n");
      out.write("            }\n");
      out.write("        });\n");
      out.write("\n");
      out.write("        // Vérification de la correspondance des mots de passe\n");
      out.write("        document.getElementById('confirmMotDePasse').addEventListener('input', function() {\n");
      out.write("            const password = document.getElementById('motDePasse').value;\n");
      out.write("            const confirmPassword = this.value;\n");
      out.write("\n");
      out.write("            if (confirmPassword && password !== confirmPassword) {\n");
      out.write("                this.classList.add('is-invalid');\n");
      out.write("            } else {\n");
      out.write("                this.classList.remove('is-invalid');\n");
      out.write("            }\n");
      out.write("        });\n");
      out.write("\n");
      out.write("        function getPasswordStrength(password) {\n");
      out.write("            let strength = 0;\n");
      out.write("\n");
      out.write("            if (password.length >= 8) strength++;\n");
      out.write("            if (/[a-z]/.test(password)) strength++;\n");
      out.write("            if (/[A-Z]/.test(password)) strength++;\n");
      out.write("            if (/[0-9]/.test(password)) strength++;\n");
      out.write("            if (/[^A-Za-z0-9]/.test(password)) strength++;\n");
      out.write("\n");
      out.write("            return strength;\n");
      out.write("        }\n");
      out.write("\n");
      out.write("        function isStrongPassword(password) {\n");
      out.write("            return getPasswordStrength(password) >= 4;\n");
      out.write("        }\n");
      out.write("\n");
      out.write("        // Auto-focus sur le premier champ\n");
      out.write("        document.getElementById('prenom').focus();\n");
      out.write("    </script>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_c_005fforEach_005f0(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f0 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    _jspx_th_c_005fforEach_005f0.setPageContext(_jspx_page_context);
    _jspx_th_c_005fforEach_005f0.setParent(null);
    // /WEB-INF/views/auth/register.jsp(80,24) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
    _jspx_th_c_005fforEach_005f0.setVar("avantage");
    // /WEB-INF/views/auth/register.jsp(80,24) name = items type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
    _jspx_th_c_005fforEach_005f0.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/views/auth/register.jsp(80,24) '${avantagesInscription}'",_el_expressionfactory.createValueExpression(_jspx_page_context.getELContext(),"${avantagesInscription}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
    // /WEB-INF/views/auth/register.jsp(80,24) name = varStatus type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
    _jspx_th_c_005fforEach_005f0.setVarStatus("status");
    int[] _jspx_push_body_count_c_005fforEach_005f0 = new int[] { 0 };
    try {
      int _jspx_eval_c_005fforEach_005f0 = _jspx_th_c_005fforEach_005f0.doStartTag();
      if (_jspx_eval_c_005fforEach_005f0 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\n");
          out.write("                            ");
          if (_jspx_meth_c_005fif_005f1(_jspx_th_c_005fforEach_005f0, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\n");
          out.write("                        ");
          int evalDoAfterBody = _jspx_th_c_005fforEach_005f0.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fforEach_005f0.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
    } catch (java.lang.Throwable _jspx_exception) {
      while (_jspx_push_body_count_c_005fforEach_005f0[0]-- > 0)
        out = _jspx_page_context.popBody();
      _jspx_th_c_005fforEach_005f0.doCatch(_jspx_exception);
    } finally {
      _jspx_th_c_005fforEach_005f0.doFinally();
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(javax.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f0, javax.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
    _jspx_th_c_005fif_005f1.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f0);
    // /WEB-INF/views/auth/register.jsp(81,28) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
    _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${status.index < 3}", java.lang.Boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false)).booleanValue());
    int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
    if (_jspx_eval_c_005fif_005f1 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
      do {
        out.write("\n");
        out.write("                                <div class=\"col-12 mb-2\">\n");
        out.write("                                    <small><i class=\"fas fa-check-circle me-2\"></i>");
        out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${avantage}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
        out.write("</small>\n");
        out.write("                                </div>\n");
        out.write("                            ");
        int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
        if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
          break;
      } while (true);
    }
    if (_jspx_th_c_005fif_005f1.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      return true;
    }
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
    _jspx_th_c_005fif_005f2.setParent(null);
    // /WEB-INF/views/auth/register.jsp(92,20) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
    _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty errorMessage}", java.lang.Boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false)).booleanValue());
    int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
    if (_jspx_eval_c_005fif_005f2 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
      do {
        out.write("\n");
        out.write("                        <div class=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">\n");
        out.write("                            <i class=\"fas fa-exclamation-triangle\"></i> ");
        out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${errorMessage}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
        out.write("\n");
        out.write("                            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>\n");
        out.write("                        </div>\n");
        out.write("                    ");
        int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
        if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
          break;
      } while (true);
    }
    if (_jspx_th_c_005fif_005f2.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      return true;
    }
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
    return false;
  }
}
