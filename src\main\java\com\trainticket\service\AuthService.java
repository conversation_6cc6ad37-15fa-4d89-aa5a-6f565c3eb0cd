package com.trainticket.service;

import com.trainticket.model.Utilisateur;

/**
 * Service d'authentification et de gestion des utilisateurs
 */
public interface AuthService {
    
    /**
     * Authentifier un utilisateur
     * @param email l'email de l'utilisateur
     * @param motDePasse le mot de passe
     * @return l'utilisateur authentifié
     * @throws AuthenticationException si l'authentification échoue
     */
    Utilisateur authenticate(String email, String motDePasse) throws AuthenticationException;
    
    /**
     * Inscrire un nouvel utilisateur
     * @param utilisateur les données de l'utilisateur
     * @param motDePasse le mot de passe en clair
     * @return l'utilisateur créé
     * @throws RegistrationException si l'inscription échoue
     */
    Utilisateur register(Utilisateur utilisateur, String motDePasse) throws RegistrationException;
    
    /**
     * Changer le mot de passe d'un utilisateur
     * @param utilisateurId l'ID de l'utilisateur
     * @param ancienMotDePasse l'ancien mot de passe
     * @param nouveauMotDePasse le nouveau mot de passe
     * @throws PasswordChangeException si le changement échoue
     */
    void changePassword(Long utilisateurId, String ancienMotDePasse, String nouveauMotDePasse) 
            throws PasswordChangeException;
    
    /**
     * Réinitialiser le mot de passe d'un utilisateur
     * @param email l'email de l'utilisateur
     * @return le nouveau mot de passe temporaire
     * @throws PasswordResetException si la réinitialisation échoue
     */
    String resetPassword(String email) throws PasswordResetException;
    
    /**
     * Mettre à jour la dernière connexion d'un utilisateur
     * @param utilisateurId l'ID de l'utilisateur
     */
    void updateLastLogin(Long utilisateurId);
    
    /**
     * Vérifier si un email est disponible
     * @param email l'email à vérifier
     * @return true si l'email est disponible, false sinon
     */
    boolean isEmailAvailable(String email);
    
    /**
     * Valider les données d'un utilisateur
     * @param utilisateur l'utilisateur à valider
     * @param motDePasse le mot de passe à valider
     * @throws ValidationException si la validation échoue
     */
    void validateUser(Utilisateur utilisateur, String motDePasse) throws ValidationException;
    
    /**
     * Bloquer un utilisateur
     * @param utilisateurId l'ID de l'utilisateur
     * @throws UserManagementException si l'opération échoue
     */
    void blockUser(Long utilisateurId) throws UserManagementException;
    
    /**
     * Débloquer un utilisateur
     * @param utilisateurId l'ID de l'utilisateur
     * @throws UserManagementException si l'opération échoue
     */
    void unblockUser(Long utilisateurId) throws UserManagementException;
    
    /**
     * Vérifier si un utilisateur est actif
     * @param utilisateur l'utilisateur à vérifier
     * @return true si l'utilisateur est actif, false sinon
     */
    boolean isUserActive(Utilisateur utilisateur);
    
    // Exceptions personnalisées
    class AuthenticationException extends Exception {
        public AuthenticationException(String message) {
            super(message);
        }
    }
    
    class RegistrationException extends Exception {
        public RegistrationException(String message) {
            super(message);
        }
        
        public RegistrationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
    
    class PasswordChangeException extends Exception {
        public PasswordChangeException(String message) {
            super(message);
        }
    }
    
    class PasswordResetException extends Exception {
        public PasswordResetException(String message) {
            super(message);
        }
    }
    
    class ValidationException extends Exception {
        public ValidationException(String message) {
            super(message);
        }
    }
    
    class UserManagementException extends Exception {
        public UserManagementException(String message) {
            super(message);
        }
        
        public UserManagementException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
