<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-configuration PUBLIC
        "-//Hibernate/Hibernate Configuration DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-configuration-3.0.dtd">

<hibernate-configuration>
    <session-factory>
        <!-- Database connection settings -->
        <property name="hibernate.connection.driver_class">com.mysql.cj.jdbc.Driver</property>
        <property name="hibernate.connection.url">****************************************************************************************************************************************************************</property>
        <property name="hibernate.connection.username">root</property>
        <property name="hibernate.connection.password">123</property>

        <!-- Connection pool settings -->
        <property name="hibernate.connection.provider_class">com.zaxxer.hikari.hibernate.HikariConnectionProvider</property>
        <property name="hibernate.hikari.minimumIdle">5</property>
        <property name="hibernate.hikari.maximumPoolSize">20</property>
        <property name="hibernate.hikari.idleTimeout">300000</property>
        <property name="hibernate.hikari.connectionTimeout">20000</property>
        <property name="hibernate.hikari.maxLifetime">1200000</property>

        <!-- SQL dialect -->
        <property name="hibernate.dialect">org.hibernate.dialect.MySQL8Dialect</property>

        <!-- Echo all executed SQL to stdout -->
        <property name="hibernate.show_sql">true</property>
        <property name="hibernate.format_sql">true</property>
        <property name="hibernate.use_sql_comments">true</property>

        <!-- Drop and re-create the database schema on startup -->
        <property name="hibernate.hbm2ddl.auto">update</property>

        <!-- Current session context -->
        <property name="hibernate.current_session_context_class">thread</property>

        <!-- Disable the second-level cache -->
        <property name="hibernate.cache.use_second_level_cache">false</property>

        <!-- Additional settings for better performance -->
        <property name="hibernate.jdbc.batch_size">20</property>
        <property name="hibernate.order_inserts">true</property>
        <property name="hibernate.order_updates">true</property>
        <property name="hibernate.jdbc.batch_versioned_data">true</property>

        <!-- Mapping classes -->
        <mapping class="com.trainticket.model.Utilisateur"/>
        <mapping class="com.trainticket.model.Gare"/>
        <mapping class="com.trainticket.model.Trajet"/>
        <mapping class="com.trainticket.model.Voyage"/>
        <mapping class="com.trainticket.model.Billet"/>
        <mapping class="com.trainticket.model.DemandeAnnulation"/>
        <mapping class="com.trainticket.model.Preference"/>

    </session-factory>
</hibernate-configuration>
