<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Résumé de Réservation - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .summary-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .voyage-summary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 10px;
        }
        .price-total {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            position: relative;
        }
        .step.completed {
            background-color: #28a745;
            color: white;
        }
        .step.active {
            background-color: #007bff;
            color: white;
        }
        .step.pending {
            background-color: #e9ecef;
            color: #6c757d;
        }
        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 20px;
            height: 2px;
            background-color: #e9ecef;
            transform: translateY(-50%);
        }
        .step:last-child::after {
            display: none;
        }
        .step.completed::after {
            background-color: #28a745;
        }
    </style>
</head>
<body>
    <%@ include file="../common/header.jsp" %>

    <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="${pageContext.request.contextPath}/">Accueil</a></li>
                <li class="breadcrumb-item"><a href="${pageContext.request.contextPath}/recherche/search">Recherche</a></li>
                <li class="breadcrumb-item active">Résumé de réservation</li>
            </ol>
        </nav>

        <!-- Indicateur d'étapes -->
        <div class="step-indicator">
            <div class="step completed">
                <i class="fas fa-search"></i>
            </div>
            <div class="step completed">
                <i class="fas fa-train"></i>
            </div>
            <div class="step completed">
                <i class="fas fa-cog"></i>
            </div>
            <div class="step active">
                <i class="fas fa-clipboard-check"></i>
            </div>
            <div class="step pending">
                <i class="fas fa-credit-card"></i>
            </div>
        </div>

        <div class="text-center mb-4">
            <h2><i class="fas fa-clipboard-check text-primary"></i> Résumé de votre réservation</h2>
            <p class="text-muted">Vérifiez les détails avant de procéder au paiement</p>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <!-- Voyage aller -->
                <div class="card summary-card mb-4">
                    <div class="card-header voyage-summary">
                        <h5 class="mb-0"><i class="fas fa-plane-departure"></i> Voyage aller</h5>
                    </div>
                    <div class="card-body">
                        <c:if test="${not empty sessionScope.selectedVoyage}">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-map-marker-alt text-success"></i> Départ</h6>
                                    <p class="mb-1"><strong>${sessionScope.selectedVoyage.trajet.gareDepart.ville}</strong></p>
                                    <p class="text-muted small">${sessionScope.selectedVoyage.trajet.gareDepart.nom}</p>
                                    <p class="h6">
                                        <fmt:formatDate value="${sessionScope.selectedVoyage.dateHeureDepart}" pattern="dd/MM/yyyy à HH:mm"/>
                                    </p>
                                </div>
                                <div class="col-md-6 text-end">
                                    <h6><i class="fas fa-map-marker-alt text-danger"></i> Arrivée</h6>
                                    <p class="mb-1"><strong>${sessionScope.selectedVoyage.trajet.gareArrivee.ville}</strong></p>
                                    <p class="text-muted small">${sessionScope.selectedVoyage.trajet.gareArrivee.nom}</p>
                                    <p class="h6">
                                        <fmt:formatDate value="${sessionScope.selectedVoyage.dateHeureArrivee}" pattern="dd/MM/yyyy à HH:mm"/>
                                    </p>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <small class="text-muted">Classe</small>
                                    <p class="fw-bold">
                                        <c:choose>
                                            <c:when test="${sessionScope.selectedClasse == 'PREMIERE'}">
                                                <i class="fas fa-crown text-warning"></i> 1ère Classe
                                            </c:when>
                                            <c:when test="${sessionScope.selectedClasse == 'DEUXIEME'}">
                                                <i class="fas fa-star text-info"></i> 2ème Classe
                                            </c:when>
                                            <c:otherwise>
                                                <i class="fas fa-chair text-secondary"></i> Économique
                                            </c:otherwise>
                                        </c:choose>
                                    </p>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">Passagers</small>
                                    <p class="fw-bold">${sessionScope.nombrePassagers}</p>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">Durée</small>
                                    <p class="fw-bold">${sessionScope.selectedVoyage.trajet.dureeMinutes} min</p>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">Prix unitaire</small>
                                    <p class="fw-bold text-primary">
                                        <c:choose>
                                            <c:when test="${sessionScope.selectedClasse == 'PREMIERE'}">
                                                <fmt:formatNumber value="${sessionScope.selectedVoyage.trajet.prixBase * 1.5}" pattern="#,##0.00"/> TND
                                            </c:when>
                                            <c:when test="${sessionScope.selectedClasse == 'DEUXIEME'}">
                                                <fmt:formatNumber value="${sessionScope.selectedVoyage.trajet.prixBase * 1.2}" pattern="#,##0.00"/> TND
                                            </c:when>
                                            <c:otherwise>
                                                <fmt:formatNumber value="${sessionScope.selectedVoyage.trajet.prixBase}" pattern="#,##0.00"/> TND
                                            </c:otherwise>
                                        </c:choose>
                                    </p>
                                </div>
                            </div>
                        </c:if>
                    </div>
                </div>

                <!-- Voyage retour (si sélectionné) -->
                <c:if test="${not empty sessionScope.selectedReturnVoyage}">
                    <div class="card summary-card mb-4">
                        <div class="card-header" style="background: linear-gradient(135deg, #28a745, #1e7e34); color: white;">
                            <h5 class="mb-0"><i class="fas fa-plane-arrival"></i> Voyage retour</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-map-marker-alt text-success"></i> Départ</h6>
                                    <p class="mb-1"><strong>${sessionScope.selectedReturnVoyage.trajet.gareDepart.ville}</strong></p>
                                    <p class="text-muted small">${sessionScope.selectedReturnVoyage.trajet.gareDepart.nom}</p>
                                    <p class="h6">
                                        <fmt:formatDate value="${sessionScope.selectedReturnVoyage.dateHeureDepart}" pattern="dd/MM/yyyy à HH:mm"/>
                                    </p>
                                </div>
                                <div class="col-md-6 text-end">
                                    <h6><i class="fas fa-map-marker-alt text-danger"></i> Arrivée</h6>
                                    <p class="mb-1"><strong>${sessionScope.selectedReturnVoyage.trajet.gareArrivee.ville}</strong></p>
                                    <p class="text-muted small">${sessionScope.selectedReturnVoyage.trajet.gareArrivee.nom}</p>
                                    <p class="h6">
                                        <fmt:formatDate value="${sessionScope.selectedReturnVoyage.dateHeureArrivee}" pattern="dd/MM/yyyy à HH:mm"/>
                                    </p>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <small class="text-muted">Classe</small>
                                    <p class="fw-bold">
                                        <c:choose>
                                            <c:when test="${sessionScope.selectedReturnClasse == 'PREMIERE'}">
                                                <i class="fas fa-crown text-warning"></i> 1ère Classe
                                            </c:when>
                                            <c:when test="${sessionScope.selectedReturnClasse == 'DEUXIEME'}">
                                                <i class="fas fa-star text-info"></i> 2ème Classe
                                            </c:when>
                                            <c:otherwise>
                                                <i class="fas fa-chair text-secondary"></i> Économique
                                            </c:otherwise>
                                        </c:choose>
                                    </p>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">Passagers</small>
                                    <p class="fw-bold">${sessionScope.nombrePassagers}</p>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">Durée</small>
                                    <p class="fw-bold">${sessionScope.selectedReturnVoyage.trajet.dureeMinutes} min</p>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">Prix unitaire</small>
                                    <p class="fw-bold text-success">
                                        <c:choose>
                                            <c:when test="${sessionScope.selectedReturnClasse == 'PREMIERE'}">
                                                <fmt:formatNumber value="${sessionScope.selectedReturnVoyage.trajet.prixBase * 1.5}" pattern="#,##0.00"/> TND
                                            </c:when>
                                            <c:when test="${sessionScope.selectedReturnClasse == 'DEUXIEME'}">
                                                <fmt:formatNumber value="${sessionScope.selectedReturnVoyage.trajet.prixBase * 1.2}" pattern="#,##0.00"/> TND
                                            </c:when>
                                            <c:otherwise>
                                                <fmt:formatNumber value="${sessionScope.selectedReturnVoyage.trajet.prixBase}" pattern="#,##0.00"/> TND
                                            </c:otherwise>
                                        </c:choose>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </c:if>

                <!-- Préférences -->
                <div class="card summary-card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-heart"></i> Vos préférences</h5>
                    </div>
                    <div class="card-body">
                        <c:if test="${not empty sessionScope.selectedPreferences}">
                            <p><strong>Résumé :</strong> ${sessionScope.selectedPreferences.preferencesResume}</p>
                            
                            <c:if test="${not empty sessionScope.selectedPreferences.commentairesSpeciaux}">
                                <div class="mt-3">
                                    <strong>Demandes spéciales :</strong>
                                    <p class="text-muted">${sessionScope.selectedPreferences.commentairesSpeciaux}</p>
                                </div>
                            </c:if>
                        </c:if>
                    </div>
                </div>

                <!-- Boutons de navigation -->
                <div class="d-flex justify-content-between mb-4">
                    <a href="${pageContext.request.contextPath}/selection/return-trip" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Modifier
                    </a>
                    <form method="post" action="${pageContext.request.contextPath}/selection/confirm-booking" style="display: inline;">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-credit-card"></i> Procéder au paiement
                        </button>
                    </form>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Récapitulatif des prix -->
                <div class="card summary-card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0"><i class="fas fa-calculator"></i> Récapitulatif</h5>
                    </div>
                    <div class="card-body">
                        <c:set var="prixAller" value="0"/>
                        <c:set var="prixRetour" value="0"/>
                        
                        <!-- Calcul prix aller -->
                        <c:if test="${not empty sessionScope.selectedVoyage}">
                            <c:choose>
                                <c:when test="${sessionScope.selectedClasse == 'PREMIERE'}">
                                    <c:set var="prixAller" value="${sessionScope.selectedVoyage.trajet.prixBase * 1.5 * sessionScope.nombrePassagers}"/>
                                </c:when>
                                <c:when test="${sessionScope.selectedClasse == 'DEUXIEME'}">
                                    <c:set var="prixAller" value="${sessionScope.selectedVoyage.trajet.prixBase * 1.2 * sessionScope.nombrePassagers}"/>
                                </c:when>
                                <c:otherwise>
                                    <c:set var="prixAller" value="${sessionScope.selectedVoyage.trajet.prixBase * sessionScope.nombrePassagers}"/>
                                </c:otherwise>
                            </c:choose>
                        </c:if>
                        
                        <!-- Calcul prix retour -->
                        <c:if test="${not empty sessionScope.selectedReturnVoyage}">
                            <c:choose>
                                <c:when test="${sessionScope.selectedReturnClasse == 'PREMIERE'}">
                                    <c:set var="prixRetour" value="${sessionScope.selectedReturnVoyage.trajet.prixBase * 1.5 * sessionScope.nombrePassagers}"/>
                                </c:when>
                                <c:when test="${sessionScope.selectedReturnClasse == 'DEUXIEME'}">
                                    <c:set var="prixRetour" value="${sessionScope.selectedReturnVoyage.trajet.prixBase * 1.2 * sessionScope.nombrePassagers}"/>
                                </c:when>
                                <c:otherwise>
                                    <c:set var="prixRetour" value="${sessionScope.selectedReturnVoyage.trajet.prixBase * sessionScope.nombrePassagers}"/>
                                </c:otherwise>
                            </c:choose>
                        </c:if>
                        
                        <div class="d-flex justify-content-between mb-2">
                            <span>Voyage aller (${sessionScope.nombrePassagers} pers.)</span>
                            <span><fmt:formatNumber value="${prixAller}" pattern="#,##0.00"/> TND</span>
                        </div>
                        
                        <c:if test="${not empty sessionScope.selectedReturnVoyage}">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Voyage retour (${sessionScope.nombrePassagers} pers.)</span>
                                <span><fmt:formatNumber value="${prixRetour}" pattern="#,##0.00"/> TND</span>
                            </div>
                        </c:if>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between">
                            <strong>Total</strong>
                            <span class="price-total">
                                <fmt:formatNumber value="${prixAller + prixRetour}" pattern="#,##0.00"/> TND
                            </span>
                        </div>
                        
                        <!-- Sauvegarder le total en session pour le paiement -->
                        <c:set var="totalPrice" value="${prixAller + prixRetour}" scope="session"/>
                    </div>
                </div>

                <!-- Informations importantes -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle"></i> Informations importantes</h6>
                    </div>
                    <div class="card-body">
                        <div class="small">
                            <p><i class="fas fa-clock text-warning"></i> <strong>Arrivée recommandée :</strong> 30 minutes avant le départ</p>
                            <p><i class="fas fa-id-card text-info"></i> <strong>Pièce d'identité :</strong> Obligatoire pour voyager</p>
                            <p><i class="fas fa-suitcase text-secondary"></i> <strong>Bagages :</strong> 1 bagage cabine gratuit</p>
                            <p><i class="fas fa-mobile-alt text-success"></i> <strong>Billet électronique :</strong> Disponible après paiement</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include file="../common/footer.jsp" %>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
