package com.trainticket.test;

import com.trainticket.util.HibernateUtil;
import com.trainticket.model.Utilisateur;
import org.hibernate.Session;
import org.hibernate.Transaction;

public class HibernateTest {
    
    public static void main(String[] args) {
        try {
            System.out.println("🔄 Test de la configuration Hibernate...");
            
            // Test de création de SessionFactory
            System.out.println("✅ SessionFactory créée avec succès !");
            
            // Test de création d'une session
            Session session = HibernateUtil.getSessionFactory().openSession();
            System.out.println("✅ Session Hibernate créée avec succès !");
            
            // Test de transaction
            Transaction transaction = session.beginTransaction();
            System.out.println("✅ Transaction démarrée avec succès !");
            
            // Test de création d'un utilisateur
            Utilisateur utilisateur = new Utilisateur();
            utilisateur.setEmail("<EMAIL>");
            utilisateur.setMotDePasse("password123");
            utilisateur.setNom("Test");
            utilisateur.setPrenom("Hibernate");
            
            // Sauvegarder l'utilisateur
            session.save(utilisateur);
            transaction.commit();
            System.out.println("✅ Utilisateur sauvegardé avec succès ! ID: " + utilisateur.getId());
            
            // Test de requête
            session = HibernateUtil.getSessionFactory().openSession();
            Utilisateur utilisateurRecupere = session.get(Utilisateur.class, utilisateur.getId());
            System.out.println("✅ Utilisateur récupéré : " + utilisateurRecupere.getEmail());
            
            // Nettoyage
            transaction = session.beginTransaction();
            session.delete(utilisateurRecupere);
            transaction.commit();
            System.out.println("✅ Nettoyage effectué");
            
            session.close();
            System.out.println("✅ Test Hibernate terminé avec succès !");
            
        } catch (Exception e) {
            System.err.println("❌ Erreur lors du test Hibernate :");
            e.printStackTrace();
        }
    }
}
