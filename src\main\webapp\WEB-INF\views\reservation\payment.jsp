<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paiement - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="${pageContext.request.contextPath}/resources/css/style.css" rel="stylesheet">
    
    <style>
        .payment-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        
        .step.completed .step-number {
            background: #28a745;
            color: white;
        }
        
        .step.active .step-number {
            background: #007bff;
            color: white;
        }
        
        .payment-method {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .payment-method:hover {
            border-color: #007bff;
            box-shadow: 0 5px 15px rgba(0,123,255,0.1);
        }
        
        .payment-method.selected {
            border-color: #007bff;
            background: rgba(0,123,255,0.05);
        }
        
        .security-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .order-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            position: sticky;
            top: 20px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <jsp:include page="../common/navbar.jsp" />

    <div class="container my-5">
        <div class="payment-container">
            <!-- Indicateur d'étapes -->
            <div class="step-indicator">
                <div class="step completed">
                    <div class="step-number">1</div>
                    <span>Sélection du voyage</span>
                </div>
                <div class="step completed">
                    <div class="step-number">2</div>
                    <span>Détails de réservation</span>
                </div>
                <div class="step active">
                    <div class="step-number">3</div>
                    <span>Paiement</span>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <span>Confirmation</span>
                </div>
            </div>

            <div class="row">
                <!-- Formulaire de paiement -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-credit-card"></i> Informations de paiement</h5>
                        </div>
                        <div class="card-body">
                            <form id="paymentForm" action="${pageContext.request.contextPath}/reservation/process-payment" method="post">
                                <!-- Méthodes de paiement -->
                                <div class="mb-4">
                                    <h6 class="mb-3">Choisissez votre méthode de paiement</h6>
                                    
                                    <div class="payment-method selected" data-method="card">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-credit-card fa-2x text-primary me-3"></i>
                                            <div>
                                                <h6 class="mb-1">Carte bancaire</h6>
                                                <small class="text-muted">Visa, Mastercard, American Express</small>
                                            </div>
                                            <input type="radio" name="paymentMethod" value="card" class="ms-auto" checked>
                                        </div>
                                    </div>
                                    
                                    <div class="payment-method" data-method="paypal">
                                        <div class="d-flex align-items-center">
                                            <i class="fab fa-paypal fa-2x text-info me-3"></i>
                                            <div>
                                                <h6 class="mb-1">PayPal</h6>
                                                <small class="text-muted">Paiement sécurisé via PayPal</small>
                                            </div>
                                            <input type="radio" name="paymentMethod" value="paypal" class="ms-auto">
                                        </div>
                                    </div>
                                    
                                    <div class="payment-method" data-method="bank">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-university fa-2x text-success me-3"></i>
                                            <div>
                                                <h6 class="mb-1">Virement bancaire</h6>
                                                <small class="text-muted">Paiement par virement (délai 24h)</small>
                                            </div>
                                            <input type="radio" name="paymentMethod" value="bank" class="ms-auto">
                                        </div>
                                    </div>
                                </div>

                                <!-- Détails de la carte (affiché par défaut) -->
                                <div id="cardDetails">
                                    <h6 class="mb-3">Détails de la carte</h6>
                                    <div class="row">
                                        <div class="col-12 mb-3">
                                            <label for="cardNumber" class="form-label">Numéro de carte *</label>
                                            <input type="text" class="form-control" id="cardNumber" name="cardNumber" 
                                                   placeholder="1234 5678 9012 3456" maxlength="19" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="expiryDate" class="form-label">Date d'expiration *</label>
                                            <input type="text" class="form-control" id="expiryDate" name="expiryDate" 
                                                   placeholder="MM/AA" maxlength="5" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="cvv" class="form-label">CVV *</label>
                                            <input type="text" class="form-control" id="cvv" name="cvv" 
                                                   placeholder="123" maxlength="4" required>
                                        </div>
                                        <div class="col-12 mb-3">
                                            <label for="cardName" class="form-label">Nom sur la carte *</label>
                                            <input type="text" class="form-control" id="cardName" name="cardName" 
                                                   placeholder="Nom complet" required>
                                        </div>
                                    </div>
                                </div>

                                <!-- Adresse de facturation -->
                                <div class="mb-4">
                                    <h6 class="mb-3">Adresse de facturation</h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="billingAddress" class="form-label">Adresse *</label>
                                            <input type="text" class="form-control" id="billingAddress" name="billingAddress" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="billingCity" class="form-label">Ville *</label>
                                            <input type="text" class="form-control" id="billingCity" name="billingCity" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="billingPostal" class="form-label">Code postal *</label>
                                            <input type="text" class="form-control" id="billingPostal" name="billingPostal" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="billingCountry" class="form-label">Pays *</label>
                                            <select class="form-select" id="billingCountry" name="billingCountry" required>
                                                <option value="">Sélectionnez un pays</option>
                                                <option value="TN" selected>Tunisie</option>
                                                <option value="FR">France</option>
                                                <option value="MA">Maroc</option>
                                                <option value="DZ">Algérie</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Conditions -->
                                <div class="mb-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="acceptTerms" name="acceptTerms" required>
                                        <label class="form-check-label" for="acceptTerms">
                                            J'accepte les <a href="#" target="_blank">conditions générales</a> et la 
                                            <a href="#" target="_blank">politique de confidentialité</a> *
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter">
                                        <label class="form-check-label" for="newsletter">
                                            Je souhaite recevoir les offres promotionnelles par email
                                        </label>
                                    </div>
                                </div>

                                <div class="security-info">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-shield-alt text-success fa-2x me-3"></i>
                                        <div>
                                            <h6 class="mb-1">Paiement 100% sécurisé</h6>
                                            <small class="text-muted">
                                                Vos données sont protégées par un cryptage SSL 256 bits.
                                                Nous ne stockons aucune information de carte bancaire.
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Résumé de commande -->
                <div class="col-lg-4">
                    <div class="order-summary">
                        <h5 class="mb-3"><i class="fas fa-receipt"></i> Résumé de la commande</h5>
                        
                        <!-- Détails du voyage -->
                        <div class="mb-3">
                            <h6>Voyage</h6>
                            <p class="mb-1">${voyage.trajet.gareDepart.ville} → ${voyage.trajet.gareArrivee.ville}</p>
                            <small class="text-muted">
                                <fmt:formatDate value="${voyage.dateHeureDepart}" pattern="dd/MM/yyyy à HH:mm" />
                            </small>
                        </div>
                        
                        <!-- Passager -->
                        <div class="mb-3">
                            <h6>Passager</h6>
                            <p class="mb-1">${reservationPrenom} ${reservationNom}</p>
                            <small class="text-muted">Classe: ${reservationClasse}</small>
                        </div>
                        
                        <!-- Prix -->
                        <hr>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Billet</span>
                            <span>${reservationPrixBillet} DT</span>
                        </div>
                        
                        <c:if test="${not empty reservationOptions}">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Options</span>
                                <span>${reservationPrixOptions} DT</span>
                            </div>
                        </c:if>
                        
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Total</strong>
                            <strong>${reservationPrixTotal} DT</strong>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" form="paymentForm" class="btn btn-success btn-lg">
                                <i class="fas fa-lock"></i> Payer ${reservationPrixTotal} DT
                            </button>
                            <a href="${pageContext.request.contextPath}/reservation/select-voyage?voyageId=${voyage.id}" 
                               class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Modifier la réservation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <jsp:include page="../common/footer.jsp" />

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/main.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const paymentMethods = document.querySelectorAll('.payment-method');
            const cardDetails = document.getElementById('cardDetails');
            
            // Gestion de la sélection de méthode de paiement
            paymentMethods.forEach(method => {
                method.addEventListener('click', function() {
                    // Retirer la sélection précédente
                    paymentMethods.forEach(m => m.classList.remove('selected'));
                    
                    // Ajouter la sélection actuelle
                    this.classList.add('selected');
                    this.querySelector('input[type="radio"]').checked = true;
                    
                    // Afficher/masquer les détails de carte
                    const selectedMethod = this.dataset.method;
                    if (selectedMethod === 'card') {
                        cardDetails.style.display = 'block';
                    } else {
                        cardDetails.style.display = 'none';
                    }
                });
            });
            
            // Formatage du numéro de carte
            const cardNumberInput = document.getElementById('cardNumber');
            if (cardNumberInput) {
                cardNumberInput.addEventListener('input', function() {
                    let value = this.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
                    let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
                    this.value = formattedValue;
                });
            }
            
            // Formatage de la date d'expiration
            const expiryInput = document.getElementById('expiryDate');
            if (expiryInput) {
                expiryInput.addEventListener('input', function() {
                    let value = this.value.replace(/\D/g, '');
                    if (value.length >= 2) {
                        value = value.substring(0, 2) + '/' + value.substring(2, 4);
                    }
                    this.value = value;
                });
            }
            
            // Validation du formulaire
            document.getElementById('paymentForm').addEventListener('submit', function(e) {
                const selectedMethod = document.querySelector('input[name="paymentMethod"]:checked').value;
                
                if (selectedMethod === 'card') {
                    const cardNumber = document.getElementById('cardNumber').value.replace(/\s/g, '');
                    const expiryDate = document.getElementById('expiryDate').value;
                    const cvv = document.getElementById('cvv').value;
                    
                    if (cardNumber.length < 13 || cardNumber.length > 19) {
                        e.preventDefault();
                        alert('Numéro de carte invalide');
                        return;
                    }
                    
                    if (!/^\d{2}\/\d{2}$/.test(expiryDate)) {
                        e.preventDefault();
                        alert('Date d\'expiration invalide (MM/AA)');
                        return;
                    }
                    
                    if (cvv.length < 3 || cvv.length > 4) {
                        e.preventDefault();
                        alert('CVV invalide');
                        return;
                    }
                }
                
                // Simulation du traitement
                const submitBtn = this.querySelector('button[type="submit"]');
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Traitement en cours...';
                submitBtn.disabled = true;
            });
        });
    </script>
</body>
</html>
