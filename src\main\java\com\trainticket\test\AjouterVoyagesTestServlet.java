package com.trainticket.test;

import com.trainticket.util.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.Transaction;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@WebServlet("/test/ajouter-voyages")
public class AjouterVoyagesTestServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("text/html; charset=UTF-8");
        PrintWriter out = response.getWriter();

        out.println("<!DOCTYPE html>");
        out.println("<html><head><title>Ajouter Voyages de Test</title>");
        out.println("<meta charset='UTF-8'>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 20px; }");
        out.println(".success { color: green; }");
        out.println(".error { color: red; }");
        out.println(".section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }");
        out.println("</style>");
        out.println("</head><body>");
        out.println("<h1>Ajouter Voyages de Test</h1>");

        Transaction transaction = null;
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            transaction = session.beginTransaction();

            out.println("<div class='section'>");
            out.println("<h2>Ajout de voyages de test</h2>");

            // Obtenir les trajets existants
            String trajetsQuery = "SELECT t.id, t.numero_trajet, gd.ville as ville_depart, ga.ville as ville_arrivee " +
                                 "FROM trajets t " +
                                 "JOIN gares gd ON t.gare_depart_id = gd.id " +
                                 "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
                                 "WHERE t.active = true " +
                                 "ORDER BY gd.ville, ga.ville";

            @SuppressWarnings("unchecked")
            List<Object[]> trajets = session.createNativeQuery(trajetsQuery).getResultList();

            out.println("<p>Trajets disponibles: " + trajets.size() + "</p>");

            // Ajouter des voyages pour les 7 prochains jours
            LocalDateTime maintenant = LocalDateTime.now();
            int voyagesAjoutes = 0;

            for (int jour = 1; jour <= 7; jour++) {
                LocalDateTime dateVoyage = maintenant.plusDays(jour);

                for (Object[] trajet : trajets) {
                    Long trajetId = ((Number) trajet[0]).longValue();
                    String numeroTrajet = (String) trajet[1];
                    String villeDepart = (String) trajet[2];
                    String villeArrivee = (String) trajet[3];

                    // Ajouter 2-3 voyages par jour pour chaque trajet
                    for (int voyage = 0; voyage < 3; voyage++) {
                        LocalDateTime heureDepart = dateVoyage.withHour(6 + voyage * 4).withMinute(0).withSecond(0);

                        // Vérifier si le voyage existe déjà
                        String checkQuery = "SELECT COUNT(*) FROM voyages WHERE trajet_id = ? AND date_heure_depart = ?";
                        Long count = (Long) session.createNativeQuery(checkQuery)
                                                 .setParameter(1, trajetId)
                                                 .setParameter(2, heureDepart)
                                                 .getSingleResult();

                        if (count == 0) {
                            // Calculer l'heure d'arrivée (durée par défaut: 2h)
                            LocalDateTime heureArrivee = heureDepart.plusHours(2);

                            String insertQuery = "INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, " +
                                               "places_disponibles, places_premiere_classe, places_deuxieme_classe, " +
                                               "places_economique, statut) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

                            session.createNativeQuery(insertQuery)
                                   .setParameter(1, trajetId)
                                   .setParameter(2, heureDepart)
                                   .setParameter(3, heureArrivee)
                                   .setParameter(4, 200) // places_disponibles
                                   .setParameter(5, 20)  // places_premiere_classe
                                   .setParameter(6, 60)  // places_deuxieme_classe
                                   .setParameter(7, 120) // places_economique
                                   .setParameter(8, "PROGRAMME") // statut
                                   .executeUpdate();

                            voyagesAjoutes++;

                            out.println("<p class='success'>✅ Voyage ajouté: " + numeroTrajet +
                                       " (" + villeDepart + " → " + villeArrivee + ") le " +
                                       heureDepart.format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")) + "</p>");
                        }
                    }
                }
            }

            transaction.commit();

            out.println("<h3 class='success'>✅ " + voyagesAjoutes + " voyages ajoutés avec succès!</h3>");
            out.println("</div>");

            // Afficher un résumé
            out.println("<div class='section'>");
            out.println("<h2>Résumé des voyages</h2>");

            String resumeQuery = "SELECT COUNT(*) as total, " +
                               "COUNT(CASE WHEN statut = 'PROGRAMME' THEN 1 END) as programmes, " +
                               "COUNT(CASE WHEN places_disponibles > 0 THEN 1 END) as disponibles " +
                               "FROM voyages";

            Object[] resume = (Object[]) session.createNativeQuery(resumeQuery).getSingleResult();

            out.println("<p><strong>Total voyages:</strong> " + resume[0] + "</p>");
            out.println("<p><strong>Voyages programmés:</strong> " + resume[1] + "</p>");
            out.println("<p><strong>Voyages disponibles:</strong> " + resume[2] + "</p>");
            out.println("</div>");

        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }

            out.println("<div class='section'>");
            out.println("<h2 class='error'>Erreur</h2>");
            out.println("<p class='error'>❌ " + e.getMessage() + "</p>");
            out.println("<pre>");
            e.printStackTrace(out);
            out.println("</pre>");
            out.println("</div>");
        }

        out.println("<hr>");
        out.println("<p><a href='diagnostic-db'>Diagnostic base de données</a></p>");
        out.println("<p><a href='recherche-avancee'>Test de recherche avancée</a></p>");
        out.println("<p><a href='../recherche/search'>Page de recherche</a></p>");
        out.println("</body></html>");
    }
}
