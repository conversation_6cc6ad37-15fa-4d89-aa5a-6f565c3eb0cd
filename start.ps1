Write-Host "🚂 Démarrage du serveur Train Ticket Management System..." -ForegroundColor Green
Write-Host ""

# Vérifier si Maven Wrapper existe
if (Test-Path "mvnw.cmd") {
    Write-Host "✅ Maven Wrapper trouvé" -ForegroundColor Green
} else {
    Write-Host "❌ Maven Wrapper non trouvé" -ForegroundColor Red
    exit 1
}

# Démarrer le serveur Tomcat
Write-Host "🔄 Compilation et démarrage du serveur..." -ForegroundColor Yellow
Write-Host ""

try {
    & .\mvnw.cmd tomcat7:run
} catch {
    Write-Host "❌ Erreur lors du démarrage: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Appuyez sur Entrée pour continuer..."
}
