-- ========================================
-- CONFIGURATION MYSQL POUR TRAIN TICKET
-- ========================================

-- Se connecter en tant que root et exécuter ces commandes

-- Créer la base de données si elle n'existe pas
CREATE DATABASE IF NOT EXISTS train_ticket_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Utiliser la base
USE train_ticket_db;

-- Créer/modifier l'utilisateur root pour permettre les connexions locales
-- (Choisir UNE des options ci-dessous selon votre configuration MySQL)

-- OPTION 1: Permettre à root de se connecter sans mot de passe (pour développement)
-- ALTER USER 'root'@'localhost' IDENTIFIED BY '';
-- FLUSH PRIVILEGES;

-- OPTION 2: Définir un mot de passe pour root (recommandé)
-- ALTER USER 'root'@'localhost' IDENTIFIED BY 'root';
-- FLUSH PRIVILEGES;

-- OPTION 3: Créer un utilisateur spécifique pour l'application
CREATE USER IF NOT EXISTS 'trainapp'@'localhost' IDENTIFIED BY '';
GRANT ALL PRIVILEGES ON train_ticket_db.* TO 'trainapp'@'localhost';
FLUSH PRIVILEGES;

-- Vérifier les utilisateurs
SELECT User, Host, authentication_string FROM mysql.user WHERE User IN ('root', 'trainapp');

-- Créer les tables si elles n'existent pas
CREATE TABLE IF NOT EXISTS utilisateurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    motDePasse VARCHAR(255) NOT NULL,
    telephone VARCHAR(20),
    dateNaissance DATE,
    role ENUM('CLIENT', 'ADMIN') DEFAULT 'CLIENT',
    active BOOLEAN DEFAULT TRUE,
    dateCreation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    derniereConnexion TIMESTAMP NULL
);

CREATE TABLE IF NOT EXISTS gares (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    ville VARCHAR(100) NOT NULL,
    code_gare VARCHAR(10) UNIQUE NOT NULL,
    adresse TEXT,
    latitude DECIMAL(10, 6),
    longitude DECIMAL(10, 6),
    active BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS trajets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero_trajet VARCHAR(20) UNIQUE NOT NULL,
    gare_depart_id INT NOT NULL,
    gare_arrivee_id INT NOT NULL,
    duree_minutes INT NOT NULL,
    prix_base DECIMAL(10, 2) NOT NULL,
    capacite_total INT NOT NULL,
    type ENUM('EXPRESS', 'DIRECT', 'NORMAL') DEFAULT 'NORMAL',
    description TEXT,
    active BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (gare_depart_id) REFERENCES gares(id),
    FOREIGN KEY (gare_arrivee_id) REFERENCES gares(id)
);

CREATE TABLE IF NOT EXISTS voyages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trajet_id INT NOT NULL,
    date_heure_depart DATETIME NOT NULL,
    date_heure_arrivee DATETIME NOT NULL,
    places_disponibles INT NOT NULL,
    places_premiere_classe INT DEFAULT 0,
    places_deuxieme_classe INT DEFAULT 0,
    places_economique INT DEFAULT 0,
    statut ENUM('PROGRAMME', 'EN_COURS', 'TERMINE', 'ANNULE') DEFAULT 'PROGRAMME',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trajet_id) REFERENCES trajets(id)
);

CREATE TABLE IF NOT EXISTS billets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    utilisateur_id INT NOT NULL,
    voyage_id INT NOT NULL,
    numero_billet VARCHAR(50) UNIQUE NOT NULL,
    classe ENUM('PREMIERE', 'DEUXIEME', 'ECONOMIQUE') NOT NULL,
    prix DECIMAL(10, 2) NOT NULL,
    statut ENUM('RESERVE', 'PAYE', 'UTILISE', 'ANNULE') DEFAULT 'RESERVE',
    date_reservation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id),
    FOREIGN KEY (voyage_id) REFERENCES voyages(id)
);

-- Insérer un utilisateur admin par défaut
INSERT IGNORE INTO utilisateurs (nom, prenom, email, motDePasse, role) 
VALUES ('Admin', 'System', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9P2.nHb8GjU5cWO', 'ADMIN');

-- Afficher le statut
SELECT 'Configuration MySQL terminée!' as message;
SELECT 'Tables créées:' as info;
SHOW TABLES;

SELECT 'Utilisateurs configurés:' as info;
SELECT User, Host FROM mysql.user WHERE User IN ('root', 'trainapp');
