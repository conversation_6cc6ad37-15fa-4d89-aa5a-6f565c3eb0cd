package com.trainticket.dao.impl;

import com.trainticket.dao.BaseDAO;
import com.trainticket.util.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import java.lang.reflect.ParameterizedType;
import java.util.List;
import java.util.Optional;

/**
 * Implémentation de base pour tous les DAO
 * @param <T> Type de l'entité
 * @param <ID> Type de l'identifiant
 */
public abstract class BaseDAOImpl<T, ID> implements BaseDAO<T, ID> {

    protected final Logger logger = LoggerFactory.getLogger(getClass());
    protected final Class<T> entityClass;

    @SuppressWarnings("unchecked")
    public BaseDAOImpl() {
        this.entityClass = (Class<T>) ((ParameterizedType) getClass()
                .getGenericSuperclass()).getActualTypeArguments()[0];
    }

    @Override
    public T save(T entity) {
        Transaction transaction = null;
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            transaction = session.beginTransaction();
            session.save(entity);
            transaction.commit();
            logger.debug("Entité {} sauvegardée avec succès", entityClass.getSimpleName());
            return entity;
        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            logger.error("Erreur lors de la sauvegarde de l'entité {}", entityClass.getSimpleName(), e);
            throw new RuntimeException("Erreur lors de la sauvegarde", e);
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public T update(T entity) {
        Transaction transaction = null;
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            transaction = session.beginTransaction();
            T updatedEntity = (T) session.merge(entity);
            transaction.commit();
            logger.debug("Entité {} mise à jour avec succès", entityClass.getSimpleName());
            return updatedEntity;
        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            logger.error("Erreur lors de la mise à jour de l'entité {}", entityClass.getSimpleName(), e);
            throw new RuntimeException("Erreur lors de la mise à jour", e);
        }
    }

    @Override
    public void deleteById(ID id) {
        Transaction transaction = null;
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            transaction = session.beginTransaction();
            T entity = session.get(entityClass, (java.io.Serializable) id);
            if (entity != null) {
                session.delete(entity);
                logger.debug("Entité {} avec ID {} supprimée", entityClass.getSimpleName(), id);
            }
            transaction.commit();
        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            logger.error("Erreur lors de la suppression de l'entité {} avec ID {}",
                        entityClass.getSimpleName(), id, e);
            throw new RuntimeException("Erreur lors de la suppression", e);
        }
    }

    @Override
    public void delete(T entity) {
        Transaction transaction = null;
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            transaction = session.beginTransaction();
            session.delete(entity);
            transaction.commit();
            logger.debug("Entité {} supprimée avec succès", entityClass.getSimpleName());
        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            logger.error("Erreur lors de la suppression de l'entité {}", entityClass.getSimpleName(), e);
            throw new RuntimeException("Erreur lors de la suppression", e);
        }
    }

    @Override
    public Optional<T> findById(ID id) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            T entity = session.get(entityClass, (java.io.Serializable) id);
            return Optional.ofNullable(entity);
        } catch (Exception e) {
            logger.error("Erreur lors de la recherche de l'entité {} avec ID {}",
                        entityClass.getSimpleName(), id, e);
            return Optional.empty();
        }
    }

    @Override
    public List<T> findAll() {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            CriteriaBuilder cb = session.getCriteriaBuilder();
            CriteriaQuery<T> cq = cb.createQuery(entityClass);
            Root<T> root = cq.from(entityClass);
            cq.select(root);

            Query<T> query = session.createQuery(cq);
            return query.getResultList();
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération de toutes les entités {}",
                        entityClass.getSimpleName(), e);
            throw new RuntimeException("Erreur lors de la récupération", e);
        }
    }

    @Override
    public long count() {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            CriteriaBuilder cb = session.getCriteriaBuilder();
            CriteriaQuery<Long> cq = cb.createQuery(Long.class);
            Root<T> root = cq.from(entityClass);
            cq.select(cb.count(root));

            Query<Long> query = session.createQuery(cq);
            return query.getSingleResult();
        } catch (Exception e) {
            logger.error("Erreur lors du comptage des entités {}", entityClass.getSimpleName(), e);
            return 0;
        }
    }

    @Override
    public boolean existsById(ID id) {
        return findById(id).isPresent();
    }

    /**
     * Méthode utilitaire pour exécuter une requête HQL
     * @param hql la requête HQL
     * @param parameters les paramètres de la requête
     * @return liste des résultats
     */
    protected List<T> executeQuery(String hql, Object... parameters) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            Query<T> query = session.createQuery(hql, entityClass);
            for (int i = 0; i < parameters.length; i++) {
                query.setParameter(i, parameters[i]);
            }
            return query.getResultList();
        } catch (Exception e) {
            logger.error("Erreur lors de l'exécution de la requête HQL: {}", hql, e);
            throw new RuntimeException("Erreur lors de l'exécution de la requête", e);
        }
    }

    /**
     * Méthode utilitaire pour exécuter une requête HQL avec un seul résultat
     * @param hql la requête HQL
     * @param parameters les paramètres de la requête
     * @return Optional contenant le résultat
     */
    protected Optional<T> executeSingleResultQuery(String hql, Object... parameters) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            Query<T> query = session.createQuery(hql, entityClass);
            for (int i = 0; i < parameters.length; i++) {
                query.setParameter(i, parameters[i]);
            }
            List<T> results = query.getResultList();
            return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
        } catch (Exception e) {
            logger.error("Erreur lors de l'exécution de la requête HQL: {}", hql, e);
            return Optional.empty();
        }
    }

    /**
     * Méthode utilitaire pour exécuter une requête de comptage
     * @param hql la requête HQL de comptage
     * @param parameters les paramètres de la requête
     * @return le nombre de résultats
     */
    protected long executeCountQuery(String hql, Object... parameters) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            Query<Long> query = session.createQuery(hql, Long.class);
            for (int i = 0; i < parameters.length; i++) {
                query.setParameter(i, parameters[i]);
            }
            Long result = query.getSingleResult();
            return result != null ? result : 0;
        } catch (Exception e) {
            logger.error("Erreur lors de l'exécution de la requête de comptage: {}", hql, e);
            return 0;
        }
    }

    /**
     * Méthode utilitaire pour exécuter une requête de mise à jour
     * @param hql la requête HQL de mise à jour
     * @param parameters les paramètres de la requête
     * @return le nombre de lignes affectées
     */
    protected int executeUpdateQuery(String hql, Object... parameters) {
        Transaction transaction = null;
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            transaction = session.beginTransaction();
            Query<?> query = session.createQuery(hql);
            for (int i = 0; i < parameters.length; i++) {
                query.setParameter(i, parameters[i]);
            }
            int result = query.executeUpdate();
            transaction.commit();
            return result;
        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            logger.error("Erreur lors de l'exécution de la requête de mise à jour: {}", hql, e);
            throw new RuntimeException("Erreur lors de la mise à jour", e);
        }
    }
}
