package com.trainticket.test;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

@WebServlet("/check-data")
public class CheckDataServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();

        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>🔍 Vérification des données</title>");
        out.println("<meta charset='UTF-8'>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }");
        out.println(".container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }");
        out.println(".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        out.println(".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        out.println(".info { color: #0066cc; background: #cce7ff; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        out.println("h1 { color: #333; text-align: center; }");
        out.println("h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }");
        out.println("table { width: 100%; border-collapse: collapse; margin: 10px 0; }");
        out.println("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
        out.println("th { background-color: #f2f2f2; }");
        out.println(".btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        out.println("<div class='container'>");
        out.println("<h1>🔍 Vérification des données de la base</h1>");

        Connection conn = null;
        Statement stmt = null;

        try {
            // Connexion à la base de données - Essayer plusieurs configurations
            String url = "************************************************************************************************************************************************";
            String username = "root";
            String password = "";

            Class.forName("com.mysql.cj.jdbc.Driver");

            // Essayer différentes configurations de connexion
            try {
                conn = DriverManager.getConnection(url, username, password);
            } catch (Exception e1) {
                try {
                    conn = DriverManager.getConnection(url + "&allowEmptyPasswords=true", username, password);
                } catch (Exception e2) {
                    password = "root";
                    conn = DriverManager.getConnection(url, username, password);
                }
            }
            stmt = conn.createStatement();

            out.println("<h2>🚉 Gares dans la base de données</h2>");

            ResultSet rsGares = stmt.executeQuery("SELECT * FROM gares ORDER BY id");
            out.println("<table>");
            out.println("<tr><th>ID</th><th>Nom</th><th>Ville</th><th>Code</th><th>Adresse</th><th>Coordonnées</th></tr>");

            int countGares = 0;
            while (rsGares.next()) {
                countGares++;
                out.println("<tr>");
                out.println("<td>" + rsGares.getInt("id") + "</td>");
                out.println("<td>" + rsGares.getString("nom") + "</td>");
                out.println("<td><strong>" + rsGares.getString("ville") + "</strong></td>");
                out.println("<td>" + rsGares.getString("code_gare") + "</td>");
                out.println("<td>" + rsGares.getString("adresse") + "</td>");
                out.println("<td>" + rsGares.getDouble("latitude") + ", " + rsGares.getDouble("longitude") + "</td>");
                out.println("</tr>");
            }
            out.println("</table>");
            rsGares.close();

            out.println("<div class='info'>Total des gares: " + countGares + "</div>");

            out.println("<h2>🚄 Trajets dans la base de données</h2>");

            ResultSet rsTrajets = stmt.executeQuery(
                "SELECT t.*, gd.ville as ville_depart, ga.ville as ville_arrivee " +
                "FROM trajets t " +
                "JOIN gares gd ON t.gare_depart_id = gd.id " +
                "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
                "ORDER BY t.numero_trajet"
            );

            out.println("<table>");
            out.println("<tr><th>Numéro</th><th>Liaison</th><th>Durée</th><th>Prix</th><th>Type</th><th>Capacité</th></tr>");

            int countTrajets = 0;
            while (rsTrajets.next()) {
                countTrajets++;
                out.println("<tr>");
                out.println("<td>" + rsTrajets.getString("numero_trajet") + "</td>");
                out.println("<td><strong>" + rsTrajets.getString("ville_depart") + " → " + rsTrajets.getString("ville_arrivee") + "</strong></td>");
                out.println("<td>" + rsTrajets.getInt("duree_minutes") + " min</td>");
                out.println("<td>" + rsTrajets.getDouble("prix_base") + " TND</td>");
                out.println("<td>" + rsTrajets.getString("type") + "</td>");
                out.println("<td>" + rsTrajets.getInt("capacite_total") + "</td>");
                out.println("</tr>");
            }
            out.println("</table>");
            rsTrajets.close();

            out.println("<div class='info'>Total des trajets: " + countTrajets + "</div>");

            out.println("<h2>🕐 Voyages programmés</h2>");

            ResultSet rsVoyages = stmt.executeQuery("SELECT COUNT(*) as total FROM voyages");
            int countVoyages = 0;
            if (rsVoyages.next()) {
                countVoyages = rsVoyages.getInt("total");
            }
            rsVoyages.close();

            out.println("<div class='info'>Total des voyages: " + countVoyages + "</div>");

            out.println("<h2>📊 Résumé</h2>");

            if (countGares >= 10 && countTrajets >= 15) {
                out.println("<div class='success'>");
                out.println("<h3>✅ Données tunisiennes correctement chargées!</h3>");
                out.println("<ul>");
                out.println("<li>Gares: " + countGares + "</li>");
                out.println("<li>Trajets: " + countTrajets + "</li>");
                out.println("<li>Voyages: " + countVoyages + "</li>");
                out.println("</ul>");
                out.println("</div>");
            } else {
                out.println("<div class='error'>");
                out.println("<h3>❌ Problème détecté!</h3>");
                out.println("<p>Les données tunisiennes ne semblent pas être correctement chargées.</p>");
                out.println("<p>Gares: " + countGares + " (attendu: 10+)</p>");
                out.println("<p>Trajets: " + countTrajets + " (attendu: 15+)</p>");
                out.println("</div>");
            }

        } catch (Exception e) {
            out.println("<div class='error'>");
            out.println("<h2>❌ Erreur de connexion</h2>");
            out.println("<p>Erreur: " + e.getMessage() + "</p>");
            out.println("</div>");
            e.printStackTrace();
        } finally {
            try {
                if (stmt != null) stmt.close();
                if (conn != null) conn.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        out.println("<p>");
        out.println("<a href='/train-ticket/' class='btn'>🏠 Accueil</a>");
        out.println("<a href='/train-ticket/simple-tunisia-update' class='btn'>🔄 Mettre à jour</a>");
        out.println("</p>");
        out.println("</div>");
        out.println("</body>");
        out.println("</html>");
    }
}
