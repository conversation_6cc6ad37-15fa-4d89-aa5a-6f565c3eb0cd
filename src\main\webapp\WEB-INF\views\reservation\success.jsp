<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Réservation confirmée - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="${pageContext.request.contextPath}/resources/css/style.css" rel="stylesheet">
    
    <style>
        .success-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        .success-icon {
            font-size: 5rem;
            color: #28a745;
            margin-bottom: 2rem;
            animation: bounce 1s ease-in-out;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            60% {
                transform: translateY(-10px);
            }
        }
        
        .ticket-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            position: relative;
            overflow: hidden;
        }
        
        .ticket-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
        }
        
        .ticket-content {
            position: relative;
            z-index: 1;
        }
        
        .qr-code {
            width: 120px;
            height: 120px;
            background: white;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #28a745;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        
        .action-buttons {
            margin-top: 2rem;
        }
        
        .print-button {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .print-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <jsp:include page="../common/navbar.jsp" />

    <div class="container my-5">
        <div class="success-container">
            <!-- Indicateur d'étapes -->
            <div class="step-indicator">
                <div class="step">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <span>Sélection du voyage</span>
                </div>
                <div class="step">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <span>Détails de réservation</span>
                </div>
                <div class="step">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <span>Paiement</span>
                </div>
                <div class="step">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <span>Confirmation</span>
                </div>
            </div>

            <!-- Icône de succès -->
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>

            <!-- Message de succès -->
            <h1 class="text-success mb-3">Réservation confirmée !</h1>
            <p class="lead text-muted mb-4">
                Félicitations ! Votre billet de train a été réservé avec succès.
                Vous recevrez un email de confirmation à l'adresse indiquée.
            </p>

            <!-- Billet électronique -->
            <c:if test="${not empty billetConfirme}">
                <div class="ticket-card">
                    <div class="ticket-content">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="text-start">
                                    <h4 class="mb-3">
                                        <i class="fas fa-ticket-alt"></i> 
                                        Billet électronique
                                    </h4>
                                    
                                    <div class="row">
                                        <div class="col-6">
                                            <h6>Numéro de billet</h6>
                                            <p class="mb-3"><strong>${billetConfirme.numeroBillet}</strong></p>
                                            
                                            <h6>Passager</h6>
                                            <p class="mb-3">${billetConfirme.utilisateur.prenom} ${billetConfirme.utilisateur.nom}</p>
                                            
                                            <h6>Classe</h6>
                                            <p class="mb-3">
                                                <span class="badge bg-light text-dark">${billetConfirme.classe}</span>
                                            </p>
                                        </div>
                                        <div class="col-6">
                                            <h6>Trajet</h6>
                                            <p class="mb-1">
                                                <strong>${billetConfirme.voyage.trajet.gareDepart.ville}</strong>
                                                <i class="fas fa-arrow-right mx-2"></i>
                                                <strong>${billetConfirme.voyage.trajet.gareArrivee.ville}</strong>
                                            </p>
                                            <small class="opacity-75">Train ${billetConfirme.voyage.trajet.numeroTrajet}</small>
                                            
                                            <h6 class="mt-3">Date et heure</h6>
                                            <p class="mb-1">
                                                <fmt:formatDate value="${billetConfirme.voyage.dateHeureDepart}" 
                                                              pattern="EEEE dd MMMM yyyy" />
                                            </p>
                                            <p class="mb-3">
                                                Départ: <strong>
                                                    <fmt:formatDate value="${billetConfirme.voyage.dateHeureDepart}" 
                                                                  pattern="HH:mm" />
                                                </strong>
                                                - Arrivée: <strong>
                                                    <fmt:formatDate value="${billetConfirme.voyage.dateHeureArrivee}" 
                                                                  pattern="HH:mm" />
                                                </strong>
                                            </p>
                                            
                                            <h6>Prix payé</h6>
                                            <p class="mb-0">
                                                <strong><fmt:formatNumber value="${billetConfirme.prix}" type="currency" currencySymbol="DT" /></strong>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="qr-code">
                                    <i class="fas fa-qrcode fa-3x text-dark"></i>
                                </div>
                                <small class="opacity-75">Code QR pour validation</small>
                            </div>
                        </div>
                    </div>
                </div>
            </c:if>

            <!-- Informations importantes -->
            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <i class="fas fa-mobile-alt fa-2x text-primary mb-3"></i>
                            <h6>Billet mobile</h6>
                            <p class="small text-muted">
                                Présentez ce billet sur votre téléphone ou imprimez-le
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <i class="fas fa-clock fa-2x text-warning mb-3"></i>
                            <h6>Arrivée en gare</h6>
                            <p class="small text-muted">
                                Présentez-vous 30 minutes avant le départ
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <i class="fas fa-id-card fa-2x text-info mb-3"></i>
                            <h6>Pièce d'identité</h6>
                            <p class="small text-muted">
                                N'oubliez pas votre pièce d'identité valide
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Boutons d'action -->
            <div class="action-buttons">
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <button onclick="window.print()" class="btn print-button">
                        <i class="fas fa-print"></i> Imprimer le billet
                    </button>
                    <button onclick="downloadPDF()" class="btn btn-outline-primary">
                        <i class="fas fa-download"></i> Télécharger PDF
                    </button>
                    <button onclick="sendByEmail()" class="btn btn-outline-success">
                        <i class="fas fa-envelope"></i> Envoyer par email
                    </button>
                </div>
                
                <div class="mt-3">
                    <a href="${pageContext.request.contextPath}/recherche/search" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-search"></i> Nouvelle recherche
                    </a>
                    <a href="${pageContext.request.contextPath}/" class="btn btn-outline-primary">
                        <i class="fas fa-home"></i> Retour à l'accueil
                    </a>
                </div>
            </div>

            <!-- Aide et support -->
            <div class="mt-5 p-4 bg-light rounded">
                <h6><i class="fas fa-question-circle"></i> Besoin d'aide ?</h6>
                <p class="mb-2">
                    Pour toute question concernant votre réservation, contactez notre service client :
                </p>
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-1">
                            <i class="fas fa-phone text-primary"></i> 
                            <strong>+216 70 123 456</strong>
                        </p>
                        <small class="text-muted">Lun-Ven 8h-18h, Sam 9h-16h</small>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1">
                            <i class="fas fa-envelope text-primary"></i> 
                            <strong><EMAIL></strong>
                        </p>
                        <small class="text-muted">Réponse sous 24h</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <jsp:include page="../common/footer.jsp" />

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/main.js"></script>
    
    <script>
        // Fonction pour télécharger le PDF
        function downloadPDF() {
            // Simulation - en réalité, on générerait un PDF côté serveur
            alert('Fonctionnalité de téléchargement PDF en cours de développement');
        }
        
        // Fonction pour envoyer par email
        function sendByEmail() {
            // Simulation - en réalité, on enverrait l'email côté serveur
            alert('Un email de confirmation sera envoyé à votre adresse');
        }
        
        // Animation d'entrée
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.card, .ticket-card');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    element.style.transition = 'all 0.6s ease';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
        
        // Styles d'impression
        const printStyles = `
            @media print {
                .navbar, .footer, .action-buttons, .mt-5 { display: none !important; }
                .ticket-card { break-inside: avoid; }
                body { font-size: 12px; }
            }
        `;
        
        const styleSheet = document.createElement('style');
        styleSheet.textContent = printStyles;
        document.head.appendChild(styleSheet);
    </script>
</body>
</html>
