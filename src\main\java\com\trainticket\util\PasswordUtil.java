package com.trainticket.util;

import org.mindrot.jbcrypt.BCrypt;

public class PasswordUtil {
    
    private static final int ROUNDS = 12;
    
    /**
     * Hache un mot de passe en utilisant BCrypt
     * @param plainPassword le mot de passe en clair
     * @return le mot de passe haché
     */
    public static String hashPassword(String plainPassword) {
        if (plainPassword == null || plainPassword.trim().isEmpty()) {
            throw new IllegalArgumentException("Le mot de passe ne peut pas être vide");
        }
        return BCrypt.hashpw(plainPassword, BCrypt.gensalt(ROUNDS));
    }
    
    /**
     * Vérifie si un mot de passe correspond au hash
     * @param plainPassword le mot de passe en clair
     * @param hashedPassword le mot de passe haché
     * @return true si le mot de passe correspond, false sinon
     */
    public static boolean checkPassword(String plainPassword, String hashedPassword) {
        if (plainPassword == null || hashedPassword == null) {
            return false;
        }
        try {
            return BCrypt.checkpw(plainPassword, hashedPassword);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Valide la force d'un mot de passe
     * @param password le mot de passe à valider
     * @return true si le mot de passe est valide, false sinon
     */
    public static boolean isValidPassword(String password) {
        if (password == null || password.length() < 8) {
            return false;
        }
        
        boolean hasUpper = false;
        boolean hasLower = false;
        boolean hasDigit = false;
        boolean hasSpecial = false;
        
        for (char c : password.toCharArray()) {
            if (Character.isUpperCase(c)) {
                hasUpper = true;
            } else if (Character.isLowerCase(c)) {
                hasLower = true;
            } else if (Character.isDigit(c)) {
                hasDigit = true;
            } else if (!Character.isLetterOrDigit(c)) {
                hasSpecial = true;
            }
        }
        
        return hasUpper && hasLower && hasDigit && hasSpecial;
    }
    
    /**
     * Génère un message d'erreur pour un mot de passe invalide
     * @param password le mot de passe à valider
     * @return le message d'erreur ou null si le mot de passe est valide
     */
    public static String getPasswordValidationMessage(String password) {
        if (password == null || password.trim().isEmpty()) {
            return "Le mot de passe est requis";
        }
        
        if (password.length() < 8) {
            return "Le mot de passe doit contenir au moins 8 caractères";
        }
        
        if (!isValidPassword(password)) {
            return "Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial";
        }
        
        return null;
    }
}
