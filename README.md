# Train Ticket Management System

## Description
Application JEE complète de gestion d'achat de tickets de train avec architecture MVC, utilisant Servlets, JSP, JSTL, Hibernate et MySQL.

## Technologies utilisées
- **Backend**: Java EE, Servlets, JSP, JSTL, EL
- **ORM**: Hibernate 5.6.15
- **Base de données**: MySQL 8.0
- **Frontend**: Bootstrap 5, Font Awesome, JavaScript
- **Build**: Maven 3.x
- **Serveur**: Tomcat 9.x

## Prérequis
- JDK 11 ou supérieur
- Maven 3.6+
- MySQL 8.0+
- Tom<PERSON> 9.x
- IDE (Eclipse, IntelliJ IDEA, VS Code)

## Installation et Configuration

### 1. Configuration de la base de données
```sql
-- Créer la base de données
CREATE DATABASE train_ticket_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- <PERSON><PERSON>er un utilisateur (optionnel)
CREATE USER 'trainticket'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON train_ticket_db.* TO 'trainticket'@'localhost';
FLUSH PRIVILEGES;

-- Exécuter le script de création des tables
SOURCE db.sql;
```

### 2. Configuration Hibernate
Modifier `src/main/resources/hibernate.cfg.xml` si nécessaire :
```xml
<property name="hibernate.connection.url">*******************************************</property>
<property name="hibernate.connection.username">root</property>
<property name="hibernate.connection.password">votre_mot_de_passe</property>
```

### 3. Compilation et déploiement
```bash
# Compiler le projet
mvn clean compile

# Créer le fichier WAR
mvn clean package

# Déployer sur Tomcat (copier le WAR dans webapps/)
cp target/train-ticket-management.war $TOMCAT_HOME/webapps/

# Ou utiliser le plugin Maven Tomcat
mvn tomcat7:run
```

## Résolution des erreurs courantes

### Erreur de version Java
**Problème**: "Build path specifies execution environment JavaSE-11"

**Solution**:
1. Vérifier que JDK 11 est installé
2. Dans Eclipse: Project Properties → Java Build Path → Libraries → Modulepath/Classpath → JRE System Library → Edit → Workspace default JRE
3. Ou modifier `.classpath` pour pointer vers la bonne version

### Erreurs de compilation Hibernate
**Problème**: Erreurs de cast ou de types génériques

**Solutions appliquées**:
- Ajout de `@SuppressWarnings("unchecked")` pour les casts sécurisés
- Correction des types génériques dans les requêtes HQL
- Utilisation de `(java.io.Serializable)` pour les IDs

### Erreurs de méthodes manquantes
**Problème**: Méthodes appelées mais non déclarées dans les interfaces

**Solutions appliquées**:
- Ajout des méthodes manquantes dans les interfaces DAO
- Implémentation des méthodes dans les classes d'implémentation
- Correction des signatures de méthodes

## Structure du projet

```
src/
├── main/
│   ├── java/
│   │   └── com/trainticket/
│   │       ├── controller/     # Servlets (MVC Controllers)
│   │       ├── dao/           # Data Access Objects
│   │       ├── model/         # Entités JPA/Hibernate
│   │       ├── service/       # Services métier
│   │       └── util/          # Classes utilitaires
│   ├── resources/
│   │   └── hibernate.cfg.xml  # Configuration Hibernate
│   └── webapp/
│       ├── WEB-INF/
│       │   ├── views/         # Pages JSP
│       │   └── web.xml        # Configuration web
│       ├── resources/         # CSS, JS, images
│       └── index.jsp          # Page d'accueil
├── db.sql                     # Script de base de données
└── pom.xml                    # Configuration Maven
```

## Fonctionnalités

### Authentification
- Inscription et connexion utilisateur
- Gestion des rôles (USER, ADMIN)
- Hachage sécurisé des mots de passe
- Sessions utilisateur

### Recherche de voyages
- Recherche par ville de départ/arrivée et date
- Filtres avancés (prix, durée, type de train)
- Voyages directs et avec correspondance
- Vérification de disponibilité en temps réel

### Réservation et paiement
- Sélection de classe (Économique, 2ème, 1ère)
- Préférences de voyage (fenêtre, couloir, etc.)
- Simulation de paiement sécurisé
- Codes promo et réductions
- Génération de billets électroniques

### Administration
- Tableau de bord avec statistiques
- Gestion des utilisateurs
- Gestion des gares, trajets et voyages
- Rapports financiers
- API REST pour les données

## URLs principales

- **Accueil**: `/`
- **Connexion**: `/auth/login`
- **Inscription**: `/auth/register`
- **Recherche**: `/recherche/search`
- **Réservation**: `/reservation/select`
- **Paiement**: `/paiement/checkout`
- **Profil**: `/user/profile`
- **Administration**: `/admin/dashboard`

## Tests

```bash
# Exécuter les tests unitaires
mvn test

# Exécuter les tests avec couverture
mvn test jacoco:report
```

## Sécurité

- Protection CSRF avec tokens
- Validation des entrées utilisateur
- Hachage des mots de passe avec BCrypt
- Sessions sécurisées
- Protection contre l'injection SQL (requêtes paramétrées)

## Performance

- Pool de connexions HikariCP
- Cache de second niveau Hibernate (désactivé par défaut)
- Pagination des résultats
- Requêtes optimisées avec jointures

## Maintenance

### Logs
Les logs sont configurés avec Logback dans `src/main/resources/logback.xml`

### Monitoring
- Statistiques d'utilisation dans le tableau de bord admin
- Logs des transactions de paiement
- Suivi des performances des requêtes

## Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/nouvelle-fonctionnalite`)
3. Commit les changements (`git commit -am 'Ajout nouvelle fonctionnalité'`)
4. Push vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. Créer une Pull Request

## Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## Support

Pour toute question ou problème, créer une issue sur GitHub ou contacter l'équipe de développement.
