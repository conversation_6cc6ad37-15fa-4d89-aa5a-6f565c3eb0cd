package com.trainticket.test;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;

@WebServlet("/setup-mysql")
public class SetupMySQLServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>🔧 Configuration MySQL</title>");
        out.println("<meta charset='UTF-8'>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }");
        out.println(".container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }");
        out.println(".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        out.println(".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        out.println(".info { color: #0066cc; background: #cce7ff; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        out.println(".warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        out.println("h1 { color: #333; text-align: center; }");
        out.println("h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }");
        out.println(".btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px; }");
        out.println("pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        out.println("<div class='container'>");
        out.println("<h1>🔧 Configuration automatique de MySQL</h1>");
        
        Connection conn = null;
        Statement stmt = null;
        boolean success = false;
        
        try {
            out.println("<h2>🔍 Test de connexion MySQL...</h2>");
            
            // Essayer différentes configurations
            String[] passwords = {"", "root", "password", "admin"};
            String url = "*****************************************************************************************";
            String username = "root";
            
            Class.forName("com.mysql.cj.jdbc.Driver");
            
            for (String password : passwords) {
                try {
                    conn = DriverManager.getConnection(url, username, password);
                    out.println("<div class='success'>✅ Connexion réussie avec le mot de passe: " + (password.isEmpty() ? "(vide)" : password) + "</div>");
                    success = true;
                    break;
                } catch (Exception e) {
                    out.println("<div class='info'>⚠️ Échec avec mot de passe '" + (password.isEmpty() ? "(vide)" : password) + "': " + e.getMessage() + "</div>");
                }
            }
            
            if (!success) {
                out.println("<div class='error'>");
                out.println("<h3>❌ Impossible de se connecter à MySQL</h3>");
                out.println("<p>Vérifiez que:</p>");
                out.println("<ul>");
                out.println("<li>MySQL est démarré</li>");
                out.println("<li>Le port 3306 est accessible</li>");
                out.println("<li>L'utilisateur root existe</li>");
                out.println("</ul>");
                out.println("<h4>🛠️ Solutions possibles:</h4>");
                out.println("<pre>");
                out.println("# Démarrer MySQL (Windows)");
                out.println("net start mysql");
                out.println("");
                out.println("# Ou via XAMPP/WAMP");
                out.println("# Démarrer MySQL depuis le panneau de contrôle");
                out.println("");
                out.println("# Réinitialiser le mot de passe root");
                out.println("mysql -u root");
                out.println("ALTER USER 'root'@'localhost' IDENTIFIED BY 'root';");
                out.println("FLUSH PRIVILEGES;");
                out.println("</pre>");
                out.println("</div>");
                return;
            }
            
            stmt = conn.createStatement();
            
            out.println("<h2>🗄️ Configuration de la base de données...</h2>");
            
            // Créer la base de données
            try {
                stmt.executeUpdate("CREATE DATABASE IF NOT EXISTS train_ticket_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                out.println("<div class='success'>✅ Base de données 'train_ticket_db' créée/vérifiée</div>");
            } catch (Exception e) {
                out.println("<div class='error'>❌ Erreur création base: " + e.getMessage() + "</div>");
            }
            
            // Utiliser la base
            stmt.executeUpdate("USE train_ticket_db");
            
            // Créer les tables principales
            out.println("<h3>📋 Création des tables...</h3>");
            
            // Table utilisateurs
            try {
                stmt.executeUpdate(
                    "CREATE TABLE IF NOT EXISTS utilisateurs (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "nom VARCHAR(100) NOT NULL, " +
                    "prenom VARCHAR(100) NOT NULL, " +
                    "email VARCHAR(255) UNIQUE NOT NULL, " +
                    "motDePasse VARCHAR(255) NOT NULL, " +
                    "telephone VARCHAR(20), " +
                    "dateNaissance DATE, " +
                    "role ENUM('CLIENT', 'ADMIN') DEFAULT 'CLIENT', " +
                    "active BOOLEAN DEFAULT TRUE, " +
                    "dateCreation TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                    "derniereConnexion TIMESTAMP NULL" +
                    ")"
                );
                out.println("<div class='success'>✅ Table 'utilisateurs' créée</div>");
            } catch (Exception e) {
                out.println("<div class='error'>❌ Erreur table utilisateurs: " + e.getMessage() + "</div>");
            }
            
            // Table gares
            try {
                stmt.executeUpdate(
                    "CREATE TABLE IF NOT EXISTS gares (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "nom VARCHAR(255) NOT NULL, " +
                    "ville VARCHAR(100) NOT NULL, " +
                    "code_gare VARCHAR(10) UNIQUE NOT NULL, " +
                    "adresse TEXT, " +
                    "latitude DECIMAL(10, 6), " +
                    "longitude DECIMAL(10, 6), " +
                    "active BOOLEAN DEFAULT TRUE, " +
                    "date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                    "date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" +
                    ")"
                );
                out.println("<div class='success'>✅ Table 'gares' créée</div>");
            } catch (Exception e) {
                out.println("<div class='error'>❌ Erreur table gares: " + e.getMessage() + "</div>");
            }
            
            // Table trajets
            try {
                stmt.executeUpdate(
                    "CREATE TABLE IF NOT EXISTS trajets (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "numero_trajet VARCHAR(20) UNIQUE NOT NULL, " +
                    "gare_depart_id INT NOT NULL, " +
                    "gare_arrivee_id INT NOT NULL, " +
                    "duree_minutes INT NOT NULL, " +
                    "prix_base DECIMAL(10, 2) NOT NULL, " +
                    "capacite_total INT NOT NULL, " +
                    "type ENUM('EXPRESS', 'DIRECT', 'NORMAL') DEFAULT 'NORMAL', " +
                    "description TEXT, " +
                    "active BOOLEAN DEFAULT TRUE, " +
                    "date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                    "FOREIGN KEY (gare_depart_id) REFERENCES gares(id), " +
                    "FOREIGN KEY (gare_arrivee_id) REFERENCES gares(id)" +
                    ")"
                );
                out.println("<div class='success'>✅ Table 'trajets' créée</div>");
            } catch (Exception e) {
                out.println("<div class='error'>❌ Erreur table trajets: " + e.getMessage() + "</div>");
            }
            
            // Table voyages
            try {
                stmt.executeUpdate(
                    "CREATE TABLE IF NOT EXISTS voyages (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "trajet_id INT NOT NULL, " +
                    "date_heure_depart DATETIME NOT NULL, " +
                    "date_heure_arrivee DATETIME NOT NULL, " +
                    "places_disponibles INT NOT NULL, " +
                    "places_premiere_classe INT DEFAULT 0, " +
                    "places_deuxieme_classe INT DEFAULT 0, " +
                    "places_economique INT DEFAULT 0, " +
                    "statut ENUM('PROGRAMME', 'EN_COURS', 'TERMINE', 'ANNULE') DEFAULT 'PROGRAMME', " +
                    "date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                    "FOREIGN KEY (trajet_id) REFERENCES trajets(id)" +
                    ")"
                );
                out.println("<div class='success'>✅ Table 'voyages' créée</div>");
            } catch (Exception e) {
                out.println("<div class='error'>❌ Erreur table voyages: " + e.getMessage() + "</div>");
            }
            
            // Table billets
            try {
                stmt.executeUpdate(
                    "CREATE TABLE IF NOT EXISTS billets (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "utilisateur_id INT NOT NULL, " +
                    "voyage_id INT NOT NULL, " +
                    "numero_billet VARCHAR(50) UNIQUE NOT NULL, " +
                    "classe ENUM('PREMIERE', 'DEUXIEME', 'ECONOMIQUE') NOT NULL, " +
                    "prix DECIMAL(10, 2) NOT NULL, " +
                    "statut ENUM('RESERVE', 'PAYE', 'UTILISE', 'ANNULE') DEFAULT 'RESERVE', " +
                    "date_reservation TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                    "FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id), " +
                    "FOREIGN KEY (voyage_id) REFERENCES voyages(id)" +
                    ")"
                );
                out.println("<div class='success'>✅ Table 'billets' créée</div>");
            } catch (Exception e) {
                out.println("<div class='error'>❌ Erreur table billets: " + e.getMessage() + "</div>");
            }
            
            out.println("<h2>🎉 Configuration terminée!</h2>");
            out.println("<div class='success'>");
            out.println("<h3>✅ MySQL est maintenant configuré pour Train Ticket</h3>");
            out.println("<p>Vous pouvez maintenant:</p>");
            out.println("<ul>");
            out.println("<li>Mettre à jour avec les données tunisiennes</li>");
            out.println("<li>Utiliser l'application normalement</li>");
            out.println("<li>Créer des comptes utilisateurs</li>");
            out.println("</ul>");
            out.println("</div>");
            
        } catch (Exception e) {
            out.println("<div class='error'>");
            out.println("<h2>❌ Erreur de configuration</h2>");
            out.println("<p>Erreur: " + e.getMessage() + "</p>");
            out.println("</div>");
            e.printStackTrace();
        } finally {
            try {
                if (stmt != null) stmt.close();
                if (conn != null) conn.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        
        out.println("<p>");
        out.println("<a href='/train-ticket/' class='btn'>🏠 Accueil</a>");
        out.println("<a href='/train-ticket/simple-tunisia-update' class='btn'>🇹🇳 Données tunisiennes</a>");
        out.println("<a href='/train-ticket/check-data' class='btn'>📊 Vérifier données</a>");
        out.println("<a href='/train-ticket/mysql-diagnostic' class='btn'>🔧 Diagnostic</a>");
        out.println("</p>");
        out.println("</div>");
        out.println("</body>");
        out.println("</html>");
    }
}
