<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Résultats de recherche - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="${pageContext.request.contextPath}/resources/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <jsp:include page="../common/navbar.jsp" />

    <!-- En-tête des résultats -->
    <section class="bg-light py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-1">
                        <i class="fas fa-search text-primary"></i> Résultats de recherche
                    </h2>
                    <p class="text-muted mb-0">
                        ${villeDepart} → ${villeArrivee} le
                        <fmt:formatDate value="${dateVoyage}" pattern="dd MMMM yyyy" />
                        pour ${nombrePassagers} passager(s)
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="${pageContext.request.contextPath}/recherche/search" class="btn btn-outline-primary">
                        <i class="fas fa-edit"></i> Modifier la recherche
                    </a>
                </div>
            </div>
        </div>
    </section>

    <div class="container my-4">
        <div class="row">
            <!-- Filtres (sidebar) -->
            <div class="col-lg-3 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-filter"></i> Filtres</h5>
                    </div>
                    <div class="card-body">
                        <form id="filterForm">
                            <!-- Filtre par prix -->
                            <div class="mb-3">
                                <label class="form-label">Prix maximum</label>
                                <input type="range" class="form-range" id="prixRange" min="0" max="1000" step="50" value="1000">
                                <div class="d-flex justify-content-between">
                                    <small>0 MAD</small>
                                    <small id="prixValue">1000 MAD</small>
                                </div>
                            </div>

                            <!-- Filtre par durée -->
                            <div class="mb-3">
                                <label class="form-label">Durée maximale</label>
                                <input type="range" class="form-range" id="dureeRange" min="1" max="12" step="1" value="12">
                                <div class="d-flex justify-content-between">
                                    <small>1h</small>
                                    <small id="dureeValue">12h</small>
                                </div>
                            </div>

                            <!-- Filtre par type -->
                            <div class="mb-3">
                                <label class="form-label">Type de trajet</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="typeExpress" value="EXPRESS" checked>
                                    <label class="form-check-label" for="typeExpress">Express</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="typeDirect" value="DIRECT" checked>
                                    <label class="form-check-label" for="typeDirect">Direct</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="typeNormal" value="NORMAL" checked>
                                    <label class="form-check-label" for="typeNormal">Normal</label>
                                </div>
                            </div>

                            <!-- Filtre par classe -->
                            <div class="mb-3">
                                <label class="form-label">Classes disponibles</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="classeEco" value="ECONOMIQUE" checked>
                                    <label class="form-check-label" for="classeEco">Économique</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="classe2" value="DEUXIEME" checked>
                                    <label class="form-check-label" for="classe2">Deuxième classe</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="classe1" value="PREMIERE" checked>
                                    <label class="form-check-label" for="classe1">Première classe</label>
                                </div>
                            </div>

                            <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="resetFilters()">
                                <i class="fas fa-undo"></i> Réinitialiser
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Résultats -->
            <div class="col-lg-9">
                <!-- Tri -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <p class="mb-0">
                        <strong>${fn:length(voyages)}</strong> voyage(s) trouvé(s)
                    </p>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                data-bs-toggle="dropdown">
                            <i class="fas fa-sort"></i> Trier par
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="sortResults('prix')">Prix croissant</a></li>
                            <li><a class="dropdown-item" href="#" onclick="sortResults('duree')">Durée croissante</a></li>
                            <li><a class="dropdown-item" href="#" onclick="sortResults('heure')">Heure de départ</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Liste des voyages directs -->
                <c:if test="${not empty voyages}">
                    <div id="voyagesList">
                        <c:forEach var="voyage" items="${voyages}" varStatus="status">
                            <div class="card voyage-card mb-3" data-prix="${voyage.trajet.prixBase}"
                                 data-duree="${voyage.trajet.dureeMinutes}" data-type="${voyage.trajet.type}">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <div class="voyage-time">
                                                    <fmt:formatDate value="${voyage.dateHeureDepart}" pattern="HH:mm" />
                                                </div>
                                                <small class="text-muted">${voyage.trajet.gareDepart.ville}</small>
                                            </div>
                                        </div>

                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <i class="fas fa-arrow-right text-primary"></i>
                                                <div class="voyage-duration">
                                                    <fmt:formatNumber value="${voyage.trajet.dureeMinutes / 60}"
                                                                    maxFractionDigits="0" />h
                                                    <fmt:formatNumber value="${voyage.trajet.dureeMinutes % 60}"
                                                                    maxFractionDigits="0" />min
                                                </div>
                                                <span class="badge bg-${voyage.trajet.type == 'EXPRESS' ? 'danger' : voyage.trajet.type == 'DIRECT' ? 'warning' : 'secondary'}">
                                                    ${voyage.trajet.type}
                                                </span>
                                            </div>
                                        </div>

                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <div class="voyage-time">
                                                    <fmt:formatDate value="${voyage.dateHeureArrivee}" pattern="HH:mm" />
                                                </div>
                                                <small class="text-muted">${voyage.trajet.gareArrivee.ville}</small>
                                            </div>
                                        </div>

                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <div class="voyage-price">
                                                    À partir de ${voyage.trajet.prixBase} MAD
                                                </div>
                                                <small class="text-success">
                                                    <i class="fas fa-check-circle"></i>
                                                    ${voyage.placesDisponibles} place(s) disponible(s)
                                                </small>
                                                <div class="mt-2">
                                                    <button class="btn btn-primary btn-sm"
                                                            onclick="selectVoyage(${voyage.id})">
                                                        <i class="fas fa-ticket-alt"></i> Réserver
                                                    </button>
                                                    <button class="btn btn-outline-info btn-sm ms-1"
                                                            onclick="showVoyageDetails(${voyage.id})">
                                                        <i class="fas fa-info-circle"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Détails des classes -->
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <div class="d-flex justify-content-center gap-3">
                                                <c:if test="${voyage.placesEconomique > 0}">
                                                    <span class="badge classe-economique">
                                                        Économique: ${voyage.placesEconomique} places
                                                    </span>
                                                </c:if>
                                                <c:if test="${voyage.placesDeuxiemeClasse > 0}">
                                                    <span class="badge classe-deuxieme">
                                                        2ème classe: ${voyage.placesDeuxiemeClasse} places
                                                    </span>
                                                </c:if>
                                                <c:if test="${voyage.placesPremiereClasse > 0}">
                                                    <span class="badge classe-premiere">
                                                        1ère classe: ${voyage.placesPremiereClasse} places
                                                    </span>
                                                </c:if>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </c:if>

                <!-- Voyages avec correspondance -->
                <c:if test="${not empty correspondances}">
                    <div class="mt-5">
                        <h4><i class="fas fa-exchange-alt"></i> Voyages avec correspondance</h4>
                        <c:forEach var="correspondance" items="${correspondances}" varStatus="status">
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="row">
                                        <c:forEach var="voyage" items="${correspondance}" varStatus="voyageStatus">
                                            <div class="col-md-${voyageStatus.index == 0 ? '5' : '5'}">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <strong>
                                                            <fmt:formatDate value="${voyage.dateHeureDepart}" pattern="HH:mm" />
                                                        </strong>
                                                        <br>
                                                        <small>${voyage.trajet.gareDepart.ville}</small>
                                                    </div>
                                                    <div class="text-center">
                                                        <i class="fas fa-arrow-right"></i>
                                                        <br>
                                                        <small>
                                                            <fmt:formatNumber value="${voyage.trajet.dureeMinutes / 60}"
                                                                            maxFractionDigits="0" />h
                                                            <fmt:formatNumber value="${voyage.trajet.dureeMinutes % 60}"
                                                                            maxFractionDigits="0" />min
                                                        </small>
                                                    </div>
                                                    <div>
                                                        <strong>
                                                            <fmt:formatDate value="${voyage.dateHeureArrivee}" pattern="HH:mm" />
                                                        </strong>
                                                        <br>
                                                        <small>${voyage.trajet.gareArrivee.ville}</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <c:if test="${voyageStatus.index == 0}">
                                                <div class="col-md-2 text-center">
                                                    <i class="fas fa-exchange-alt fa-2x text-warning"></i>
                                                    <br>
                                                    <small class="text-muted">Correspondance</small>
                                                </div>
                                            </c:if>
                                        </c:forEach>
                                    </div>
                                    <div class="text-center mt-3">
                                        <button class="btn btn-outline-primary"
                                                onclick="selectCorrespondance([${correspondance[0].id}, ${correspondance[1].id}])">
                                            <i class="fas fa-ticket-alt"></i> Réserver ce voyage
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </c:if>

                <!-- Aucun résultat -->
                <c:if test="${empty voyages and empty correspondances}">
                    <div class="text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-train fa-3x text-muted mb-3"></i>
                            <h4 class="text-dark">Aucun voyage disponible</h4>
                        </div>

                        <div class="alert alert-info mx-auto" style="max-width: 600px;">
                            <h6><i class="fas fa-info-circle"></i> Désolé, aucun voyage n'est disponible</h6>
                            <p class="mb-2">
                                <strong>De :</strong> ${villeDepart} <br>
                                <strong>Vers :</strong> ${villeArrivee} <br>
                                <strong>Date :</strong> <fmt:formatDate value="${dateVoyage}" pattern="dd MMMM yyyy" /> <br>
                                <strong>Passagers :</strong> ${nombrePassagers}
                            </p>
                            <hr>

                            <!-- Message contextuel basé sur la raison -->
                            <c:choose>
                                <c:when test="${noResultsReason == 'NO_SCHEDULE'}">
                                    <p class="mb-2">
                                        <i class="fas fa-calendar-times text-warning"></i>
                                        ${noResultsMessage}
                                    </p>
                                    <p class="mb-0">
                                        Cela peut être dû à :
                                    </p>
                                    <ul class="text-start mt-2 mb-0">
                                        <li>Aucun trajet direct entre ces villes</li>
                                        <li>Service temporairement suspendu</li>
                                        <li>Maintenance ou travaux sur la ligne</li>
                                    </ul>
                                </c:when>
                                <c:when test="${noResultsReason == 'DATE_UNAVAILABLE'}">
                                    <p class="mb-2">
                                        <i class="fas fa-calendar-alt text-info"></i>
                                        ${noResultsMessage}
                                    </p>
                                    <p class="mb-0">
                                        Raisons possibles :
                                    </p>
                                    <ul class="text-start mt-2 mb-0">
                                        <li>Tous les voyages sont complets pour cette date</li>
                                        <li>Aucun voyage programmé ce jour-là</li>
                                        <li>Jour férié ou weekend sans service</li>
                                    </ul>
                                </c:when>
                                <c:otherwise>
                                    <p class="mb-2">
                                        <i class="fas fa-exclamation-triangle text-warning"></i>
                                        ${noResultsMessage != null ? noResultsMessage : 'Aucun voyage trouvé pour votre recherche.'}
                                    </p>
                                    <p class="mb-0">
                                        Cela peut être dû à :
                                    </p>
                                    <ul class="text-start mt-2 mb-0">
                                        <li>Aucun trajet direct entre ces villes</li>
                                        <li>Tous les voyages sont complets</li>
                                        <li>Aucun voyage programmé ce jour-là</li>
                                        <li>Maintenance ou travaux sur la ligne</li>
                                    </ul>
                                </c:otherwise>
                            </c:choose>
                        </div>

                        <div class="mt-4">
                            <h6>Suggestions :</h6>
                            <div class="d-flex flex-wrap justify-content-center gap-2 mt-3">
                                <a href="${pageContext.request.contextPath}/recherche/search" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Nouvelle recherche
                                </a>
                                <button class="btn btn-outline-secondary" onclick="modifyDate(-1)">
                                    <i class="fas fa-calendar-minus"></i> Jour précédent
                                </button>
                                <button class="btn btn-outline-secondary" onclick="modifyDate(1)">
                                    <i class="fas fa-calendar-plus"></i> Jour suivant
                                </button>
                                <button class="btn btn-outline-info" onclick="searchWithCorrespondance()">
                                    <i class="fas fa-exchange-alt"></i> Avec correspondance
                                </button>
                            </div>
                        </div>

                        <!-- Destinations alternatives -->
                        <c:if test="${not empty destinationsAlternatives}">
                            <div class="mt-4">
                                <div class="alert alert-light">
                                    <h6><i class="fas fa-map-marked-alt"></i> Destinations alternatives depuis ${villeDepart} :</h6>
                                    <div class="d-flex flex-wrap gap-2 mt-2">
                                        <c:forEach var="destination" items="${destinationsAlternatives}">
                                            <button class="btn btn-outline-primary btn-sm"
                                                    onclick="searchAlternativeDestination('${destination}')">
                                                <i class="fas fa-arrow-right"></i> ${destination}
                                            </button>
                                        </c:forEach>
                                    </div>
                                </div>
                            </div>
                        </c:if>

                        <div class="mt-4">
                            <small class="text-muted">
                                <i class="fas fa-lightbulb"></i>
                                Astuce : Essayez de rechercher des villes voisines ou des dates flexibles pour plus d'options.
                            </small>
                        </div>
                    </div>
                </c:if>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <jsp:include page="../common/footer.jsp" />

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/main.js"></script>

    <script>
        // Gestion des filtres
        document.getElementById('prixRange').addEventListener('input', function() {
            document.getElementById('prixValue').textContent = this.value + ' MAD';
            applyFilters();
        });

        document.getElementById('dureeRange').addEventListener('input', function() {
            document.getElementById('dureeValue').textContent = this.value + 'h';
            applyFilters();
        });

        // Appliquer les filtres
        function applyFilters() {
            const prixMax = parseInt(document.getElementById('prixRange').value);
            const dureeMax = parseInt(document.getElementById('dureeRange').value) * 60; // en minutes

            const typeFilters = [];
            if (document.getElementById('typeExpress').checked) typeFilters.push('EXPRESS');
            if (document.getElementById('typeDirect').checked) typeFilters.push('DIRECT');
            if (document.getElementById('typeNormal').checked) typeFilters.push('NORMAL');

            const voyageCards = document.querySelectorAll('.voyage-card');

            voyageCards.forEach(card => {
                const prix = parseInt(card.dataset.prix);
                const duree = parseInt(card.dataset.duree);
                const type = card.dataset.type;

                const visible = prix <= prixMax &&
                               duree <= dureeMax &&
                               typeFilters.includes(type);

                card.style.display = visible ? 'block' : 'none';
            });
        }

        // Réinitialiser les filtres
        function resetFilters() {
            document.getElementById('prixRange').value = 1000;
            document.getElementById('dureeRange').value = 12;
            document.getElementById('prixValue').textContent = '1000 MAD';
            document.getElementById('dureeValue').textContent = '12h';

            document.querySelectorAll('#filterForm input[type="checkbox"]').forEach(cb => {
                cb.checked = true;
            });

            applyFilters();
        }

        // Trier les résultats
        function sortResults(criteria) {
            const container = document.getElementById('voyagesList');
            const cards = Array.from(container.querySelectorAll('.voyage-card'));

            cards.sort((a, b) => {
                switch(criteria) {
                    case 'prix':
                        return parseInt(a.dataset.prix) - parseInt(b.dataset.prix);
                    case 'duree':
                        return parseInt(a.dataset.duree) - parseInt(b.dataset.duree);
                    case 'heure':
                        // Tri par heure de départ (nécessiterait un attribut data supplémentaire)
                        return 0;
                    default:
                        return 0;
                }
            });

            // Réorganiser les éléments
            cards.forEach(card => container.appendChild(card));
        }

        // Sélectionner un voyage
        function selectVoyage(voyageId) {
            <c:choose>
                <c:when test="${isAuthenticated}">
                    window.location.href = '${pageContext.request.contextPath}/selection/voyage?id=' + voyageId;
                </c:when>
                <c:otherwise>
                    // Sauvegarder l'URL de redirection et rediriger vers la connexion
                    sessionStorage.setItem('redirectAfterLogin', '${pageContext.request.contextPath}/selection/voyage?id=' + voyageId);
                    window.location.href = '${pageContext.request.contextPath}/auth/login';
                </c:otherwise>
            </c:choose>
        }

        // Afficher les détails d'un voyage
        function showVoyageDetails(voyageId) {
            window.location.href = '${pageContext.request.contextPath}/recherche/voyage-details?id=' + voyageId;
        }

        // Sélectionner un voyage avec correspondance
        function selectCorrespondance(voyageIds) {
            // Logique pour gérer les voyages avec correspondance
            alert('Fonctionnalité de correspondance en cours de développement');
        }

        // Modifier la date de recherche
        function modifyDate(days) {
            const currentDate = new Date('${dateVoyage}');
            currentDate.setDate(currentDate.getDate() + days);

            const year = currentDate.getFullYear();
            const month = String(currentDate.getMonth() + 1).padStart(2, '0');
            const day = String(currentDate.getDate()).padStart(2, '0');
            const newDate = `${year}-${month}-${day}`;

            // Construire l'URL avec les nouveaux paramètres
            const url = new URL('${pageContext.request.contextPath}/recherche/search', window.location.origin);
            url.searchParams.set('villeDepart', '${villeDepart}');
            url.searchParams.set('villeArrivee', '${villeArrivee}');
            url.searchParams.set('dateVoyage', newDate);
            url.searchParams.set('nombrePassagers', '${nombrePassagers}');
            url.searchParams.set('action', 'search');

            window.location.href = url.toString();
        }

        // Rechercher avec correspondance
        function searchWithCorrespondance() {
            const url = new URL('${pageContext.request.contextPath}/recherche/search', window.location.origin);
            url.searchParams.set('villeDepart', '${villeDepart}');
            url.searchParams.set('villeArrivee', '${villeArrivee}');
            url.searchParams.set('dateVoyage', '${dateVoyage}');
            url.searchParams.set('nombrePassagers', '${nombrePassagers}');
            url.searchParams.set('voyagesDirects', 'false');
            url.searchParams.set('action', 'search');

            window.location.href = url.toString();
        }

        // Rechercher une destination alternative
        function searchAlternativeDestination(destination) {
            const url = new URL('${pageContext.request.contextPath}/recherche/search', window.location.origin);
            url.searchParams.set('villeDepart', '${villeDepart}');
            url.searchParams.set('villeArrivee', destination);
            url.searchParams.set('dateVoyage', '${dateVoyage}');
            url.searchParams.set('nombrePassagers', '${nombrePassagers}');
            url.searchParams.set('action', 'search');

            window.location.href = url.toString();
        }

        // Initialiser les filtres
        applyFilters();
    </script>
</body>
</html>
