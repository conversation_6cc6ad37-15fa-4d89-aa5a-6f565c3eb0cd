/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-05-29 12:23:58 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.views.auth;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;

public final class login_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write('\n');
      //  c:if
      org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent(null);
      // /WEB-INF/views/auth/login.jsp(6,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${empty messageAccueil}", java.lang.Boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\n");
          out.write("    ");

        try {
            // Créer et utiliser le contrôleur pour charger les données
            com.trainticket.controller.AuthController authController =
                new com.trainticket.controller.AuthController();
            authController.loadLoginData(request);
        } catch (Exception e) {
            // En cas d'erreur, définir des valeurs par défaut
            request.setAttribute("messageAccueil", "Bienvenue sur Train Ticket");
            request.setAttribute("nombreVillesDesservies", 15);
            request.setAttribute("nombreTrajetsDisponibles", 50);
            request.setAttribute("nombreUtilisateursInscrits", 1000);
            // System.err.println("Erreur lors du chargement des données de connexion: " + e.getMessage());
        }
    
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
        return;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"fr\">\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
      out.write("    <title>Connexion - Train Ticket</title>\n");
      out.write("    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css\" rel=\"stylesheet\">\n");
      out.write("    <link href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\" rel=\"stylesheet\">\n");
      out.write("    <link href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/resources/css/style.css\" rel=\"stylesheet\">\n");
      out.write("    <style>\n");
      out.write("        .login-container {\n");
      out.write("            min-height: 100vh;\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("            display: flex;\n");
      out.write("            align-items: center;\n");
      out.write("            justify-content: center;\n");
      out.write("        }\n");
      out.write("        .login-card {\n");
      out.write("            background: white;\n");
      out.write("            border-radius: 20px;\n");
      out.write("            box-shadow: 0 20px 40px rgba(0,0,0,0.1);\n");
      out.write("            overflow: hidden;\n");
      out.write("            max-width: 400px;\n");
      out.write("            width: 100%;\n");
      out.write("        }\n");
      out.write("        .login-header {\n");
      out.write("            background: linear-gradient(45deg, #007bff, #0056b3);\n");
      out.write("            color: white;\n");
      out.write("            padding: 2rem;\n");
      out.write("            text-align: center;\n");
      out.write("        }\n");
      out.write("        .login-body {\n");
      out.write("            padding: 2rem;\n");
      out.write("        }\n");
      out.write("        .form-floating {\n");
      out.write("            margin-bottom: 1rem;\n");
      out.write("        }\n");
      out.write("        .btn-login {\n");
      out.write("            background: linear-gradient(45deg, #007bff, #0056b3);\n");
      out.write("            border: none;\n");
      out.write("            border-radius: 10px;\n");
      out.write("            padding: 12px;\n");
      out.write("            font-weight: 600;\n");
      out.write("            transition: all 0.3s ease;\n");
      out.write("        }\n");
      out.write("        .btn-login:hover {\n");
      out.write("            transform: translateY(-2px);\n");
      out.write("            box-shadow: 0 5px 15px rgba(0,123,255,0.3);\n");
      out.write("        }\n");
      out.write("        .stat-mini {\n");
      out.write("            padding: 0.5rem;\n");
      out.write("            border-radius: 8px;\n");
      out.write("            background: rgba(255,255,255,0.1);\n");
      out.write("            margin: 0.2rem;\n");
      out.write("        }\n");
      out.write("        .stat-mini h6 {\n");
      out.write("            font-size: 1.2rem;\n");
      out.write("            font-weight: bold;\n");
      out.write("            margin-bottom: 0;\n");
      out.write("        }\n");
      out.write("        .stat-mini small {\n");
      out.write("            font-size: 0.75rem;\n");
      out.write("            opacity: 0.9;\n");
      out.write("        }\n");
      out.write("    </style>\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <div class=\"login-container\">\n");
      out.write("        <div class=\"login-card\">\n");
      out.write("            <div class=\"login-header\">\n");
      out.write("                <h2><i class=\"fas fa-train\"></i> Train Ticket</h2>\n");
      out.write("                <p class=\"mb-2\">");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${messageAccueil}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("</p>\n");
      out.write("                <div class=\"row text-center mt-3\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"stat-mini\">\n");
      out.write("                            <h6 class=\"mb-0\">");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${nombreVillesDesservies}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("</h6>\n");
      out.write("                            <small>Villes</small>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"stat-mini\">\n");
      out.write("                            <h6 class=\"mb-0\">");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${nombreTrajetsDisponibles}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("</h6>\n");
      out.write("                            <small>Trajets</small>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"stat-mini\">\n");
      out.write("                            <h6 class=\"mb-0\">");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${nombreUtilisateursInscrits}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("+</h6>\n");
      out.write("                            <small>Utilisateurs</small>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <div class=\"login-body\">\n");
      out.write("                <!-- Messages d'erreur -->\n");
      out.write("                ");
      if (_jspx_meth_c_005fif_005f1(_jspx_page_context))
        return;
      out.write("\n");
      out.write("\n");
      out.write("                <!-- Messages de succès -->\n");
      out.write("                ");
      if (_jspx_meth_c_005fif_005f2(_jspx_page_context))
        return;
      out.write("\n");
      out.write("\n");
      out.write("                ");
      if (_jspx_meth_c_005fif_005f3(_jspx_page_context))
        return;
      out.write("\n");
      out.write("\n");
      out.write("                <form action=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/auth/login\" method=\"post\" id=\"loginForm\">\n");
      out.write("                    <div class=\"form-floating\">\n");
      out.write("                        <input type=\"email\" class=\"form-control\" id=\"email\" name=\"email\"\n");
      out.write("                               placeholder=\"<EMAIL>\" value=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${email}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("\" required>\n");
      out.write("                        <label for=\"email\"><i class=\"fas fa-envelope\"></i> Adresse email</label>\n");
      out.write("                    </div>\n");
      out.write("\n");
      out.write("                    <div class=\"form-floating\">\n");
      out.write("                        <input type=\"password\" class=\"form-control\" id=\"motDePasse\" name=\"motDePasse\"\n");
      out.write("                               placeholder=\"Mot de passe\" required>\n");
      out.write("                        <label for=\"motDePasse\"><i class=\"fas fa-lock\"></i> Mot de passe</label>\n");
      out.write("                    </div>\n");
      out.write("\n");
      out.write("                    <div class=\"form-check mb-3\">\n");
      out.write("                        <input class=\"form-check-input\" type=\"checkbox\" id=\"rememberMe\" name=\"rememberMe\">\n");
      out.write("                        <label class=\"form-check-label\" for=\"rememberMe\">\n");
      out.write("                            Se souvenir de moi\n");
      out.write("                        </label>\n");
      out.write("                    </div>\n");
      out.write("\n");
      out.write("                    <!-- Champ caché pour l'URL de redirection -->\n");
      out.write("                    ");
      if (_jspx_meth_c_005fif_005f4(_jspx_page_context))
        return;
      out.write("\n");
      out.write("\n");
      out.write("                    <button type=\"submit\" class=\"btn btn-primary btn-login w-100 mb-3\">\n");
      out.write("                        <i class=\"fas fa-sign-in-alt\"></i> Se connecter\n");
      out.write("                    </button>\n");
      out.write("                </form>\n");
      out.write("\n");
      out.write("                <div class=\"text-center\">\n");
      out.write("                    <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/auth/forgot-password\" class=\"text-decoration-none\">\n");
      out.write("                        <i class=\"fas fa-question-circle\"></i> Mot de passe oublié ?\n");
      out.write("                    </a>\n");
      out.write("                </div>\n");
      out.write("\n");
      out.write("                <hr class=\"my-4\">\n");
      out.write("\n");
      out.write("                <div class=\"text-center\">\n");
      out.write("                    <p class=\"mb-2\">Pas encore de compte ?</p>\n");
      out.write("                    <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/auth/register\"\n");
      out.write("                       class=\"btn btn-outline-primary w-100\">\n");
      out.write("                        <i class=\"fas fa-user-plus\"></i> Créer un compte\n");
      out.write("                    </a>\n");
      out.write("                </div>\n");
      out.write("\n");
      out.write("                <div class=\"text-center mt-3\">\n");
      out.write("                    <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/\" class=\"text-muted text-decoration-none\">\n");
      out.write("                        <i class=\"fas fa-arrow-left\"></i> Retour à l'accueil\n");
      out.write("                    </a>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("\n");
      out.write("    <!-- Scripts -->\n");
      out.write("    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js\"></script>\n");
      out.write("    <script>\n");
      out.write("        document.getElementById('loginForm').addEventListener('submit', function(e) {\n");
      out.write("            const email = document.getElementById('email').value;\n");
      out.write("            const password = document.getElementById('motDePasse').value;\n");
      out.write("\n");
      out.write("            if (!email || !password) {\n");
      out.write("                e.preventDefault();\n");
      out.write("                alert('Veuillez remplir tous les champs');\n");
      out.write("                return;\n");
      out.write("            }\n");
      out.write("\n");
      out.write("            // Validation email\n");
      out.write("            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n");
      out.write("            if (!emailRegex.test(email)) {\n");
      out.write("                e.preventDefault();\n");
      out.write("                alert('Veuillez entrer une adresse email valide');\n");
      out.write("                return;\n");
      out.write("            }\n");
      out.write("\n");
      out.write("            // Afficher un indicateur de chargement\n");
      out.write("            const submitBtn = this.querySelector('button[type=\"submit\"]');\n");
      out.write("            const originalText = submitBtn.innerHTML;\n");
      out.write("            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin\"></i> Connexion...';\n");
      out.write("            submitBtn.disabled = true;\n");
      out.write("\n");
      out.write("            // En cas d'erreur, restaurer le bouton (sera géré par le rechargement de page)\n");
      out.write("            setTimeout(() => {\n");
      out.write("                submitBtn.innerHTML = originalText;\n");
      out.write("                submitBtn.disabled = false;\n");
      out.write("            }, 5000);\n");
      out.write("        });\n");
      out.write("\n");
      out.write("        // Auto-focus sur le champ email\n");
      out.write("        document.getElementById('email').focus();\n");
      out.write("\n");
      out.write("        // Gestion de la touche Entrée\n");
      out.write("        document.addEventListener('keypress', function(e) {\n");
      out.write("            if (e.key === 'Enter') {\n");
      out.write("                document.getElementById('loginForm').submit();\n");
      out.write("            }\n");
      out.write("        });\n");
      out.write("    </script>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_c_005fif_005f1(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
    _jspx_th_c_005fif_005f1.setParent(null);
    // /WEB-INF/views/auth/login.jsp(120,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
    _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty errorMessage}", java.lang.Boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false)).booleanValue());
    int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
    if (_jspx_eval_c_005fif_005f1 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
      do {
        out.write("\n");
        out.write("                    <div class=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">\n");
        out.write("                        <i class=\"fas fa-exclamation-triangle\"></i> ");
        out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${errorMessage}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
        out.write("\n");
        out.write("                        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>\n");
        out.write("                    </div>\n");
        out.write("                ");
        int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
        if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
          break;
      } while (true);
    }
    if (_jspx_th_c_005fif_005f1.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      return true;
    }
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
    _jspx_th_c_005fif_005f2.setParent(null);
    // /WEB-INF/views/auth/login.jsp(128,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
    _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.message == 'logout'}", java.lang.Boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false)).booleanValue());
    int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
    if (_jspx_eval_c_005fif_005f2 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
      do {
        out.write("\n");
        out.write("                    <div class=\"alert alert-success alert-dismissible fade show\" role=\"alert\">\n");
        out.write("                        <i class=\"fas fa-check-circle\"></i> Vous avez été déconnecté avec succès\n");
        out.write("                        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>\n");
        out.write("                    </div>\n");
        out.write("                ");
        int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
        if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
          break;
      } while (true);
    }
    if (_jspx_th_c_005fif_005f2.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      return true;
    }
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f3(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f3 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    _jspx_th_c_005fif_005f3.setPageContext(_jspx_page_context);
    _jspx_th_c_005fif_005f3.setParent(null);
    // /WEB-INF/views/auth/login.jsp(135,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
    _jspx_th_c_005fif_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.message == 'registered'}", java.lang.Boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false)).booleanValue());
    int _jspx_eval_c_005fif_005f3 = _jspx_th_c_005fif_005f3.doStartTag();
    if (_jspx_eval_c_005fif_005f3 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
      do {
        out.write("\n");
        out.write("                    <div class=\"alert alert-success alert-dismissible fade show\" role=\"alert\">\n");
        out.write("                        <i class=\"fas fa-check-circle\"></i> Inscription réussie ! Vous pouvez maintenant vous connecter\n");
        out.write("                        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>\n");
        out.write("                    </div>\n");
        out.write("                ");
        int evalDoAfterBody = _jspx_th_c_005fif_005f3.doAfterBody();
        if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
          break;
      } while (true);
    }
    if (_jspx_th_c_005fif_005f3.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f3);
      return true;
    }
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f3);
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f4(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f4 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    _jspx_th_c_005fif_005f4.setPageContext(_jspx_page_context);
    _jspx_th_c_005fif_005f4.setParent(null);
    // /WEB-INF/views/auth/login.jsp(163,20) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
    _jspx_th_c_005fif_005f4.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty param.redirectUrl}", java.lang.Boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false)).booleanValue());
    int _jspx_eval_c_005fif_005f4 = _jspx_th_c_005fif_005f4.doStartTag();
    if (_jspx_eval_c_005fif_005f4 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
      do {
        out.write("\n");
        out.write("                        <input type=\"hidden\" name=\"redirectUrl\" value=\"");
        out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.redirectUrl}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
        out.write("\">\n");
        out.write("                    ");
        int evalDoAfterBody = _jspx_th_c_005fif_005f4.doAfterBody();
        if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
          break;
      } while (true);
    }
    if (_jspx_th_c_005fif_005f4.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f4);
      return true;
    }
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f4);
    return false;
  }
}
