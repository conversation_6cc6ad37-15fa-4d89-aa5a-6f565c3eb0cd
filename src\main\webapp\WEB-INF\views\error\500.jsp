<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Erreur serveur - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="${pageContext.request.contextPath}/resources/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
        <div class="row justify-content-center w-100">
            <div class="col-md-6 col-lg-4">
                <div class="card shadow-lg border-0">
                    <div class="card-body text-center p-5">
                        <div class="mb-4">
                            <i class="fas fa-exclamation-circle text-danger" style="font-size: 4rem;"></i>
                        </div>
                        <h1 class="display-4 text-danger mb-3">500</h1>
                        <h2 class="h4 mb-3">Erreur interne du serveur</h2>
                        <p class="text-muted mb-4">
                            Une erreur inattendue s'est produite. Nos équipes techniques ont été notifiées et travaillent à résoudre le problème.
                        </p>
                        <div class="d-grid gap-2">
                            <a href="${pageContext.request.contextPath}/" class="btn btn-primary">
                                <i class="fas fa-home"></i> Retour à l'accueil
                            </a>
                            <button onclick="history.back()" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Page précédente
                            </button>
                        </div>
                        
                        <!-- Affichage des détails de l'erreur en mode développement -->
                        <c:if test="${not empty exception}">
                            <div class="mt-4">
                                <details class="text-start">
                                    <summary class="btn btn-sm btn-outline-danger">Détails techniques</summary>
                                    <div class="mt-2 p-3 bg-light border rounded">
                                        <small class="text-danger">
                                            <strong>Exception:</strong> ${exception.class.simpleName}<br>
                                            <strong>Message:</strong> ${exception.message}
                                        </small>
                                    </div>
                                </details>
                            </div>
                        </c:if>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
