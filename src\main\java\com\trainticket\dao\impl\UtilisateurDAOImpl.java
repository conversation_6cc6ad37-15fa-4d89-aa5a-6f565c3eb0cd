package com.trainticket.dao.impl;

import com.trainticket.dao.UtilisateurDAO;
import com.trainticket.model.Utilisateur;
import com.trainticket.util.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.query.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public class UtilisateurDAOImpl extends BaseDAOImpl<Utilisateur, Long> implements UtilisateurDAO {

    @Override
    public Optional<Utilisateur> findByEmail(String email) {
        String hql = "FROM Utilisateur u WHERE u.email = ?0";
        return executeSingleResultQuery(hql, email);
    }

    @Override
    public boolean existsByEmail(String email) {
        String hql = "SELECT COUNT(u) FROM Utilisateur u WHERE u.email = ?0";
        return executeCountQuery(hql, email) > 0;
    }

    @Override
    public List<Utilisateur> findByRole(Utilisateur.Role role) {
        String hql = "FROM Utilisateur u WHERE u.role = ?0 ORDER BY u.nom, u.prenom";
        return executeQuery(hql, role);
    }

    @Override
    public List<Utilisateur> findByStatut(Utilisateur.StatutCompte statut) {
        String hql = "FROM Utilisateur u WHERE u.statut = ?0 ORDER BY u.dateCreation DESC";
        return executeQuery(hql, statut);
    }

    @Override
    public List<Utilisateur> searchByName(String searchTerm) {
        String hql = "FROM Utilisateur u WHERE " +
                    "LOWER(u.nom) LIKE LOWER(?0) OR " +
                    "LOWER(u.prenom) LIKE LOWER(?0) OR " +
                    "LOWER(CONCAT(u.prenom, ' ', u.nom)) LIKE LOWER(?0) " +
                    "ORDER BY u.nom, u.prenom";
        String searchPattern = "%" + searchTerm + "%";
        return executeQuery(hql, searchPattern);
    }

    @Override
    public List<Utilisateur> findActiveUsers() {
        return findByStatut(Utilisateur.StatutCompte.ACTIF);
    }

    @Override
    public void updateLastLogin(Long userId) {
        String hql = "UPDATE Utilisateur u SET u.derniereConnexion = ?0 WHERE u.id = ?1";
        executeUpdateQuery(hql, LocalDateTime.now(), userId);
    }

    @Override
    public long countByRole(Utilisateur.Role role) {
        String hql = "SELECT COUNT(u) FROM Utilisateur u WHERE u.role = ?0";
        return executeCountQuery(hql, role);
    }

    @Override
    public List<Utilisateur> findWithPagination(int page, int size) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            String hql = "FROM Utilisateur u ORDER BY u.dateCreation DESC";
            Query<Utilisateur> query = session.createQuery(hql, Utilisateur.class);
            query.setFirstResult(page * size);
            query.setMaxResults(size);
            return query.getResultList();
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération paginée des utilisateurs", e);
            throw new RuntimeException("Erreur lors de la récupération", e);
        }
    }

    /**
     * Trouver les utilisateurs récemment inscrits
     * @param days nombre de jours
     * @return liste des utilisateurs inscrits dans les derniers jours
     */
    public List<Utilisateur> findRecentUsers(int days) {
        String hql = "FROM Utilisateur u WHERE u.dateCreation >= ?0 ORDER BY u.dateCreation DESC";
        LocalDateTime dateLimit = LocalDateTime.now().minusDays(days);
        return executeQuery(hql, dateLimit);
    }

    /**
     * Trouver les utilisateurs par ville
     * @param ville la ville
     * @return liste des utilisateurs de cette ville
     */
    public List<Utilisateur> findByVille(String ville) {
        String hql = "FROM Utilisateur u WHERE LOWER(u.adresse) LIKE LOWER(?0)";
        String searchPattern = "%" + ville + "%";
        return executeQuery(hql, searchPattern);
    }

    /**
     * Compter les utilisateurs actifs
     * @return nombre d'utilisateurs actifs
     */
    public long countActiveUsers() {
        return countByStatut(Utilisateur.StatutCompte.ACTIF);
    }

    /**
     * Compter les utilisateurs par statut
     * @param statut le statut
     * @return nombre d'utilisateurs avec ce statut
     */
    public long countByStatut(Utilisateur.StatutCompte statut) {
        String hql = "SELECT COUNT(u) FROM Utilisateur u WHERE u.statut = ?0";
        return executeCountQuery(hql, statut);
    }

    /**
     * Trouver les utilisateurs sans connexion récente
     * @param days nombre de jours
     * @return liste des utilisateurs inactifs
     */
    public List<Utilisateur> findInactiveUsers(int days) {
        String hql = "FROM Utilisateur u WHERE u.derniereConnexion < ?0 OR u.derniereConnexion IS NULL";
        LocalDateTime dateLimit = LocalDateTime.now().minusDays(days);
        return executeQuery(hql, dateLimit);
    }

    /**
     * Mettre à jour le statut d'un utilisateur
     * @param userId l'ID de l'utilisateur
     * @param nouveauStatut le nouveau statut
     */
    public void updateUserStatus(Long userId, Utilisateur.StatutCompte nouveauStatut) {
        String hql = "UPDATE Utilisateur u SET u.statut = ?0 WHERE u.id = ?1";
        executeUpdateQuery(hql, nouveauStatut, userId);
    }

    /**
     * Recherche avancée d'utilisateurs
     * @param email email (peut être partiel)
     * @param nom nom (peut être partiel)
     * @param role rôle
     * @param statut statut
     * @return liste des utilisateurs correspondants
     */
    public List<Utilisateur> advancedSearch(String email, String nom,
                                          Utilisateur.Role role, Utilisateur.StatutCompte statut) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            StringBuilder hql = new StringBuilder("FROM Utilisateur u WHERE 1=1");

            if (email != null && !email.trim().isEmpty()) {
                hql.append(" AND LOWER(u.email) LIKE LOWER(:email)");
            }

            if (nom != null && !nom.trim().isEmpty()) {
                hql.append(" AND (LOWER(u.nom) LIKE LOWER(:nom) OR LOWER(u.prenom) LIKE LOWER(:nom))");
            }

            if (role != null) {
                hql.append(" AND u.role = :role");
            }

            if (statut != null) {
                hql.append(" AND u.statut = :statut");
            }

            hql.append(" ORDER BY u.nom, u.prenom");

            Query<Utilisateur> query = session.createQuery(hql.toString(), Utilisateur.class);

            if (email != null && !email.trim().isEmpty()) {
                query.setParameter("email", "%" + email + "%");
            }

            if (nom != null && !nom.trim().isEmpty()) {
                query.setParameter("nom", "%" + nom + "%");
            }

            if (role != null) {
                query.setParameter("role", role);
            }

            if (statut != null) {
                query.setParameter("statut", statut);
            }

            return query.getResultList();
        } catch (Exception e) {
            logger.error("Erreur lors de la recherche avancée d'utilisateurs", e);
            throw new RuntimeException("Erreur lors de la recherche", e);
        }
    }
}
