package com.trainticket.util;

import org.hibernate.SessionFactory;
import org.hibernate.boot.MetadataSources;
import org.hibernate.boot.registry.StandardServiceRegistry;
import org.hibernate.boot.registry.StandardServiceRegistryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

// Import des entités
import com.trainticket.model.Utilisateur;
import com.trainticket.model.Gare;
import com.trainticket.model.Trajet;
import com.trainticket.model.Voyage;
import com.trainticket.model.Billet;

public class HibernateUtil {

    private static final Logger logger = LoggerFactory.getLogger(HibernateUtil.class);
    private static SessionFactory sessionFactory;

    static {
        try {
            // Test de connexion simple d'abord
            testBasicConnection();

            // Configuration avec la nouvelle API Hibernate
            StandardServiceRegistry registry = new StandardServiceRegistryBuilder()
                    .applySetting("hibernate.connection.driver_class", "com.mysql.cj.jdbc.Driver")
                    .applySetting("hibernate.connection.url", "********************************************************************************************************")
                    .applySetting("hibernate.connection.username", "root")
                    .applySetting("hibernate.connection.password", "123")
                    .applySetting("hibernate.dialect", "org.hibernate.dialect.MySQL8Dialect")
                    .applySetting("hibernate.show_sql", "true")
                    .applySetting("hibernate.format_sql", "true")
                    .applySetting("hibernate.hbm2ddl.auto", "update")
                    .applySetting("hibernate.current_session_context_class", "thread")
                    .applySetting("hibernate.cache.use_second_level_cache", "false")
                    // Configuration de pool simple
                    .applySetting("hibernate.connection.pool_size", "10")
                    .build();

            // Créer la SessionFactory avec les entités
            sessionFactory = new MetadataSources(registry)
                    .addAnnotatedClass(Utilisateur.class)
                    .addAnnotatedClass(Gare.class)
                    .addAnnotatedClass(Trajet.class)
                    .addAnnotatedClass(Voyage.class)
                    .addAnnotatedClass(Billet.class)
                    .buildMetadata()
                    .buildSessionFactory();

            logger.info("Hibernate SessionFactory créée avec succès");

        } catch (Exception ex) {
            logger.error("Erreur lors de la création de la SessionFactory", ex);
            System.err.println("ERREUR HIBERNATE: " + ex.getMessage());
            if (ex.getCause() != null) {
                System.err.println("CAUSE: " + ex.getCause().getMessage());
            }
            ex.printStackTrace();
            throw new ExceptionInInitializerError(ex);
        }
    }

    /**
     * Test de connexion basique avant d'initialiser Hibernate
     */
    private static void testBasicConnection() throws Exception {
        String url = "********************************************************************************************************";
        String username = "root";
        String password = "123";

        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            java.sql.Connection conn = java.sql.DriverManager.getConnection(url, username, password);
            System.out.println("✅ Test de connexion basique réussi");
            conn.close();
        } catch (Exception e) {
            System.err.println("❌ Test de connexion basique échoué: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Retourne la SessionFactory
     * @return SessionFactory
     */
    public static SessionFactory getSessionFactory() {
        return sessionFactory;
    }

    /**
     * Ferme la SessionFactory
     */
    public static void shutdown() {
        if (sessionFactory != null) {
            try {
                sessionFactory.close();
                logger.info("Hibernate SessionFactory fermée");
            } catch (Exception e) {
                logger.error("Erreur lors de la fermeture de la SessionFactory", e);
            }
        }
    }

    /**
     * Vérifie si la SessionFactory est ouverte
     * @return true si ouverte, false sinon
     */
    public static boolean isSessionFactoryOpen() {
        return sessionFactory != null && !sessionFactory.isClosed();
    }
}
