package com.trainticket.controller;

import com.trainticket.model.Utilisateur;
import com.trainticket.service.AuthService;
import com.trainticket.service.impl.AuthServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

public class AuthServlet extends HttpServlet {

    private static final Logger logger = LoggerFactory.getLogger(AuthServlet.class);
    private AuthService authService;

    @Override
    public void init() throws ServletException {
        super.init();
        this.authService = new AuthServiceImpl();
        logger.info("AuthServlet initialisé");
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String pathInfo = request.getPathInfo();

        if (pathInfo == null) {
            response.sendRedirect(request.getContextPath() + "/");
            return;
        }

        switch (pathInfo) {
            case "/login":
                showLoginPage(request, response);
                break;
            case "/register":
                showRegisterPage(request, response);
                break;
            case "/logout":
                handleLogout(request, response);
                break;
            case "/forgot-password":
                showForgotPasswordPage(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String pathInfo = request.getPathInfo();

        if (pathInfo == null) {
            response.sendRedirect(request.getContextPath() + "/");
            return;
        }

        switch (pathInfo) {
            case "/login":
                handleLogin(request, response);
                break;
            case "/register":
                handleRegister(request, response);
                break;
            case "/reset-password":
                handlePasswordReset(request, response);
                break;
            case "/change-password":
                handlePasswordChange(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    private void showLoginPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Si l'utilisateur est déjà connecté, rediriger vers l'accueil
        HttpSession session = request.getSession(false);
        if (session != null && session.getAttribute("utilisateur") != null) {
            response.sendRedirect(request.getContextPath() + "/");
            return;
        }

        request.getRequestDispatcher("/WEB-INF/views/auth/login.jsp").forward(request, response);
    }

    private void showRegisterPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Si l'utilisateur est déjà connecté, rediriger vers l'accueil
        HttpSession session = request.getSession(false);
        if (session != null && session.getAttribute("utilisateur") != null) {
            response.sendRedirect(request.getContextPath() + "/");
            return;
        }

        request.getRequestDispatcher("/WEB-INF/views/auth/register.jsp").forward(request, response);
    }

    private void showForgotPasswordPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.getRequestDispatcher("/WEB-INF/views/auth/forgot-password.jsp").forward(request, response);
    }

    private void handleLogin(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String email = request.getParameter("email");
        String motDePasse = request.getParameter("motDePasse");
        String redirectUrl = request.getParameter("redirectUrl");

        try {
            // Authentifier l'utilisateur
            Utilisateur utilisateur = authService.authenticate(email, motDePasse);

            // Créer une session
            HttpSession session = request.getSession(true);
            session.setAttribute("utilisateur", utilisateur);
            session.setAttribute("userId", utilisateur.getId());
            session.setAttribute("userRole", utilisateur.getRole().toString());

            // Message de succès
            session.setAttribute("successMessage", "Connexion réussie ! Bienvenue " + utilisateur.getPrenom());

            logger.info("Utilisateur connecté: {} (ID: {})", utilisateur.getEmail(), utilisateur.getId());

            // Redirection
            if (redirectUrl != null && !redirectUrl.trim().isEmpty()) {
                response.sendRedirect(redirectUrl);
            } else if (utilisateur.getRole() == Utilisateur.Role.ADMIN) {
                response.sendRedirect(request.getContextPath() + "/admin/dashboard");
            } else {
                response.sendRedirect(request.getContextPath() + "/");
            }

        } catch (AuthService.AuthenticationException e) {
            logger.warn("Échec de connexion pour l'email: {}", email);
            request.setAttribute("errorMessage", e.getMessage());
            request.setAttribute("email", email);
            request.getRequestDispatcher("/WEB-INF/views/auth/login.jsp").forward(request, response);
        }
    }

    private void handleRegister(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String email = request.getParameter("email");
        String motDePasse = request.getParameter("motDePasse");
        String confirmMotDePasse = request.getParameter("confirmMotDePasse");
        String nom = request.getParameter("nom");
        String prenom = request.getParameter("prenom");
        String telephone = request.getParameter("telephone");
        String adresse = request.getParameter("adresse");

        try {
            // Vérifier que les mots de passe correspondent
            if (!motDePasse.equals(confirmMotDePasse)) {
                throw new AuthService.RegistrationException("Les mots de passe ne correspondent pas");
            }

            // Créer l'utilisateur
            Utilisateur utilisateur = new Utilisateur();
            utilisateur.setEmail(email);
            utilisateur.setNom(nom);
            utilisateur.setPrenom(prenom);
            utilisateur.setTelephone(telephone);
            utilisateur.setAdresse(adresse);
            utilisateur.setRole(Utilisateur.Role.USER);

            // Enregistrer l'utilisateur
            Utilisateur nouvelUtilisateur = authService.register(utilisateur, motDePasse);

            logger.info("Nouvel utilisateur inscrit: {} (ID: {})",
                       nouvelUtilisateur.getEmail(), nouvelUtilisateur.getId());

            // Connecter automatiquement l'utilisateur
            HttpSession session = request.getSession(true);
            session.setAttribute("utilisateur", nouvelUtilisateur);
            session.setAttribute("userId", nouvelUtilisateur.getId());
            session.setAttribute("userRole", nouvelUtilisateur.getRole().toString());
            session.setAttribute("successMessage",
                "Inscription réussie ! Bienvenue " + nouvelUtilisateur.getPrenom());

            response.sendRedirect(request.getContextPath() + "/");

        } catch (AuthService.RegistrationException e) {
            logger.warn("Échec d'inscription pour l'email: {}", email);
            request.setAttribute("errorMessage", e.getMessage());

            // Conserver les données saisies (sauf les mots de passe)
            request.setAttribute("email", email);
            request.setAttribute("nom", nom);
            request.setAttribute("prenom", prenom);
            request.setAttribute("telephone", telephone);
            request.setAttribute("adresse", adresse);

            request.getRequestDispatcher("/WEB-INF/views/auth/register.jsp").forward(request, response);
        }
    }

    private void handleLogout(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);
        if (session != null) {
            Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");
            if (utilisateur != null) {
                logger.info("Utilisateur déconnecté: {} (ID: {})",
                           utilisateur.getEmail(), utilisateur.getId());
            }
            session.invalidate();
        }

        response.sendRedirect(request.getContextPath() + "/?message=logout");
    }

    private void handlePasswordReset(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String email = request.getParameter("email");

        try {
            String nouveauMotDePasse = authService.resetPassword(email);

            logger.info("Mot de passe réinitialisé pour: {}", email);

            request.setAttribute("successMessage",
                "Un nouveau mot de passe a été envoyé à votre adresse email. " +
                "Mot de passe temporaire: " + nouveauMotDePasse);
            request.getRequestDispatcher("/WEB-INF/views/auth/forgot-password.jsp").forward(request, response);

        } catch (AuthService.PasswordResetException e) {
            logger.warn("Échec de réinitialisation pour l'email: {}", email);
            request.setAttribute("errorMessage", e.getMessage());
            request.setAttribute("email", email);
            request.getRequestDispatcher("/WEB-INF/views/auth/forgot-password.jsp").forward(request, response);
        }
    }

    private void handlePasswordChange(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("utilisateur") == null) {
            response.sendRedirect(request.getContextPath() + "/auth/login");
            return;
        }

        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");
        String ancienMotDePasse = request.getParameter("ancienMotDePasse");
        String nouveauMotDePasse = request.getParameter("nouveauMotDePasse");
        String confirmMotDePasse = request.getParameter("confirmMotDePasse");

        try {
            // Vérifier que les nouveaux mots de passe correspondent
            if (!nouveauMotDePasse.equals(confirmMotDePasse)) {
                throw new AuthService.PasswordChangeException("Les nouveaux mots de passe ne correspondent pas");
            }

            authService.changePassword(utilisateur.getId(), ancienMotDePasse, nouveauMotDePasse);

            logger.info("Mot de passe changé pour l'utilisateur: {} (ID: {})",
                       utilisateur.getEmail(), utilisateur.getId());

            session.setAttribute("successMessage", "Mot de passe modifié avec succès");
            response.sendRedirect(request.getContextPath() + "/user/profile");

        } catch (AuthService.PasswordChangeException e) {
            logger.warn("Échec de changement de mot de passe pour l'utilisateur: {} (ID: {})",
                       utilisateur.getEmail(), utilisateur.getId());

            session.setAttribute("errorMessage", e.getMessage());
            response.sendRedirect(request.getContextPath() + "/user/profile");
        }
    }
}
