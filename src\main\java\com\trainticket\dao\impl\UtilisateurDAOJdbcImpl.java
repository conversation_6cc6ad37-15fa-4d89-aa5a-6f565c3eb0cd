package com.trainticket.dao.impl;

import com.trainticket.dao.UtilisateurDAO;
import com.trainticket.model.Utilisateur;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class UtilisateurDAOJdbcImpl implements UtilisateurDAO {

    private static final Logger logger = LoggerFactory.getLogger(UtilisateurDAOJdbcImpl.class);

    private static final String URL = "********************************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "123";

    private Connection getConnection() throws SQLException {
        return DriverManager.getConnection(URL, USERNAME, PASSWORD);
    }

    @Override
    public Utilisateur save(Utilisateur utilisateur) {
        String sql = "INSERT INTO utilisateurs (email, motDePasse, nom, prenom, telephone, adresse, role, statut, date_creation) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            stmt.setString(1, utilisateur.getEmail());
            stmt.setString(2, utilisateur.getMotDePasse());
            stmt.setString(3, utilisateur.getNom());
            stmt.setString(4, utilisateur.getPrenom());
            stmt.setString(5, utilisateur.getTelephone());
            stmt.setString(6, utilisateur.getAdresse());
            stmt.setString(7, utilisateur.getRole().name());
            stmt.setString(8, utilisateur.getStatut().name());
            stmt.setTimestamp(9, Timestamp.valueOf(LocalDateTime.now()));

            int affectedRows = stmt.executeUpdate();

            if (affectedRows == 0) {
                throw new SQLException("Échec de la création de l'utilisateur, aucune ligne affectée.");
            }

            try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    utilisateur.setId(generatedKeys.getLong(1));
                } else {
                    throw new SQLException("Échec de la création de l'utilisateur, aucun ID obtenu.");
                }
            }

            logger.info("Utilisateur créé avec succès : {}", utilisateur.getEmail());
            return utilisateur;

        } catch (SQLException e) {
            logger.error("Erreur lors de la création de l'utilisateur : {}", utilisateur.getEmail(), e);
            throw new RuntimeException("Erreur lors de la création de l'utilisateur", e);
        }
    }

    @Override
    public Optional<Utilisateur> findByEmail(String email) {
        String sql = "SELECT * FROM utilisateurs WHERE email = ?";

        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, email);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToUtilisateur(rs));
                }
            }

        } catch (SQLException e) {
            logger.error("Erreur lors de la recherche de l'utilisateur par email : {}", email, e);
        }

        return Optional.empty();
    }

    @Override
    public boolean existsByEmail(String email) {
        String sql = "SELECT COUNT(*) FROM utilisateurs WHERE email = ?";

        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, email);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }

        } catch (SQLException e) {
            logger.error("Erreur lors de la vérification de l'existence de l'email : {}", email, e);
        }

        return false;
    }

    private Utilisateur mapResultSetToUtilisateur(ResultSet rs) throws SQLException {
        Utilisateur utilisateur = new Utilisateur();
        utilisateur.setId(rs.getLong("id"));
        utilisateur.setEmail(rs.getString("email"));
        utilisateur.setMotDePasse(rs.getString("motDePasse"));
        utilisateur.setNom(rs.getString("nom"));
        utilisateur.setPrenom(rs.getString("prenom"));
        utilisateur.setTelephone(rs.getString("telephone"));
        utilisateur.setAdresse(rs.getString("adresse"));
        utilisateur.setRole(Utilisateur.Role.valueOf(rs.getString("role")));
        utilisateur.setStatut(Utilisateur.StatutCompte.valueOf(rs.getString("statut")));

        Timestamp dateCreation = rs.getTimestamp("date_creation");
        if (dateCreation != null) {
            utilisateur.setDateCreation(dateCreation.toLocalDateTime());
        }

        Timestamp derniereConnexion = rs.getTimestamp("derniere_connexion");
        if (derniereConnexion != null) {
            utilisateur.setDerniereConnexion(derniereConnexion.toLocalDateTime());
        }

        return utilisateur;
    }

    // Méthodes non implémentées pour l'instant (on peut les ajouter si nécessaire)
    @Override
    public Utilisateur update(Utilisateur entity) {
        throw new UnsupportedOperationException("Non implémenté");
    }

    @Override
    public void delete(Utilisateur entity) {
        throw new UnsupportedOperationException("Non implémenté");
    }

    @Override
    public void deleteById(Long id) {
        throw new UnsupportedOperationException("Non implémenté");
    }

    @Override
    public Optional<Utilisateur> findById(Long id) {
        String sql = "SELECT * FROM utilisateurs WHERE id = ?";

        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, id);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToUtilisateur(rs));
                }
            }

        } catch (SQLException e) {
            logger.error("Erreur lors de la recherche de l'utilisateur par ID : {}", id, e);
        }

        return Optional.empty();
    }

    @Override
    public boolean existsById(Long id) {
        String sql = "SELECT COUNT(*) FROM utilisateurs WHERE id = ?";

        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, id);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }

        } catch (SQLException e) {
            logger.error("Erreur lors de la vérification de l'existence de l'utilisateur ID : {}", id, e);
        }

        return false;
    }

    @Override
    public List<Utilisateur> findAll() {
        return new ArrayList<>();
    }

    @Override
    public long count() {
        return 0;
    }

    @Override
    public List<Utilisateur> findByRole(Utilisateur.Role role) {
        return new ArrayList<>();
    }

    @Override
    public List<Utilisateur> findByStatut(Utilisateur.StatutCompte statut) {
        return new ArrayList<>();
    }

    @Override
    public void updateLastLogin(Long utilisateurId) {
        String sql = "UPDATE utilisateurs SET derniere_connexion = ? WHERE id = ?";

        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setTimestamp(1, Timestamp.valueOf(LocalDateTime.now()));
            stmt.setLong(2, utilisateurId);

            stmt.executeUpdate();
            logger.debug("Dernière connexion mise à jour pour l'utilisateur ID: {}", utilisateurId);

        } catch (SQLException e) {
            logger.error("Erreur lors de la mise à jour de la dernière connexion pour l'utilisateur ID: {}", utilisateurId, e);
        }
    }

    @Override
    public List<Utilisateur> findWithPagination(int page, int size) {
        return new ArrayList<>();
    }

    // Méthodes manquantes de l'interface
    @Override
    public List<Utilisateur> searchByName(String searchTerm) {
        return new ArrayList<>();
    }

    @Override
    public List<Utilisateur> findActiveUsers() {
        return new ArrayList<>();
    }

    @Override
    public long countByRole(Utilisateur.Role role) {
        return 0;
    }

    @Override
    public long countByStatut(Utilisateur.StatutCompte statut) {
        return 0;
    }

    @Override
    public List<Utilisateur> findRecentUsers(int days) {
        return new ArrayList<>();
    }

    @Override
    public List<Utilisateur> advancedSearch(String email, String nom, Utilisateur.Role role, Utilisateur.StatutCompte statut) {
        return new ArrayList<>();
    }
}
