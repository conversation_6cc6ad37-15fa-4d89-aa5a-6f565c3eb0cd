package com.trainticket.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trainticket.model.*;
import com.trainticket.service.RechercheService;
import com.trainticket.service.ReservationService;
import com.trainticket.service.impl.RechercheServiceImpl;
import com.trainticket.service.impl.ReservationServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

public class PaiementServlet extends HttpServlet {

    private static final Logger logger = LoggerFactory.getLogger(PaiementServlet.class);
    private ReservationService reservationService;
    private RechercheService rechercheService;
    private ObjectMapper objectMapper;
    private SecureRandom secureRandom;

    @Override
    public void init() throws ServletException {
        super.init();
        this.reservationService = new ReservationServiceImpl();
        this.rechercheService = new RechercheServiceImpl();
        this.objectMapper = new ObjectMapper();
        this.secureRandom = new SecureRandom();
        logger.info("PaiementServlet initialisé");
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Vérifier l'authentification
        if (!isUserAuthenticated(request)) {
            response.sendRedirect(request.getContextPath() + "/auth/login");
            return;
        }

        String pathInfo = request.getPathInfo();

        if (pathInfo == null) {
            response.sendRedirect(request.getContextPath() + "/");
            return;
        }

        switch (pathInfo) {
            case "/checkout":
                showCheckoutPage(request, response);
                break;
            case "/success":
                showSuccessPage(request, response);
                break;
            case "/cancel":
                showCancelPage(request, response);
                break;
            case "/receipt":
                showReceiptPage(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Vérifier l'authentification
        if (!isUserAuthenticated(request)) {
            response.sendRedirect(request.getContextPath() + "/auth/login");
            return;
        }

        String pathInfo = request.getPathInfo();

        if (pathInfo == null) {
            response.sendRedirect(request.getContextPath() + "/");
            return;
        }

        switch (pathInfo) {
            case "/process":
                handlePaymentProcess(request, response);
                break;
            case "/verify-card":
                handleCardVerification(request, response);
                break;
            case "/apply-promo":
                handlePromoCode(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    private void showCheckoutPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession();

        // Récupérer les données de réservation de la session
        Long voyageId = (Long) session.getAttribute("reservationVoyageId");
        Voyage.ClasseBillet classe = (Voyage.ClasseBillet) session.getAttribute("reservationClasse");
        Preference preferences = (Preference) session.getAttribute("reservationPreferences");

        if (voyageId == null || classe == null) {
            response.sendRedirect(request.getContextPath() + "/recherche/search");
            return;
        }

        try {
            Voyage voyage = rechercheService.getVoyageDetails(voyageId);

            // Vérifier la disponibilité une dernière fois
            if (!reservationService.verifierDisponibilite(voyage, classe, 1)) {
                request.setAttribute("errorMessage", "Cette réservation n'est plus disponible");
                response.sendRedirect(request.getContextPath() + "/recherche/search");
                return;
            }

            // Calculer le prix
            BigDecimal prix = reservationService.calculerPrix(voyage, classe, preferences);

            // Générer un token de session pour la sécurité
            String paymentToken = generatePaymentToken();
            session.setAttribute("paymentToken", paymentToken);
            session.setAttribute("paymentAmount", prix);

            request.setAttribute("voyage", voyage);
            request.setAttribute("classe", classe);
            request.setAttribute("preferences", preferences);
            request.setAttribute("prix", prix);
            request.setAttribute("paymentToken", paymentToken);

            request.getRequestDispatcher("/WEB-INF/views/payment/checkout.jsp")
                   .forward(request, response);

        } catch (Exception e) {
            logger.error("Erreur lors de la préparation du paiement", e);
            request.setAttribute("errorMessage", "Erreur lors de la préparation du paiement");
            response.sendRedirect(request.getContextPath() + "/recherche/search");
        }
    }

    private void handlePaymentProcess(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession();
        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");

        try {
            // Vérifier le token de sécurité
            String paymentToken = request.getParameter("paymentToken");
            String sessionToken = (String) session.getAttribute("paymentToken");

            if (paymentToken == null || !paymentToken.equals(sessionToken)) {
                throw new SecurityException("Token de paiement invalide");
            }

            // Récupérer les données de paiement
            String numeroCarteStr = request.getParameter("numeroCarte");
            String nomTitulaire = request.getParameter("nomTitulaire");
            String dateExpiration = request.getParameter("dateExpiration");
            String cvv = request.getParameter("cvv");
            String typePaiement = request.getParameter("typePaiement");
            String codePromo = request.getParameter("codePromo");

            // Valider les données de carte
            if (!isValidCardNumber(numeroCarteStr) || !isValidCVV(cvv) ||
                !isValidExpirationDate(dateExpiration)) {
                throw new IllegalArgumentException("Données de carte invalides");
            }

            // Récupérer les données de réservation
            Long voyageId = (Long) session.getAttribute("reservationVoyageId");
            Voyage.ClasseBillet classe = (Voyage.ClasseBillet) session.getAttribute("reservationClasse");
            Preference preferences = (Preference) session.getAttribute("reservationPreferences");

            Voyage voyage = rechercheService.getVoyageDetails(voyageId);

            // Calculer le prix final avec promotion éventuelle
            BigDecimal prix = reservationService.calculerPrix(voyage, classe, preferences);
            if (codePromo != null && !codePromo.trim().isEmpty()) {
                prix = reservationService.appliquerPromotion(prix, codePromo, utilisateur);
            }

            // Simuler le traitement du paiement
            boolean paiementReussi = processPayment(numeroCarteStr, prix, typePaiement);

            if (!paiementReussi) {
                throw new RuntimeException("Paiement refusé par la banque");
            }

            // Créer la réservation après paiement réussi
            Billet billet = reservationService.creerReservation(utilisateur, voyage, classe, preferences);

            // Enregistrer les détails du paiement
            String transactionId = generateTransactionId();
            session.setAttribute("transactionId", transactionId);
            session.setAttribute("paiementMontant", prix);
            session.setAttribute("paiementDate", LocalDateTime.now());

            logger.info("Paiement réussi: {} MAD pour le billet {} (transaction: {}, titulaire: {})",
                       prix, billet.getNumeroBillet(), transactionId, nomTitulaire);

            // Nettoyer la session
            session.removeAttribute("reservationVoyageId");
            session.removeAttribute("reservationClasse");
            session.removeAttribute("reservationPreferences");
            session.removeAttribute("paymentToken");
            session.removeAttribute("paymentAmount");

            // Rediriger vers la page de succès
            response.sendRedirect(request.getContextPath() + "/paiement/success?billet=" +
                                billet.getNumeroBillet() + "&transaction=" + transactionId);

        } catch (Exception e) {
            logger.error("Erreur lors du traitement du paiement", e);
            session.setAttribute("errorMessage", "Erreur lors du paiement: " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/paiement/checkout");
        }
    }

    private void showSuccessPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String numeroBillet = request.getParameter("billet");
        String transactionId = request.getParameter("transaction");

        if (numeroBillet == null || transactionId == null) {
            response.sendRedirect(request.getContextPath() + "/user/reservations");
            return;
        }

        try {
            Billet billet = reservationService.getBilletDetails(numeroBillet);

            HttpSession session = request.getSession();
            BigDecimal montantPaye = (BigDecimal) session.getAttribute("paiementMontant");
            LocalDateTime datePaiement = (LocalDateTime) session.getAttribute("paiementDate");

            request.setAttribute("billet", billet);
            request.setAttribute("transactionId", transactionId);
            request.setAttribute("montantPaye", montantPaye);
            request.setAttribute("datePaiement", datePaiement);

            request.getRequestDispatcher("/WEB-INF/views/payment/success.jsp")
                   .forward(request, response);

        } catch (ReservationService.ReservationException e) {
            logger.error("Erreur lors de l'affichage de la page de succès", e);
            response.sendRedirect(request.getContextPath() + "/user/reservations");
        }
    }

    private void showCancelPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Nettoyer la session
        HttpSession session = request.getSession();
        session.removeAttribute("paymentToken");
        session.removeAttribute("paymentAmount");

        request.getRequestDispatcher("/WEB-INF/views/payment/cancel.jsp").forward(request, response);
    }

    private void showReceiptPage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String numeroBillet = request.getParameter("billet");

        if (numeroBillet == null) {
            response.sendRedirect(request.getContextPath() + "/user/reservations");
            return;
        }

        try {
            Billet billet = reservationService.getBilletDetails(numeroBillet);

            // Vérifier que le billet appartient à l'utilisateur connecté
            HttpSession session = request.getSession();
            Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");

            if (!billet.getUtilisateur().getId().equals(utilisateur.getId())) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN);
                return;
            }

            request.setAttribute("billet", billet);
            request.getRequestDispatcher("/WEB-INF/views/payment/receipt.jsp")
                   .forward(request, response);

        } catch (ReservationService.ReservationException e) {
            logger.error("Erreur lors de l'affichage du reçu", e);
            response.sendRedirect(request.getContextPath() + "/user/reservations");
        }
    }

    private void handleCardVerification(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try {
            String numeroCarte = request.getParameter("numeroCarte");
            String cvv = request.getParameter("cvv");
            String dateExpiration = request.getParameter("dateExpiration");

            boolean carteValide = isValidCardNumber(numeroCarte) &&
                                 isValidCVV(cvv) &&
                                 isValidExpirationDate(dateExpiration);

            Map<String, Object> result = new HashMap<>();
            result.put("valid", carteValide);

            if (carteValide) {
                result.put("cardType", getCardType(numeroCarte));
                result.put("message", "Carte valide");
            } else {
                result.put("message", "Données de carte invalides");
            }

            objectMapper.writeValue(response.getWriter(), result);

        } catch (Exception e) {
            logger.error("Erreur lors de la vérification de carte", e);

            Map<String, Object> result = new HashMap<>();
            result.put("valid", false);
            result.put("message", "Erreur lors de la vérification");

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            objectMapper.writeValue(response.getWriter(), result);
        }
    }

    private void handlePromoCode(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try {
            String codePromo = request.getParameter("codePromo");
            BigDecimal prixOriginal = new BigDecimal(request.getParameter("prixOriginal"));

            HttpSession session = request.getSession();
            Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");

            BigDecimal nouveauPrix = reservationService.appliquerPromotion(prixOriginal, codePromo, utilisateur);
            BigDecimal reduction = prixOriginal.subtract(nouveauPrix);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("nouveauPrix", nouveauPrix);
            result.put("reduction", reduction);
            result.put("message", "Code promo appliqué avec succès");

            objectMapper.writeValue(response.getWriter(), result);

        } catch (Exception e) {
            logger.error("Erreur lors de l'application du code promo", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Code promo invalide ou expiré");

            objectMapper.writeValue(response.getWriter(), result);
        }
    }

    // Méthodes utilitaires

    private boolean isUserAuthenticated(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        return session != null && session.getAttribute("utilisateur") != null;
    }

    private String generatePaymentToken() {
        byte[] bytes = new byte[32];
        secureRandom.nextBytes(bytes);
        StringBuilder token = new StringBuilder();
        for (byte b : bytes) {
            token.append(String.format("%02x", b));
        }
        return token.toString();
    }

    private String generateTransactionId() {
        return "TXN" + System.currentTimeMillis() + secureRandom.nextInt(1000);
    }

    private boolean isValidCardNumber(String numeroCarte) {
        if (numeroCarte == null) return false;
        String cleaned = numeroCarte.replaceAll("\\s+", "");
        return cleaned.matches("\\d{13,19}") && luhnCheck(cleaned);
    }

    private boolean luhnCheck(String cardNumber) {
        int sum = 0;
        boolean alternate = false;
        for (int i = cardNumber.length() - 1; i >= 0; i--) {
            int n = Integer.parseInt(cardNumber.substring(i, i + 1));
            if (alternate) {
                n *= 2;
                if (n > 9) {
                    n = (n % 10) + 1;
                }
            }
            sum += n;
            alternate = !alternate;
        }
        return (sum % 10 == 0);
    }

    private boolean isValidCVV(String cvv) {
        return cvv != null && cvv.matches("\\d{3,4}");
    }

    private boolean isValidExpirationDate(String dateExpiration) {
        if (dateExpiration == null) return false;
        return dateExpiration.matches("(0[1-9]|1[0-2])/\\d{2}");
    }

    private String getCardType(String numeroCarte) {
        String cleaned = numeroCarte.replaceAll("\\s+", "");
        if (cleaned.startsWith("4")) return "Visa";
        if (cleaned.startsWith("5")) return "MasterCard";
        if (cleaned.startsWith("3")) return "American Express";
        return "Inconnue";
    }

    private boolean processPayment(String numeroCarte, BigDecimal montant, String typePaiement) {
        // Simulation du traitement de paiement
        // Dans un vrai système, on ferait appel à une API de paiement

        // Simuler un délai de traitement
        try {
            Thread.sleep(1000 + secureRandom.nextInt(2000));
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Simuler un taux de succès de 95%
        return secureRandom.nextInt(100) < 95;
    }
}
