-- Script de correction des caractères spéciaux pour les noms de villes tunisiennes
-- À exécuter pour corriger l'encodage des caractères arabes et français

USE train_ticket_db;

-- Désactiver les vérifications de clés étrangères temporairement
SET FOREIGN_KEY_CHECKS = 0;

-- Commencer une transaction
START TRANSACTION;

-- ========================================
-- CORRECTION DES NOMS DE VILLES ET GARES
-- ========================================

-- Corri<PERSON> (actuellement mal encodé)
UPDATE gares SET 
    ville = 'Gabès',
    nom = 'Gare de Gabès',
    adresse = 'Avenue Farhat Hached, Gabès'
WHERE ville LIKE 'Gab%' OR ville LIKE '%Gab%' OR code_gare = 'GAB01' OR code_gare = 'GAB';

-- Corriger Béja
UPDATE gares SET 
    ville = 'Béja',
    nom = 'Gare de Béja',
    adresse = 'Avenue Habib Bourguiba, Béja'
WHERE ville LIKE 'B%ja' OR ville LIKE '%Beja%' OR code_gare = 'BEJ01' OR code_gare = 'BEJ';

-- Corriger Médenine
UPDATE gares SET 
    ville = 'Médenine',
    nom = 'Gare de Médenine',
    adresse = 'Avenue de la République, Médenine'
WHERE ville LIKE 'M%denine' OR ville LIKE '%Medenine%' OR code_gare = 'MED01' OR code_gare = 'MED';

-- Corriger Kébili
UPDATE gares SET 
    ville = 'Kébili',
    nom = 'Gare de Kébili',
    adresse = 'Avenue Habib Bourguiba, Kébili'
WHERE ville LIKE 'K%bili' OR ville LIKE '%Kebili%' OR code_gare = 'KEB01' OR code_gare = 'KEB';

-- Corriger Tataouine
UPDATE gares SET 
    ville = 'Tataouine',
    nom = 'Gare de Tataouine',
    adresse = 'Avenue de l\'Indépendance, Tataouine'
WHERE ville LIKE 'Tataouine%' OR code_gare = 'TAT01' OR code_gare = 'TAT';

-- Corriger Jendouba
UPDATE gares SET 
    ville = 'Jendouba',
    nom = 'Gare de Jendouba',
    adresse = 'Avenue Habib Bourguiba, Jendouba'
WHERE ville LIKE 'Jendouba%' OR code_gare = 'JEN01' OR code_gare = 'JEN';

-- Corriger Le Kef
UPDATE gares SET 
    ville = 'Le Kef',
    nom = 'Gare du Kef',
    adresse = 'Avenue Habib Bourguiba, Le Kef'
WHERE ville LIKE '%Kef%' OR code_gare = 'KEF01' OR code_gare = 'KEF';

-- Corriger Siliana
UPDATE gares SET 
    ville = 'Siliana',
    nom = 'Gare de Siliana',
    adresse = 'Avenue de la République, Siliana'
WHERE ville LIKE 'Siliana%' OR code_gare = 'SIL01' OR code_gare = 'SIL';

-- Corriger Kasserine
UPDATE gares SET 
    ville = 'Kasserine',
    nom = 'Gare de Kasserine',
    adresse = 'Avenue Habib Bourguiba, Kasserine'
WHERE ville LIKE 'Kasserine%' OR code_gare = 'KAS01' OR code_gare = 'KAS';

-- Corriger Sidi Bouzid
UPDATE gares SET 
    ville = 'Sidi Bouzid',
    nom = 'Gare de Sidi Bouzid',
    adresse = 'Avenue de l\'Indépendance, Sidi Bouzid'
WHERE ville LIKE 'Sidi Bouzid%' OR code_gare = 'SBZ01' OR code_gare = 'SBZ';

-- ========================================
-- CORRECTION DES DESCRIPTIONS DE TRAJETS
-- ========================================

-- Corriger les descriptions de trajets contenant Gabès
UPDATE trajets SET 
    description = REPLACE(description, 'GabÔö£┬┐s', 'Gabès')
WHERE description LIKE '%Gab%';

-- Corriger les descriptions de trajets contenant Béja
UPDATE trajets SET 
    description = REPLACE(description, 'Beja', 'Béja')
WHERE description LIKE '%Beja%';

-- Corriger les descriptions de trajets contenant Médenine
UPDATE trajets SET 
    description = REPLACE(description, 'Medenine', 'Médenine')
WHERE description LIKE '%Medenine%';

-- Corriger les descriptions de trajets contenant Kébili
UPDATE trajets SET 
    description = REPLACE(description, 'Kebili', 'Kébili')
WHERE description LIKE '%Kebili%';

-- ========================================
-- AJOUTER DES GARES MANQUANTES AVEC CARACTÈRES CORRECTS
-- ========================================

-- Insérer Béja si elle n'existe pas
INSERT IGNORE INTO gares (nom, ville, code_gare, adresse, latitude, longitude, active) VALUES
('Gare de Béja', 'Béja', 'BEJ01', 'Avenue Habib Bourguiba, Béja', 36.7256, 9.1817, TRUE);

-- Insérer Médenine si elle n'existe pas
INSERT IGNORE INTO gares (nom, ville, code_gare, adresse, latitude, longitude, active) VALUES
('Gare de Médenine', 'Médenine', 'MED01', 'Avenue de la République, Médenine', 33.3549, 10.5055, TRUE);

-- Insérer Kébili si elle n'existe pas
INSERT IGNORE INTO gares (nom, ville, code_gare, adresse, latitude, longitude, active) VALUES
('Gare de Kébili', 'Kébili', 'KEB01', 'Avenue Habib Bourguiba, Kébili', 33.7044, 8.9690, TRUE);

-- ========================================
-- VÉRIFICATION ET VALIDATION
-- ========================================

-- Afficher les gares avec caractères spéciaux pour vérification
SELECT id, nom, ville, code_gare, adresse 
FROM gares 
WHERE ville IN ('Gabès', 'Béja', 'Médenine', 'Kébili', 'Tataouine', 'Jendouba', 'Le Kef', 'Siliana', 'Kasserine', 'Sidi Bouzid')
ORDER BY ville;

-- Compter le nombre total de gares
SELECT COUNT(*) as total_gares FROM gares WHERE active = TRUE;

-- Valider la transaction
COMMIT;

-- Réactiver les vérifications de clés étrangères
SET FOREIGN_KEY_CHECKS = 1;

SELECT 'Correction des caractères spéciaux terminée avec succès!' as message;
