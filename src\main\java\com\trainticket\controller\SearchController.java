package com.trainticket.controller;

import com.trainticket.dao.GareDAO;
import com.trainticket.dao.TrajetDAO;
import com.trainticket.dao.impl.GareDAOImpl;
import com.trainticket.dao.impl.TrajetDAOImpl;
import com.trainticket.model.Trajet;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * Contrôleur pour les pages de recherche
 * Récupère les données dynamiques pour les formulaires de recherche
 */
public class SearchController {

    private final GareDAO gareDAO;
    private final TrajetDAO trajetDAO;

    public SearchController() {
        this.gareDAO = new GareDAOImpl();
        this.trajetDAO = new TrajetDAOImpl();
    }

    /**
     * Charge les données pour la page de recherche
     * @param request la requête HTTP
     */
    public void loadSearchData(HttpServletRequest request) {
        try {
            // Récupérer les villes disponibles
            List<String> villes = gareDAO.findDistinctCities();
            request.setAttribute("villes", villes);

            // Récupérer les trajets populaires (limité à 6)
            List<Trajet> trajetsPopulaires = trajetDAO.findMostPopularTrajets(6);
            request.setAttribute("trajetsPopulaires", trajetsPopulaires);

            // Date actuelle pour la validation
            request.setAttribute("now", new Date());

            System.out.println("Données de recherche chargées: " + villes.size() + " villes, " +
                              trajetsPopulaires.size() + " trajets populaires");

        } catch (Exception e) {
            System.err.println("Erreur lors du chargement des données de recherche: " + e.getMessage());
            e.printStackTrace();

            // Valeurs par défaut en cas d'erreur
            setDefaultSearchValues(request);
        }
    }

    /**
     * Charge les données pour les résultats de recherche
     * @param request la requête HTTP
     */
    public void loadSearchResultsData(HttpServletRequest request) {
        try {
            // Récupérer les villes pour les filtres
            List<String> villes = gareDAO.findDistinctCities();
            request.setAttribute("villes", villes);

            // Date actuelle
            request.setAttribute("now", new Date());

            System.out.println("Données des résultats de recherche chargées");

        } catch (Exception e) {
            System.err.println("Erreur lors du chargement des données des résultats: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Définit des valeurs par défaut pour la recherche
     * @param request la requête HTTP
     */
    private void setDefaultSearchValues(HttpServletRequest request) {
        // Villes tunisiennes par défaut
        List<String> villesDefaut = List.of(
            "Tunis", "Sfax", "Sousse", "Kairouan", "Bizerte",
            "Gabès", "Ariana", "Gafsa", "Monastir", "Ben Arous",
            "Nabeul", "Kasserine", "Médenine", "Jendouba", "Mahdia"
        );

        request.setAttribute("villes", villesDefaut);
        request.setAttribute("trajetsPopulaires", List.of());
        request.setAttribute("now", new Date());

        System.out.println("ATTENTION: Utilisation des valeurs par défaut pour la recherche");
    }

    /**
     * Récupère les statistiques de recherche
     * @param request la requête HTTP
     */
    public void loadSearchStatistics(HttpServletRequest request) {
        try {
            // Nombre total de trajets actifs
            long nombreTrajets = trajetDAO.countActiveTrajets();
            request.setAttribute("nombreTrajetsDisponibles", nombreTrajets);

            // Nombre de villes desservies
            List<String> villes = gareDAO.findDistinctCities();
            request.setAttribute("nombreVillesDesservies", villes.size());

            // Trajets les plus rapides (utiliser les trajets actifs pour l'instant)
            List<Trajet> trajetsRapides = trajetDAO.findActiveTrajets();
            if (trajetsRapides.size() > 3) {
                trajetsRapides = trajetsRapides.subList(0, 3);
            }
            request.setAttribute("trajetsRapides", trajetsRapides);

            System.out.println("Statistiques de recherche chargées: " + nombreTrajets + " trajets, " +
                              villes.size() + " villes");

        } catch (Exception e) {
            System.err.println("Erreur lors du chargement des statistiques de recherche: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
