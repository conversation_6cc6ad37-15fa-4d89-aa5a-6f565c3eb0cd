package com.trainticket.controller;

import com.trainticket.dao.GareDAO;
import com.trainticket.dao.TrajetDAO;
import com.trainticket.dao.UtilisateurDAO;
import com.trainticket.dao.VoyageDAO;
import com.trainticket.dao.BilletDAO;
import com.trainticket.dao.impl.GareDAOImpl;
import com.trainticket.dao.impl.TrajetDAOImpl;
import com.trainticket.dao.impl.UtilisateurDAOImpl;
import com.trainticket.dao.impl.VoyageDAOImpl;
import com.trainticket.dao.impl.BilletDAOImpl;
import com.trainticket.model.Trajet;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * Contrôleur pour les pages d'administration
 * Récupère les données dynamiques pour le tableau de bord et la gestion
 */
public class AdminController {

    private final GareDAO gareDAO;
    private final TrajetDAO trajetDAO;
    private final UtilisateurDAO utilisateurDAO;
    private final VoyageDAO voyageDAO;
    private final BilletDAO billetDAO;

    public AdminController() {
        this.gareDAO = new GareDAOImpl();
        this.trajetDAO = new TrajetDAOImpl();
        this.utilisateurDAO = new UtilisateurDAOImpl();
        this.voyageDAO = new VoyageDAOImpl();
        this.billetDAO = new BilletDAOImpl();
    }

    /**
     * Charge les données pour le tableau de bord administrateur
     * @param request la requête HTTP
     */
    public void loadDashboardData(HttpServletRequest request) {
        try {
            // Statistiques principales
            loadMainStatistics(request);

            // Activité récente
            loadRecentActivity(request);

            // Trajets populaires
            loadPopularRoutes(request);

            // Alertes système
            loadSystemAlerts(request);

            // Date actuelle
            request.setAttribute("now", new Date());

            System.out.println("Données du tableau de bord admin chargées");

        } catch (Exception e) {
            System.err.println("Erreur lors du chargement des données admin: " + e.getMessage());
            e.printStackTrace();
            setDefaultAdminValues(request);
        }
    }

    /**
     * Charge les statistiques principales
     * @param request la requête HTTP
     */
    private void loadMainStatistics(HttpServletRequest request) {
        try {
            Map<String, Object> stats = new HashMap<>();

            // Nombre d'utilisateurs
            long nombreUtilisateurs = utilisateurDAO.count();
            stats.put("nombreUtilisateurs", nombreUtilisateurs);

            // Nouveaux utilisateurs ce mois
            // Simulation - dans un vrai projet, on aurait une méthode spécifique
            long nouveauxUtilisateurs = Math.max(1, nombreUtilisateurs / 10);
            stats.put("nouveauxUtilisateursCeMois", nouveauxUtilisateurs);

            // Nombre de réservations
            long nombreReservations = billetDAO.count();
            stats.put("nombreReservations", nombreReservations);

            // Réservations aujourd'hui
            long reservationsAujourdhui = Math.max(0, nombreReservations / 30);
            stats.put("reservationsAujourdhui", reservationsAujourdhui);

            // Nombre de voyages
            long nombreVoyages = voyageDAO.count();
            stats.put("nombreVoyages", nombreVoyages);

            // Voyages aujourd'hui
            long voyagesAujourdhui = voyageDAO.findByDate(LocalDate.now()).size();
            stats.put("voyagesAujourdhui", voyagesAujourdhui);

            // Chiffre d'affaires (simulation)
            BigDecimal chiffreAffaires = BigDecimal.valueOf(nombreReservations * 25.5);
            stats.put("chiffreAffaires", chiffreAffaires);

            request.setAttribute("statistiquesAdmin", stats);

            System.out.println("Statistiques admin chargées: " + nombreUtilisateurs + " utilisateurs, " +
                              nombreReservations + " réservations");

        } catch (Exception e) {
            System.err.println("Erreur lors du chargement des statistiques admin: " + e.getMessage());
            // Valeurs par défaut
            Map<String, Object> defaultStats = new HashMap<>();
            defaultStats.put("nombreUtilisateurs", 0L);
            defaultStats.put("nouveauxUtilisateursCeMois", 0L);
            defaultStats.put("nombreReservations", 0L);
            defaultStats.put("reservationsAujourdhui", 0L);
            defaultStats.put("nombreVoyages", 0L);
            defaultStats.put("voyagesAujourdhui", 0L);
            defaultStats.put("chiffreAffaires", BigDecimal.ZERO);
            request.setAttribute("statistiquesAdmin", defaultStats);
        }
    }

    /**
     * Charge l'activité récente
     * @param request la requête HTTP
     */
    private void loadRecentActivity(HttpServletRequest request) {
        try {
            List<Map<String, Object>> activites = new ArrayList<>();

            // Simulation d'activités récentes
            Map<String, Object> activite1 = new HashMap<>();
            activite1.put("description", "Nouvelle réservation pour Tunis → Sfax");
            activite1.put("date", new Date());
            activite1.put("icone", "ticket-alt");
            activite1.put("couleur", "success");
            activites.add(activite1);

            Map<String, Object> activite2 = new HashMap<>();
            activite2.put("description", "Nouvel utilisateur inscrit");
            activite2.put("date", new Date(System.currentTimeMillis() - 300000)); // 5 min ago
            activite2.put("icone", "user-plus");
            activite2.put("couleur", "primary");
            activites.add(activite2);

            Map<String, Object> activite3 = new HashMap<>();
            activite3.put("description", "Voyage Sousse → Kairouan terminé");
            activite3.put("date", new Date(System.currentTimeMillis() - 600000)); // 10 min ago
            activite3.put("icone", "check-circle");
            activite3.put("couleur", "info");
            activites.add(activite3);

            request.setAttribute("activiteRecente", activites);

        } catch (Exception e) {
            System.err.println("Erreur lors du chargement de l'activité récente: " + e.getMessage());
            request.setAttribute("activiteRecente", new ArrayList<>());
        }
    }

    /**
     * Charge les trajets populaires avec statistiques
     * @param request la requête HTTP
     */
    private void loadPopularRoutes(HttpServletRequest request) {
        try {
            List<Trajet> trajets = trajetDAO.findMostPopularTrajets(5);

            // Enrichir avec des statistiques simulées
            List<Map<String, Object>> trajetsAvecStats = new ArrayList<>();
            for (Trajet trajet : trajets) {
                Map<String, Object> trajetStats = new HashMap<>();
                trajetStats.put("gareDepart", trajet.getGareDepart());
                trajetStats.put("gareArrivee", trajet.getGareArrivee());
                trajetStats.put("numeroTrajet", trajet.getNumeroTrajet());
                trajetStats.put("type", trajet.getType());

                // Statistiques simulées
                int nombreReservations = (int) (Math.random() * 100) + 20;
                int tauxOccupation = (int) (Math.random() * 40) + 60;
                BigDecimal revenus = trajet.getPrixBase().multiply(BigDecimal.valueOf(nombreReservations));

                trajetStats.put("nombreReservations", nombreReservations);
                trajetStats.put("tauxOccupation", tauxOccupation);
                trajetStats.put("revenus", revenus);

                trajetsAvecStats.add(trajetStats);
            }

            request.setAttribute("trajetsPopulaires", trajetsAvecStats);

        } catch (Exception e) {
            System.err.println("Erreur lors du chargement des trajets populaires: " + e.getMessage());
            request.setAttribute("trajetsPopulaires", new ArrayList<>());
        }
    }

    /**
     * Charge les alertes système
     * @param request la requête HTTP
     */
    private void loadSystemAlerts(HttpServletRequest request) {
        try {
            List<Map<String, Object>> alertes = new ArrayList<>();

            // Vérifications système simulées
            long nombreGares = gareDAO.countActiveGares();
            if (nombreGares < 5) {
                Map<String, Object> alerte = new HashMap<>();
                alerte.put("message", "Peu de gares configurées (" + nombreGares + ")");
                alerte.put("niveau", "warning");
                alertes.add(alerte);
            }

            long nombreTrajets = trajetDAO.countActiveTrajets();
            if (nombreTrajets < 10) {
                Map<String, Object> alerte = new HashMap<>();
                alerte.put("message", "Peu de trajets disponibles (" + nombreTrajets + ")");
                alerte.put("niveau", "info");
                alertes.add(alerte);
            }

            request.setAttribute("alertesSysteme", alertes);

        } catch (Exception e) {
            System.err.println("Erreur lors du chargement des alertes: " + e.getMessage());
            request.setAttribute("alertesSysteme", new ArrayList<>());
        }
    }

    /**
     * Définit des valeurs par défaut pour l'administration
     * @param request la requête HTTP
     */
    private void setDefaultAdminValues(HttpServletRequest request) {
        Map<String, Object> defaultStats = new HashMap<>();
        defaultStats.put("nombreUtilisateurs", 0L);
        defaultStats.put("nouveauxUtilisateursCeMois", 0L);
        defaultStats.put("nombreReservations", 0L);
        defaultStats.put("reservationsAujourdhui", 0L);
        defaultStats.put("nombreVoyages", 0L);
        defaultStats.put("voyagesAujourdhui", 0L);
        defaultStats.put("chiffreAffaires", BigDecimal.ZERO);

        request.setAttribute("statistiquesAdmin", defaultStats);
        request.setAttribute("activiteRecente", new ArrayList<>());
        request.setAttribute("trajetsPopulaires", new ArrayList<>());
        request.setAttribute("alertesSysteme", new ArrayList<>());
        request.setAttribute("now", new Date());

        System.out.println("ATTENTION: Utilisation des valeurs par défaut pour l'administration");
    }
}
