-- Script pour tester toutes les combinaisons de recherche de voyages
USE train_ticket_db;

-- ========================================
-- VÉRIFICATION DE TOUTES LES COMBINAISONS
-- ========================================

SELECT '=== RÉSUMÉ DES TRAJETS PAR TYPE ===' as titre;

-- Statistiques par type
SELECT 
    t.type,
    COUNT(*) as nombre_trajets,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM trajets WHERE active = TRUE), 1) as pourcentage
FROM trajets t 
WHERE t.active = TRUE 
GROUP BY t.type 
ORDER BY COUNT(*) DESC;

SELECT '=== TRAINS EXPRESS ===' as titre;
SELECT 
    CONCAT(gd.ville, ' → ', ga.ville) as trajet,
    t.type,
    t.description,
    CONCAT(t.dureeMinutes, ' min') as duree,
    CONCAT(t.prixBase, ' TND') as prix,
    t.capaciteTotal as places
FROM trajets t
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE t.type = 'EXPRESS' AND t.active = TRUE
ORDER BY gd.ville, ga.ville;

SELECT '=== TRAINS DIRECT ===' as titre;
SELECT 
    CONCAT(gd.ville, ' → ', ga.ville) as trajet,
    t.type,
    t.description,
    CONCAT(t.dureeMinutes, ' min') as duree,
    CONCAT(t.prixBase, ' TND') as prix,
    t.capaciteTotal as places
FROM trajets t
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE t.type = 'DIRECT' AND t.active = TRUE
ORDER BY gd.ville, ga.ville;

SELECT '=== TRAINS NORMAL (Échantillon) ===' as titre;
SELECT 
    CONCAT(gd.ville, ' → ', ga.ville) as trajet,
    t.type,
    t.description,
    CONCAT(t.dureeMinutes, ' min') as duree,
    CONCAT(t.prixBase, ' TND') as prix,
    t.capaciteTotal as places
FROM trajets t
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE t.type = 'NORMAL' AND t.active = TRUE
ORDER BY gd.ville, ga.ville
LIMIT 15;

SELECT '=== VILLES DESSERVIES ===' as titre;
SELECT DISTINCT ville FROM gares WHERE active = TRUE ORDER BY ville;

SELECT '=== VOYAGES DISPONIBLES AUJOURD\'HUI ===' as titre;
SELECT 
    CONCAT(gd.ville, ' → ', ga.ville) as trajet,
    t.type,
    DATE_FORMAT(v.date_heure_depart, '%H:%i') as depart,
    DATE_FORMAT(v.date_heure_arrivee, '%H:%i') as arrivee,
    v.places_disponibles as places_dispo,
    CONCAT(t.prixBase, ' TND') as prix
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE DATE(v.date_heure_depart) = CURDATE()
AND v.statut = 'PROGRAMME'
AND v.places_disponibles > 0
ORDER BY t.type, v.date_heure_depart
LIMIT 20;

SELECT '=== STATISTIQUES GÉNÉRALES ===' as titre;
SELECT 
    'Gares actives' as element,
    COUNT(*) as nombre
FROM gares WHERE active = TRUE
UNION ALL
SELECT 
    'Trajets actifs' as element,
    COUNT(*) as nombre
FROM trajets WHERE active = TRUE
UNION ALL
SELECT 
    'Voyages programmés' as element,
    COUNT(*) as nombre
FROM voyages WHERE statut = 'PROGRAMME' AND date_heure_depart >= NOW()
UNION ALL
SELECT 
    'Utilisateurs' as element,
    COUNT(*) as nombre
FROM utilisateurs WHERE statut = 'ACTIF';

-- Test de recherche spécifique pour chaque type
SELECT '=== TEST RECHERCHE EXPRESS: Tunis → Sousse ===' as titre;
SELECT 
    DATE_FORMAT(v.date_heure_depart, '%Y-%m-%d %H:%i') as depart,
    DATE_FORMAT(v.date_heure_arrivee, '%Y-%m-%d %H:%i') as arrivee,
    v.places_disponibles as places,
    t.type,
    CONCAT(t.prixBase, ' TND') as prix
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE LOWER(gd.ville) = 'tunis'
AND LOWER(ga.ville) = 'sousse'
AND t.type = 'EXPRESS'
AND DATE(v.date_heure_depart) >= CURDATE()
AND v.statut = 'PROGRAMME'
ORDER BY v.date_heure_depart
LIMIT 5;

SELECT '=== TEST RECHERCHE DIRECT: Tunis → Gabès ===' as titre;
SELECT 
    DATE_FORMAT(v.date_heure_depart, '%Y-%m-%d %H:%i') as depart,
    DATE_FORMAT(v.date_heure_arrivee, '%Y-%m-%d %H:%i') as arrivee,
    v.places_disponibles as places,
    t.type,
    CONCAT(t.prixBase, ' TND') as prix
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE LOWER(gd.ville) = 'tunis'
AND LOWER(ga.ville) = 'gabès'
AND t.type = 'DIRECT'
AND DATE(v.date_heure_depart) >= CURDATE()
AND v.statut = 'PROGRAMME'
ORDER BY v.date_heure_depart
LIMIT 5;

SELECT '=== TEST RECHERCHE NORMAL: Tunis → Bizerte ===' as titre;
SELECT 
    DATE_FORMAT(v.date_heure_depart, '%Y-%m-%d %H:%i') as depart,
    DATE_FORMAT(v.date_heure_arrivee, '%Y-%m-%d %H:%i') as arrivee,
    v.places_disponibles as places,
    t.type,
    CONCAT(t.prixBase, ' TND') as prix
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE LOWER(gd.ville) = 'tunis'
AND LOWER(ga.ville) = 'bizerte'
AND t.type = 'NORMAL'
AND DATE(v.date_heure_depart) >= CURDATE()
AND v.statut = 'PROGRAMME'
ORDER BY v.date_heure_depart
LIMIT 5;
