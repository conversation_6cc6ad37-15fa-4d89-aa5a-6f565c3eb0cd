package com.trainticket.dao;

import com.trainticket.model.Gare;
import java.util.List;
import java.util.Optional;

public interface GareDAO extends BaseDAO<Gare, Long> {
    
    /**
     * Trouver une gare par son nom
     * @param nom le nom de la gare
     * @return Optional contenant la gare si trouvée
     */
    Optional<Gare> findByNom(String nom);
    
    /**
     * Trouver une gare par son code
     * @param codeGare le code de la gare
     * @return Optional contenant la gare si trouvée
     */
    Optional<Gare> findByCodeGare(String codeGare);
    
    /**
     * Trouver les gares par ville
     * @param ville la ville
     * @return liste des gares dans cette ville
     */
    List<Gare> findByVille(String ville);
    
    /**
     * Trouver toutes les gares actives
     * @return liste des gares actives
     */
    List<Gare> findActiveGares();
    
    /**
     * Rechercher des gares par nom ou ville
     * @param searchTerm terme de recherche
     * @return liste des gares correspondantes
     */
    List<Gare> searchByNameOrCity(String searchTerm);
    
    /**
     * Trouver les gares dans un rayon donné
     * @param latitude latitude du point central
     * @param longitude longitude du point central
     * @param radiusKm rayon en kilomètres
     * @return liste des gares dans le rayon
     */
    List<Gare> findGaresInRadius(double latitude, double longitude, double radiusKm);
    
    /**
     * Trouver toutes les villes distinctes
     * @return liste des villes ayant des gares
     */
    List<String> findDistinctCities();
    
    /**
     * Vérifier si une gare existe par son nom
     * @param nom le nom de la gare
     * @return true si la gare existe, false sinon
     */
    boolean existsByNom(String nom);
    
    /**
     * Vérifier si une gare existe par son code
     * @param codeGare le code de la gare
     * @return true si la gare existe, false sinon
     */
    boolean existsByCodeGare(String codeGare);
    
    /**
     * Compter les gares actives
     * @return nombre de gares actives
     */
    long countActiveGares();
}
