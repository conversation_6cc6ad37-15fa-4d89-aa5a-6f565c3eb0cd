-- Création de la base de données Train Ticket Management System
CREATE DATABASE IF NOT EXISTS train_ticket_db;
USE train_ticket_db;

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS utilisateurs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    mot_de_passe VARCHAR(255) NOT NULL,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    telephone VARCHAR(20),
    adresse TEXT,
    role ENUM('USER', 'ADMIN') DEFAULT 'USER',
    statut ENUM('ACTIF', 'BLOQUE', 'SUSPENDU') DEFAULT 'ACTIF',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    derniere_connexion TIMESTAMP NULL
);

-- Table des gares
CREATE TABLE IF NOT EXISTS gares (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    ville VARCHAR(100) NOT NULL,
    code VARCHAR(10) UNIQUE NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    actif BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des trajets
CREATE TABLE IF NOT EXISTS trajets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    gare_depart_id BIGINT NOT NULL,
    gare_arrivee_id BIGINT NOT NULL,
    distance_km DECIMAL(8, 2),
    duree_minutes INT,
    actif BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (gare_depart_id) REFERENCES gares(id),
    FOREIGN KEY (gare_arrivee_id) REFERENCES gares(id)
);

-- Table des voyages
CREATE TABLE IF NOT EXISTS voyages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    trajet_id BIGINT NOT NULL,
    numero_train VARCHAR(20) NOT NULL,
    heure_depart TIME NOT NULL,
    heure_arrivee TIME NOT NULL,
    prix_base DECIMAL(10, 2) NOT NULL,
    places_totales INT NOT NULL,
    places_disponibles INT NOT NULL,
    statut ENUM('ACTIF', 'ANNULE', 'COMPLET') DEFAULT 'ACTIF',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trajet_id) REFERENCES trajets(id)
);

-- Table des billets
CREATE TABLE IF NOT EXISTS billets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    utilisateur_id BIGINT NOT NULL,
    voyage_id BIGINT NOT NULL,
    numero_billet VARCHAR(50) UNIQUE NOT NULL,
    classe ENUM('PREMIERE', 'DEUXIEME', 'ECONOMIQUE') NOT NULL DEFAULT 'ECONOMIQUE',
    prix DECIMAL(10, 2) NOT NULL,
    statut ENUM('ACHETE', 'UTILISE', 'ANNULE', 'REMBOURSE') DEFAULT 'ACHETE',
    date_achat TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_utilisation TIMESTAMP NULL,
    date_annulation TIMESTAMP NULL,
    numero_siege VARCHAR(10),
    preference_fenetre ENUM('FENETRE', 'COULOIR', 'INDIFFERENT'),
    wagon_non_fumeur BOOLEAN DEFAULT TRUE,
    espace_famille BOOLEAN DEFAULT FALSE,
    acces_handicape BOOLEAN DEFAULT FALSE,
    wifi_requis BOOLEAN DEFAULT FALSE,
    prise_electrique BOOLEAN DEFAULT FALSE,
    commentaires_speciaux TEXT,
    commentaires VARCHAR(500),
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id),
    FOREIGN KEY (voyage_id) REFERENCES voyages(id)
);

-- Table des demandes d'annulation
CREATE TABLE IF NOT EXISTS demandes_annulation (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    billet_id BIGINT NOT NULL,
    utilisateur_id BIGINT NOT NULL,
    motif_annulation TEXT NOT NULL,
    date_demande TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_traitement TIMESTAMP NULL,
    statut ENUM('EN_ATTENTE', 'APPROUVEE', 'REJETEE', 'REMBOURSEE') DEFAULT 'EN_ATTENTE',
    commentaire_admin TEXT,
    admin_id BIGINT,
    montant_remboursement DECIMAL(10, 2),
    frais_annulation DECIMAL(10, 2) DEFAULT 0.00,
    FOREIGN KEY (billet_id) REFERENCES billets(id),
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id),
    FOREIGN KEY (admin_id) REFERENCES utilisateurs(id)
);

-- Insertion de données de test
INSERT IGNORE INTO gares (nom, ville, code, latitude, longitude) VALUES
('Gare Centrale', 'Casablanca', 'CASA', 33.5731, -7.5898),
('Gare Agdal', 'Rabat', 'RAB', 34.0209, -6.8416),
('Gare Principale', 'Marrakech', 'MAR', 31.6295, -7.9811),
('Gare Centrale', 'Fès', 'FES', 34.0181, -5.0078),
('Gare Principale', 'Tanger', 'TAN', 35.7595, -5.8340);

-- Insertion de trajets de test
INSERT IGNORE INTO trajets (gare_depart_id, gare_arrivee_id, distance_km, duree_minutes) VALUES
(1, 2, 87, 60),   -- Casablanca -> Rabat
(2, 1, 87, 60),   -- Rabat -> Casablanca
(1, 3, 237, 180), -- Casablanca -> Marrakech
(3, 1, 237, 180), -- Marrakech -> Casablanca
(1, 4, 298, 240), -- Casablanca -> Fès
(4, 1, 298, 240); -- Fès -> Casablanca

-- Insertion de voyages de test
INSERT IGNORE INTO voyages (trajet_id, numero_train, heure_depart, heure_arrivee, prix_base, places_totales, places_disponibles) VALUES
(1, 'TGV001', '08:00:00', '09:00:00', 45.00, 200, 200),
(1, 'TGV002', '14:00:00', '15:00:00', 45.00, 200, 200),
(2, 'TGV003', '10:00:00', '11:00:00', 45.00, 200, 200),
(3, 'TGV004', '07:30:00', '10:30:00', 85.00, 300, 300),
(4, 'TGV005', '16:00:00', '19:00:00', 85.00, 300, 300);

SELECT 'Base de données créée avec succès!' as message;
