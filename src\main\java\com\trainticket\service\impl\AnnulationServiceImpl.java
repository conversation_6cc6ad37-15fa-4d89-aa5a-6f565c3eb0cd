package com.trainticket.service.impl;

import com.trainticket.dao.DemandeAnnulationDAO;
import com.trainticket.dao.BilletDAO;
import com.trainticket.dao.impl.DemandeAnnulationDAOImpl;
import com.trainticket.dao.impl.BilletDAOImpl;
import com.trainticket.model.DemandeAnnulation;
import com.trainticket.model.Billet;
import com.trainticket.model.Utilisateur;
import com.trainticket.service.AnnulationService;
import com.trainticket.service.ServiceException;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public class AnnulationServiceImpl implements AnnulationService {

    private final DemandeAnnulationDAO demandeAnnulationDAO;
    private final BilletDAO billetDAO;

    public AnnulationServiceImpl() {
        this.demandeAnnulationDAO = new DemandeAnnulationDAOImpl();
        this.billetDAO = new BilletDAOImpl();
    }

    @Override
    public DemandeAnnulation creerDemandeAnnulation(Billet billet, Utilisateur utilisateur, String motif) throws ServiceException {
        try {
            // Vérifications
            if (billet == null) {
                throw new ServiceException("Le billet ne peut pas être null");
            }
            if (utilisateur == null) {
                throw new ServiceException("L'utilisateur ne peut pas être null");
            }
            if (motif == null || motif.trim().isEmpty()) {
                throw new ServiceException("Le motif d'annulation est obligatoire");
            }

            // Vérifier que le billet appartient à l'utilisateur
            if (!billet.getUtilisateur().getId().equals(utilisateur.getId())) {
                throw new ServiceException("Ce billet n'appartient pas à cet utilisateur");
            }

            // Vérifier que le billet peut être annulé
            if (!peutEtreAnnule(billet)) {
                throw new ServiceException("Ce billet ne peut pas être annulé");
            }

            // Vérifier qu'il n'y a pas déjà une demande pour ce billet
            if (demandeExistePourBillet(billet)) {
                throw new ServiceException("Une demande d'annulation existe déjà pour ce billet");
            }

            // Créer la demande
            DemandeAnnulation demande = new DemandeAnnulation(billet, utilisateur, motif);
            return demandeAnnulationDAO.save(demande);

        } catch (Exception e) {
            System.err.println("Erreur lors de la création de la demande d'annulation: " + e.getMessage());
            e.printStackTrace();
            throw new ServiceException("Erreur lors de la création de la demande d'annulation: " + e.getMessage());
        }
    }

    @Override
    public void approuverDemande(Long demandeId, Utilisateur admin, String commentaire) throws ServiceException {
        try {
            Optional<DemandeAnnulation> optionalDemande = demandeAnnulationDAO.findById(demandeId);
            if (!optionalDemande.isPresent()) {
                throw new ServiceException("Demande d'annulation non trouvée");
            }

            DemandeAnnulation demande = optionalDemande.get();

            if (!demande.peutEtreTraitee()) {
                throw new ServiceException("Cette demande ne peut plus être traitée");
            }

            // Approuver la demande
            demande.approuver(admin, commentaire);
            demandeAnnulationDAO.update(demande);

            // Libérer la place dans le voyage
            Billet billet = demande.getBillet();
            billet.getVoyage().libererPlace(billet.getClasse());

            // Mettre à jour le billet
            billetDAO.update(billet);

            System.out.println("Demande d'annulation " + demandeId + " approuvée par " + admin.getEmail());

        } catch (Exception e) {
            System.err.println("Erreur lors de l'approbation de la demande: " + e.getMessage());
            e.printStackTrace();
            throw new ServiceException("Erreur lors de l'approbation: " + e.getMessage());
        }
    }

    @Override
    public void rejeterDemande(Long demandeId, Utilisateur admin, String commentaire) throws ServiceException {
        try {
            Optional<DemandeAnnulation> optionalDemande = demandeAnnulationDAO.findById(demandeId);
            if (!optionalDemande.isPresent()) {
                throw new ServiceException("Demande d'annulation non trouvée");
            }

            DemandeAnnulation demande = optionalDemande.get();

            if (!demande.peutEtreTraitee()) {
                throw new ServiceException("Cette demande ne peut plus être traitée");
            }

            // Rejeter la demande
            demande.rejeter(admin, commentaire);
            demandeAnnulationDAO.update(demande);

            System.out.println("Demande d'annulation " + demandeId + " rejetée par " + admin.getEmail());

        } catch (Exception e) {
            System.err.println("Erreur lors du rejet de la demande: " + e.getMessage());
            e.printStackTrace();
            throw new ServiceException("Erreur lors du rejet: " + e.getMessage());
        }
    }

    @Override
    public List<DemandeAnnulation> getDemandesEnAttente() {
        try {
            return demandeAnnulationDAO.findDemandesEnAttente();
        } catch (Exception e) {
            System.err.println("Erreur lors de la récupération des demandes en attente: " + e.getMessage());
            e.printStackTrace();
            return List.of();
        }
    }

    @Override
    public List<DemandeAnnulation> getDemandesUtilisateur(Utilisateur utilisateur) {
        try {
            return demandeAnnulationDAO.findByUtilisateur(utilisateur);
        } catch (Exception e) {
            System.err.println("Erreur lors de la récupération des demandes de l'utilisateur: " + e.getMessage());
            e.printStackTrace();
            return List.of();
        }
    }

    @Override
    public boolean peutEtreAnnule(Billet billet) {
        if (billet == null) return false;

        // Le billet doit être acheté
        if (billet.getStatut() != Billet.StatutBillet.ACHETE) {
            return false;
        }

        // Le voyage ne doit pas être déjà commencé
        LocalDateTime maintenant = LocalDateTime.now();
        LocalDateTime dateVoyage = billet.getVoyage().getDateHeureDepart();

        // Autoriser l'annulation jusqu'à 2h avant le départ
        return dateVoyage.isAfter(maintenant.plusHours(2));
    }

    @Override
    public Optional<DemandeAnnulation> getDemandeById(Long id) {
        try {
            return demandeAnnulationDAO.findById(id);
        } catch (Exception e) {
            System.err.println("Erreur lors de la récupération de la demande: " + e.getMessage());
            e.printStackTrace();
            return Optional.empty();
        }
    }

    @Override
    public boolean demandeExistePourBillet(Billet billet) {
        try {
            Optional<DemandeAnnulation> demande = demandeAnnulationDAO.findByBillet(billet);
            return demande.isPresent() &&
                   (demande.get().getStatut() == DemandeAnnulation.StatutDemande.EN_ATTENTE ||
                    demande.get().getStatut() == DemandeAnnulation.StatutDemande.APPROUVEE);
        } catch (Exception e) {
            System.err.println("Erreur lors de la vérification de l'existence d'une demande: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
}
