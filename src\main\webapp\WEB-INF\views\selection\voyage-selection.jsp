<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sélection du Voyage - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .voyage-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            transition: all 0.3s ease;
        }
        .voyage-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 15px rgba(0,123,255,0.1);
        }
        .class-option {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .class-option:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .class-option.selected {
            border-color: #007bff;
            background-color: #e7f3ff;
        }
        .price-highlight {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <%@ include file="../common/header.jsp" %>

    <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="${pageContext.request.contextPath}/">Accueil</a></li>
                <li class="breadcrumb-item"><a href="${pageContext.request.contextPath}/recherche/search">Recherche</a></li>
                <li class="breadcrumb-item active">Sélection du voyage</li>
            </ol>
        </nav>

        <div class="row">
            <div class="col-lg-8">
                <!-- Détails du voyage -->
                <div class="card voyage-card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-train"></i> Détails du voyage</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-map-marker-alt text-success"></i> ${voyage.trajet.gareDepart.ville}</h5>
                                <p class="text-muted">${voyage.trajet.gareDepart.nom}</p>
                                <p class="h6">
                                    <fmt:formatDate value="${voyage.dateHeureDepart}" pattern="dd/MM/yyyy HH:mm"/>
                                </p>
                            </div>
                            <div class="col-md-6 text-end">
                                <h5><i class="fas fa-map-marker-alt text-danger"></i> ${voyage.trajet.gareArrivee.ville}</h5>
                                <p class="text-muted">${voyage.trajet.gareArrivee.nom}</p>
                                <p class="h6">
                                    <fmt:formatDate value="${voyage.dateHeureArrivee}" pattern="dd/MM/yyyy HH:mm"/>
                                </p>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <small class="text-muted">Durée</small>
                                <p class="fw-bold">${voyage.trajet.dureeMinutes} min</p>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">Type</small>
                                <p class="fw-bold">
                                    <c:choose>
                                        <c:when test="${voyage.trajet.type == 'EXPRESS'}">
                                            <span class="badge bg-success">Express</span>
                                        </c:when>
                                        <c:when test="${voyage.trajet.type == 'DIRECT'}">
                                            <span class="badge bg-info">Direct</span>
                                        </c:when>
                                        <c:otherwise>
                                            <span class="badge bg-secondary">Normal</span>
                                        </c:otherwise>
                                    </c:choose>
                                </p>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">Places disponibles</small>
                                <p class="fw-bold">${voyage.placesDisponibles}</p>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">Statut</small>
                                <p class="fw-bold">
                                    <span class="badge bg-success">Programmé</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sélection de la classe -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chair"></i> Sélection de la classe</h5>
                    </div>
                    <div class="card-body">
                        <form id="selectionForm" method="post" action="${pageContext.request.contextPath}/selection/select-voyage">
                            <input type="hidden" name="voyageId" value="${voyage.id}">
                            
                            <div class="row">
                                <c:forEach var="classe" items="${classes}">
                                    <div class="col-md-4 mb-3">
                                        <div class="class-option p-3 text-center" data-classe="${classe}">
                                            <input type="radio" name="classe" value="${classe}" class="d-none" required>
                                            <h6 class="mb-2">
                                                <c:choose>
                                                    <c:when test="${classe == 'PREMIERE'}">
                                                        <i class="fas fa-crown text-warning"></i> 1ère Classe
                                                    </c:when>
                                                    <c:when test="${classe == 'DEUXIEME'}">
                                                        <i class="fas fa-star text-info"></i> 2ème Classe
                                                    </c:when>
                                                    <c:otherwise>
                                                        <i class="fas fa-chair text-secondary"></i> Économique
                                                    </c:otherwise>
                                                </c:choose>
                                            </h6>
                                            
                                            <div class="price-highlight">
                                                <c:choose>
                                                    <c:when test="${classe == 'PREMIERE'}">
                                                        <fmt:formatNumber value="${voyage.trajet.prixBase * 1.5}" pattern="#,##0.00"/> TND
                                                    </c:when>
                                                    <c:when test="${classe == 'DEUXIEME'}">
                                                        <fmt:formatNumber value="${voyage.trajet.prixBase * 1.2}" pattern="#,##0.00"/> TND
                                                    </c:when>
                                                    <c:otherwise>
                                                        <fmt:formatNumber value="${voyage.trajet.prixBase}" pattern="#,##0.00"/> TND
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                            
                                            <small class="text-muted">
                                                Places disponibles: 
                                                <c:choose>
                                                    <c:when test="${classe == 'PREMIERE'}">${voyage.placesPremiereClasse}</c:when>
                                                    <c:when test="${classe == 'DEUXIEME'}">${voyage.placesDeuxiemeClasse}</c:when>
                                                    <c:otherwise>${voyage.placesEconomique}</c:otherwise>
                                                </c:choose>
                                            </small>
                                            
                                            <div class="mt-2">
                                                <c:choose>
                                                    <c:when test="${classe == 'PREMIERE'}">
                                                        <small class="text-muted">
                                                            <i class="fas fa-wifi"></i> WiFi gratuit<br>
                                                            <i class="fas fa-utensils"></i> Repas inclus<br>
                                                            <i class="fas fa-couch"></i> Sièges larges
                                                        </small>
                                                    </c:when>
                                                    <c:when test="${classe == 'DEUXIEME'}">
                                                        <small class="text-muted">
                                                            <i class="fas fa-wifi"></i> WiFi disponible<br>
                                                            <i class="fas fa-coffee"></i> Collations<br>
                                                            <i class="fas fa-chair"></i> Sièges confortables
                                                        </small>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <small class="text-muted">
                                                            <i class="fas fa-chair"></i> Sièges standards<br>
                                                            <i class="fas fa-shopping-cart"></i> Bar à bord<br>
                                                            <i class="fas fa-dollar-sign"></i> Prix économique
                                                        </small>
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>
                                    </div>
                                </c:forEach>
                            </div>

                            <!-- Nombre de passagers -->
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <label for="nombrePassagers" class="form-label">Nombre de passagers</label>
                                    <select name="nombrePassagers" id="nombrePassagers" class="form-select" required>
                                        <option value="1" selected>1 passager</option>
                                        <option value="2">2 passagers</option>
                                        <option value="3">3 passagers</option>
                                        <option value="4">4 passagers</option>
                                        <option value="5">5 passagers</option>
                                        <option value="6">6 passagers</option>
                                    </select>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between mt-4">
                                <a href="${pageContext.request.contextPath}/recherche/search" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left"></i> Retour à la recherche
                                </a>
                                <button type="submit" class="btn btn-primary" disabled id="continueBtn">
                                    Continuer <i class="fas fa-arrow-right"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Résumé de sélection -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-clipboard-list"></i> Résumé de votre sélection</h6>
                    </div>
                    <div class="card-body">
                        <div id="selectionSummary">
                            <p class="text-muted">Sélectionnez une classe pour voir le résumé</p>
                        </div>
                    </div>
                </div>

                <!-- Aide -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-question-circle"></i> Besoin d'aide ?</h6>
                    </div>
                    <div class="card-body">
                        <p class="small">
                            <strong>Classes disponibles :</strong><br>
                            • <strong>1ère Classe :</strong> Maximum de confort<br>
                            • <strong>2ème Classe :</strong> Bon rapport qualité/prix<br>
                            • <strong>Économique :</strong> Prix le plus avantageux
                        </p>
                        <p class="small">
                            <strong>Modification :</strong> Vous pourrez modifier vos préférences à l'étape suivante.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include file="../common/footer.jsp" %>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const classOptions = document.querySelectorAll('.class-option');
            const continueBtn = document.getElementById('continueBtn');
            const selectionSummary = document.getElementById('selectionSummary');
            const nombrePassagersSelect = document.getElementById('nombrePassagers');

            classOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Désélectionner toutes les options
                    classOptions.forEach(opt => {
                        opt.classList.remove('selected');
                        opt.querySelector('input[type="radio"]').checked = false;
                    });

                    // Sélectionner l'option cliquée
                    this.classList.add('selected');
                    this.querySelector('input[type="radio"]').checked = true;

                    // Activer le bouton continuer
                    continueBtn.disabled = false;

                    // Mettre à jour le résumé
                    updateSummary();
                });
            });

            nombrePassagersSelect.addEventListener('change', updateSummary);

            function updateSummary() {
                const selectedClass = document.querySelector('.class-option.selected');
                if (selectedClass) {
                    const classe = selectedClass.dataset.classe;
                    const nombrePassagers = nombrePassagersSelect.value;
                    const priceElement = selectedClass.querySelector('.price-highlight');
                    const price = priceElement.textContent.replace(' TND', '');

                    let className = '';
                    switch(classe) {
                        case 'PREMIERE': className = '1ère Classe'; break;
                        case 'DEUXIEME': className = '2ème Classe'; break;
                        case 'ECONOMIQUE': className = 'Économique'; break;
                    }

                    const totalPrice = parseFloat(price.replace(',', '')) * parseInt(nombrePassagers);

                    selectionSummary.innerHTML = `
                        <div class="mb-2">
                            <strong>Classe :</strong> ${className}
                        </div>
                        <div class="mb-2">
                            <strong>Passagers :</strong> ${nombrePassagers}
                        </div>
                        <div class="mb-2">
                            <strong>Prix unitaire :</strong> ${price} TND
                        </div>
                        <hr>
                        <div class="h6 text-primary">
                            <strong>Total :</strong> ${totalPrice.toFixed(2)} TND
                        </div>
                    `;
                }
            }
        });
    </script>
</body>
</html>
