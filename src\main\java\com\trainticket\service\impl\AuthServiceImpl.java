package com.trainticket.service.impl;

import com.trainticket.dao.UtilisateurDAO;
import com.trainticket.dao.impl.UtilisateurDAOJdbcImpl;
import com.trainticket.model.Utilisateur;
import com.trainticket.service.AuthService;
import com.trainticket.util.PasswordUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.SecureRandom;
import java.util.Optional;
import java.util.regex.Pattern;

public class AuthServiceImpl implements AuthService {

    private static final Logger logger = LoggerFactory.getLogger(AuthServiceImpl.class);
    private final UtilisateurDAO utilisateurDAO;

    // Patterns de validation
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );

    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^(\\+212|0)[5-7][0-9]{8}$"
    );

    public AuthServiceImpl() {
        this.utilisateurDAO = new UtilisateurDAOJdbcImpl();
    }

    public AuthServiceImpl(UtilisateurDAO utilisateurDAO) {
        this.utilisateurDAO = utilisateurDAO;
    }

    @Override
    public Utilisateur authenticate(String email, String motDePasse) throws AuthenticationException {
        if (email == null || email.trim().isEmpty()) {
            throw new AuthenticationException("L'email est requis");
        }

        if (motDePasse == null || motDePasse.trim().isEmpty()) {
            throw new AuthenticationException("Le mot de passe est requis");
        }

        try {
            Optional<Utilisateur> utilisateurOpt = utilisateurDAO.findByEmail(email.trim().toLowerCase());

            if (!utilisateurOpt.isPresent()) {
                logger.warn("Tentative de connexion avec un email inexistant: {}", email);
                throw new AuthenticationException("Email ou mot de passe incorrect");
            }

            Utilisateur utilisateur = utilisateurOpt.get();

            // Vérifier le statut du compte
            if (!isUserActive(utilisateur)) {
                throw new AuthenticationException("Votre compte est bloqué ou suspendu");
            }

            // Vérifier le mot de passe
            if (!PasswordUtil.checkPassword(motDePasse, utilisateur.getMotDePasse())) {
                logger.warn("Tentative de connexion avec un mot de passe incorrect pour: {}", email);
                throw new AuthenticationException("Email ou mot de passe incorrect");
            }

            // Mettre à jour la dernière connexion
            updateLastLogin(utilisateur.getId());

            logger.info("Utilisateur connecté avec succès: {}", email);
            return utilisateur;

        } catch (AuthenticationException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors de l'authentification de l'utilisateur: {}", email, e);
            throw new AuthenticationException("Erreur lors de l'authentification");
        }
    }

    @Override
    public Utilisateur register(Utilisateur utilisateur, String motDePasse) throws RegistrationException {
        try {
            // Valider les données
            validateUser(utilisateur, motDePasse);

            // Vérifier si l'email est disponible
            if (!isEmailAvailable(utilisateur.getEmail())) {
                throw new RegistrationException("Cet email est déjà utilisé");
            }

            // Hacher le mot de passe
            String hashedPassword = PasswordUtil.hashPassword(motDePasse);
            utilisateur.setMotDePasse(hashedPassword);

            // Normaliser l'email
            utilisateur.setEmail(utilisateur.getEmail().trim().toLowerCase());

            // Définir les valeurs par défaut
            if (utilisateur.getRole() == null) {
                utilisateur.setRole(Utilisateur.Role.USER);
            }

            if (utilisateur.getStatut() == null) {
                utilisateur.setStatut(Utilisateur.StatutCompte.ACTIF);
            }

            // Sauvegarder l'utilisateur
            Utilisateur nouvelUtilisateur = utilisateurDAO.save(utilisateur);

            logger.info("Nouvel utilisateur inscrit: {}", utilisateur.getEmail());
            return nouvelUtilisateur;

        } catch (RegistrationException e) {
            throw e;
        } catch (ValidationException e) {
            throw new RegistrationException(e.getMessage());
        } catch (Exception e) {
            logger.error("Erreur lors de l'inscription de l'utilisateur: {}",
                        utilisateur.getEmail(), e);
            throw new RegistrationException("Erreur lors de l'inscription", e);
        }
    }

    @Override
    public void changePassword(Long utilisateurId, String ancienMotDePasse, String nouveauMotDePasse)
            throws PasswordChangeException {
        try {
            Optional<Utilisateur> utilisateurOpt = utilisateurDAO.findById(utilisateurId);

            if (!utilisateurOpt.isPresent()) {
                throw new PasswordChangeException("Utilisateur non trouvé");
            }

            Utilisateur utilisateur = utilisateurOpt.get();

            // Vérifier l'ancien mot de passe
            if (!PasswordUtil.checkPassword(ancienMotDePasse, utilisateur.getMotDePasse())) {
                throw new PasswordChangeException("L'ancien mot de passe est incorrect");
            }

            // Valider le nouveau mot de passe
            String validationMessage = PasswordUtil.getPasswordValidationMessage(nouveauMotDePasse);
            if (validationMessage != null) {
                throw new PasswordChangeException(validationMessage);
            }

            // Hacher et sauvegarder le nouveau mot de passe
            String hashedPassword = PasswordUtil.hashPassword(nouveauMotDePasse);
            utilisateur.setMotDePasse(hashedPassword);
            utilisateurDAO.update(utilisateur);

            logger.info("Mot de passe changé pour l'utilisateur: {}", utilisateur.getEmail());

        } catch (PasswordChangeException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors du changement de mot de passe pour l'utilisateur: {}",
                        utilisateurId, e);
            throw new PasswordChangeException("Erreur lors du changement de mot de passe");
        }
    }

    @Override
    public String resetPassword(String email) throws PasswordResetException {
        try {
            Optional<Utilisateur> utilisateurOpt = utilisateurDAO.findByEmail(email.trim().toLowerCase());

            if (!utilisateurOpt.isPresent()) {
                throw new PasswordResetException("Aucun compte associé à cet email");
            }

            Utilisateur utilisateur = utilisateurOpt.get();

            // Générer un nouveau mot de passe temporaire
            String nouveauMotDePasse = generateTemporaryPassword();

            // Hacher et sauvegarder le nouveau mot de passe
            String hashedPassword = PasswordUtil.hashPassword(nouveauMotDePasse);
            utilisateur.setMotDePasse(hashedPassword);
            utilisateurDAO.update(utilisateur);

            logger.info("Mot de passe réinitialisé pour l'utilisateur: {}", email);

            // Dans un vrai système, on enverrait le mot de passe par email
            // Ici on le retourne pour la démonstration
            return nouveauMotDePasse;

        } catch (PasswordResetException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors de la réinitialisation du mot de passe pour: {}", email, e);
            throw new PasswordResetException("Erreur lors de la réinitialisation du mot de passe");
        }
    }

    @Override
    public void updateLastLogin(Long utilisateurId) {
        try {
            utilisateurDAO.updateLastLogin(utilisateurId);
        } catch (Exception e) {
            logger.error("Erreur lors de la mise à jour de la dernière connexion pour: {}",
                        utilisateurId, e);
        }
    }

    @Override
    public boolean isEmailAvailable(String email) {
        try {
            return !utilisateurDAO.existsByEmail(email.trim().toLowerCase());
        } catch (Exception e) {
            logger.error("Erreur lors de la vérification de disponibilité de l'email: {}", email, e);
            return false;
        }
    }

    @Override
    public void validateUser(Utilisateur utilisateur, String motDePasse) throws ValidationException {
        // Validation de l'email
        if (utilisateur.getEmail() == null || utilisateur.getEmail().trim().isEmpty()) {
            throw new ValidationException("L'email est requis");
        }

        if (!EMAIL_PATTERN.matcher(utilisateur.getEmail().trim()).matches()) {
            throw new ValidationException("Format d'email invalide");
        }

        // Validation du nom
        if (utilisateur.getNom() == null || utilisateur.getNom().trim().isEmpty()) {
            throw new ValidationException("Le nom est requis");
        }

        if (utilisateur.getNom().trim().length() < 2) {
            throw new ValidationException("Le nom doit contenir au moins 2 caractères");
        }

        // Validation du prénom
        if (utilisateur.getPrenom() == null || utilisateur.getPrenom().trim().isEmpty()) {
            throw new ValidationException("Le prénom est requis");
        }

        if (utilisateur.getPrenom().trim().length() < 2) {
            throw new ValidationException("Le prénom doit contenir au moins 2 caractères");
        }

        // Validation du téléphone (optionnel)
        if (utilisateur.getTelephone() != null && !utilisateur.getTelephone().trim().isEmpty()) {
            if (!PHONE_PATTERN.matcher(utilisateur.getTelephone().trim()).matches()) {
                throw new ValidationException("Format de téléphone invalide (format marocain attendu)");
            }
        }

        // Validation du mot de passe
        String passwordValidation = PasswordUtil.getPasswordValidationMessage(motDePasse);
        if (passwordValidation != null) {
            throw new ValidationException(passwordValidation);
        }
    }

    @Override
    public void blockUser(Long utilisateurId) throws UserManagementException {
        try {
            Optional<Utilisateur> utilisateurOpt = utilisateurDAO.findById(utilisateurId);

            if (!utilisateurOpt.isPresent()) {
                throw new UserManagementException("Utilisateur non trouvé");
            }

            Utilisateur utilisateur = utilisateurOpt.get();
            utilisateur.setStatut(Utilisateur.StatutCompte.BLOQUE);
            utilisateurDAO.update(utilisateur);

            logger.info("Utilisateur bloqué: {}", utilisateur.getEmail());

        } catch (UserManagementException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors du blocage de l'utilisateur: {}", utilisateurId, e);
            throw new UserManagementException("Erreur lors du blocage de l'utilisateur", e);
        }
    }

    @Override
    public void unblockUser(Long utilisateurId) throws UserManagementException {
        try {
            Optional<Utilisateur> utilisateurOpt = utilisateurDAO.findById(utilisateurId);

            if (!utilisateurOpt.isPresent()) {
                throw new UserManagementException("Utilisateur non trouvé");
            }

            Utilisateur utilisateur = utilisateurOpt.get();
            utilisateur.setStatut(Utilisateur.StatutCompte.ACTIF);
            utilisateurDAO.update(utilisateur);

            logger.info("Utilisateur débloqué: {}", utilisateur.getEmail());

        } catch (UserManagementException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Erreur lors du déblocage de l'utilisateur: {}", utilisateurId, e);
            throw new UserManagementException("Erreur lors du déblocage de l'utilisateur", e);
        }
    }

    @Override
    public boolean isUserActive(Utilisateur utilisateur) {
        return utilisateur != null && utilisateur.getStatut() == Utilisateur.StatutCompte.ACTIF;
    }

    /**
     * Génère un mot de passe temporaire sécurisé
     * @return mot de passe temporaire
     */
    private String generateTemporaryPassword() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789@#$%";
        SecureRandom random = new SecureRandom();
        StringBuilder password = new StringBuilder();

        for (int i = 0; i < 12; i++) {
            password.append(chars.charAt(random.nextInt(chars.length())));
        }

        return password.toString();
    }
}
