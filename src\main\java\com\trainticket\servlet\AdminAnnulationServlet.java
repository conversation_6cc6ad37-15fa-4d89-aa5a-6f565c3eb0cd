package com.trainticket.servlet;

import com.trainticket.model.DemandeAnnulation;
import com.trainticket.model.Utilisateur;
import com.trainticket.service.AnnulationService;
import com.trainticket.service.impl.AnnulationServiceImpl;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

@WebServlet("/admin/annulations/*")
public class AdminAnnulationServlet extends HttpServlet {

    private final AnnulationService annulationService;

    public AdminAnnulationServlet() {
        this.annulationService = new AnnulationServiceImpl();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("utilisateur") == null) {
            response.sendRedirect(request.getContextPath() + "/auth/login");
            return;
        }

        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");
        if (utilisateur.getRole() != Utilisateur.Role.ADMIN) {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès réservé aux administrateurs");
            return;
        }

        String pathInfo = request.getPathInfo();
        if (pathInfo == null) {
            pathInfo = "/list";
        }

        try {
            switch (pathInfo) {
                case "/list":
                    showDemandesList(request, response);
                    break;
                case "/details":
                    showDemandeDetails(request, response);
                    break;
                default:
                    response.sendError(HttpServletResponse.SC_NOT_FOUND);
            }
        } catch (Exception e) {
            System.err.println("Erreur dans AdminAnnulationServlet: " + e.getMessage());
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("utilisateur") == null) {
            response.sendRedirect(request.getContextPath() + "/auth/login");
            return;
        }

        Utilisateur admin = (Utilisateur) session.getAttribute("utilisateur");
        if (admin.getRole() != Utilisateur.Role.ADMIN) {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès réservé aux administrateurs");
            return;
        }

        String pathInfo = request.getPathInfo();
        if (pathInfo == null) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST);
            return;
        }

        try {
            switch (pathInfo) {
                case "/approve":
                    approuverDemande(request, response, admin);
                    break;
                case "/reject":
                    rejeterDemande(request, response, admin);
                    break;
                default:
                    response.sendError(HttpServletResponse.SC_NOT_FOUND);
            }
        } catch (Exception e) {
            System.err.println("Erreur dans AdminAnnulationServlet POST: " + e.getMessage());
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    private void showDemandesList(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String statutFilter = request.getParameter("statut");
        List<DemandeAnnulation> demandes;
        
        if (statutFilter != null && !statutFilter.isEmpty()) {
            DemandeAnnulation.StatutDemande statut = DemandeAnnulation.StatutDemande.valueOf(statutFilter);
            demandes = annulationService.getDemandesEnAttente(); // Vous pouvez étendre pour filtrer par statut
        } else {
            demandes = annulationService.getDemandesEnAttente();
        }

        request.setAttribute("demandes", demandes);
        request.setAttribute("statutFilter", statutFilter);
        request.getRequestDispatcher("/WEB-INF/views/admin/annulations.jsp").forward(request, response);
    }

    private void showDemandeDetails(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String demandeIdStr = request.getParameter("id");
        if (demandeIdStr == null) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID de la demande manquant");
            return;
        }

        try {
            Long demandeId = Long.parseLong(demandeIdStr);
            Optional<DemandeAnnulation> optionalDemande = annulationService.getDemandeById(demandeId);
            
            if (!optionalDemande.isPresent()) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "Demande non trouvée");
                return;
            }

            request.setAttribute("demande", optionalDemande.get());
            request.getRequestDispatcher("/WEB-INF/views/admin/annulation-details.jsp").forward(request, response);

        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID de la demande invalide");
        }
    }

    private void approuverDemande(HttpServletRequest request, HttpServletResponse response, Utilisateur admin)
            throws ServletException, IOException {
        
        String demandeIdStr = request.getParameter("demandeId");
        String commentaire = request.getParameter("commentaire");

        if (demandeIdStr == null) {
            request.setAttribute("error", "ID de la demande manquant");
            showDemandesList(request, response);
            return;
        }

        try {
            Long demandeId = Long.parseLong(demandeIdStr);
            annulationService.approuverDemande(demandeId, admin, commentaire);
            
            request.setAttribute("success", "Demande d'annulation approuvée avec succès");
            showDemandesList(request, response);

        } catch (NumberFormatException e) {
            request.setAttribute("error", "ID de la demande invalide");
            showDemandesList(request, response);
        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors de l'approbation: " + e.getMessage());
            showDemandesList(request, response);
        }
    }

    private void rejeterDemande(HttpServletRequest request, HttpServletResponse response, Utilisateur admin)
            throws ServletException, IOException {
        
        String demandeIdStr = request.getParameter("demandeId");
        String commentaire = request.getParameter("commentaire");

        if (demandeIdStr == null || commentaire == null || commentaire.trim().isEmpty()) {
            request.setAttribute("error", "ID de la demande et commentaire obligatoires");
            showDemandesList(request, response);
            return;
        }

        try {
            Long demandeId = Long.parseLong(demandeIdStr);
            annulationService.rejeterDemande(demandeId, admin, commentaire);
            
            request.setAttribute("success", "Demande d'annulation rejetée");
            showDemandesList(request, response);

        } catch (NumberFormatException e) {
            request.setAttribute("error", "ID de la demande invalide");
            showDemandesList(request, response);
        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du rejet: " + e.getMessage());
            showDemandesList(request, response);
        }
    }
}
