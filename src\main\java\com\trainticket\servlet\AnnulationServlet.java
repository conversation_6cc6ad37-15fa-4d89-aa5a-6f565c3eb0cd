package com.trainticket.servlet;

import com.trainticket.dao.BilletDAO;
import com.trainticket.dao.impl.BilletDAOImpl;
import com.trainticket.model.Billet;
import com.trainticket.model.DemandeAnnulation;
import com.trainticket.model.Utilisateur;
import com.trainticket.service.AnnulationService;
import com.trainticket.service.impl.AnnulationServiceImpl;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

@WebServlet("/user/annulation/*")
public class AnnulationServlet extends HttpServlet {

    private final AnnulationService annulationService;
    private final BilletDAO billetDAO;

    public AnnulationServlet() {
        this.annulationService = new AnnulationServiceImpl();
        this.billetDAO = new BilletDAOImpl();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("utilisateur") == null) {
            response.sendRedirect(request.getContextPath() + "/auth/login");
            return;
        }

        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");
        String pathInfo = request.getPathInfo();

        if (pathInfo == null) {
            pathInfo = "/list";
        }

        try {
            switch (pathInfo) {
                case "/list":
                    showDemandesUtilisateur(request, response, utilisateur);
                    break;
                case "/form":
                    showFormulaireDemande(request, response, utilisateur);
                    break;
                default:
                    response.sendError(HttpServletResponse.SC_NOT_FOUND);
            }
        } catch (Exception e) {
            System.err.println("Erreur dans AnnulationServlet: " + e.getMessage());
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("utilisateur") == null) {
            response.sendRedirect(request.getContextPath() + "/auth/login");
            return;
        }

        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");
        String pathInfo = request.getPathInfo();

        if (pathInfo == null) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST);
            return;
        }

        try {
            switch (pathInfo) {
                case "/create":
                    creerDemande(request, response, utilisateur);
                    break;
                default:
                    response.sendError(HttpServletResponse.SC_NOT_FOUND);
            }
        } catch (Exception e) {
            System.err.println("Erreur dans AnnulationServlet POST: " + e.getMessage());
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    private void showDemandesUtilisateur(HttpServletRequest request, HttpServletResponse response, 
                                       Utilisateur utilisateur) throws ServletException, IOException {
        List<DemandeAnnulation> demandes = annulationService.getDemandesUtilisateur(utilisateur);
        request.setAttribute("demandes", demandes);
        request.getRequestDispatcher("/WEB-INF/views/user/annulations.jsp").forward(request, response);
    }

    private void showFormulaireDemande(HttpServletRequest request, HttpServletResponse response, 
                                     Utilisateur utilisateur) throws ServletException, IOException {
        String billetIdStr = request.getParameter("billetId");
        
        if (billetIdStr == null) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID du billet manquant");
            return;
        }

        try {
            Long billetId = Long.parseLong(billetIdStr);
            Optional<Billet> optionalBillet = billetDAO.findById(billetId);
            
            if (!optionalBillet.isPresent()) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "Billet non trouvé");
                return;
            }

            Billet billet = optionalBillet.get();

            // Vérifier que le billet appartient à l'utilisateur
            if (!billet.getUtilisateur().getId().equals(utilisateur.getId())) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès non autorisé");
                return;
            }

            // Vérifier que le billet peut être annulé
            if (!annulationService.peutEtreAnnule(billet)) {
                request.setAttribute("error", "Ce billet ne peut plus être annulé");
                request.getRequestDispatcher("/WEB-INF/views/error/error.jsp").forward(request, response);
                return;
            }

            // Vérifier qu'il n'y a pas déjà une demande
            if (annulationService.demandeExistePourBillet(billet)) {
                request.setAttribute("error", "Une demande d'annulation existe déjà pour ce billet");
                request.getRequestDispatcher("/WEB-INF/views/error/error.jsp").forward(request, response);
                return;
            }

            request.setAttribute("billet", billet);
            request.getRequestDispatcher("/WEB-INF/views/user/annulation-form.jsp").forward(request, response);

        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID du billet invalide");
        }
    }

    private void creerDemande(HttpServletRequest request, HttpServletResponse response, 
                            Utilisateur utilisateur) throws ServletException, IOException {
        String billetIdStr = request.getParameter("billetId");
        String motif = request.getParameter("motif");

        if (billetIdStr == null || motif == null || motif.trim().isEmpty()) {
            request.setAttribute("error", "Tous les champs sont obligatoires");
            request.getRequestDispatcher("/WEB-INF/views/user/annulation-form.jsp").forward(request, response);
            return;
        }

        try {
            Long billetId = Long.parseLong(billetIdStr);
            Optional<Billet> optionalBillet = billetDAO.findById(billetId);
            
            if (!optionalBillet.isPresent()) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "Billet non trouvé");
                return;
            }

            Billet billet = optionalBillet.get();

            // Créer la demande d'annulation
            DemandeAnnulation demande = annulationService.creerDemandeAnnulation(billet, utilisateur, motif);

            request.setAttribute("success", "Votre demande d'annulation a été soumise avec succès. " +
                               "Vous recevrez une réponse sous 48h.");
            request.setAttribute("demande", demande);
            request.getRequestDispatcher("/WEB-INF/views/user/annulation-success.jsp").forward(request, response);

        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID du billet invalide");
        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors de la création de la demande: " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/user/annulation-form.jsp").forward(request, response);
        }
    }
}
