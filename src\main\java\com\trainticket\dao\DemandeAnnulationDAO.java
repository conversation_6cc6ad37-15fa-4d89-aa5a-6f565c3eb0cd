package com.trainticket.dao;

import com.trainticket.model.DemandeAnnulation;
import com.trainticket.model.Utilisateur;
import com.trainticket.model.Billet;

import java.util.List;
import java.util.Optional;

/**
 * DAO pour la gestion des demandes d'annulation
 */
public interface DemandeAnnulationDAO extends BaseDAO<DemandeAnnulation, Long> {
    
    /**
     * Trouve toutes les demandes d'annulation par statut
     * @param statut le statut recherché
     * @return liste des demandes
     */
    List<DemandeAnnulation> findByStatut(DemandeAnnulation.StatutDemande statut);
    
    /**
     * Trouve toutes les demandes d'annulation d'un utilisateur
     * @param utilisateur l'utilisateur
     * @return liste des demandes
     */
    List<DemandeAnnulation> findByUtilisateur(Utilisateur utilisateur);
    
    /**
     * Trouve une demande d'annulation par billet
     * @param billet le billet
     * @return Optional contenant la demande si trouvée
     */
    Optional<DemandeAnnulation> findByBillet(Billet billet);
    
    /**
     * Trouve toutes les demandes en attente
     * @return liste des demandes en attente
     */
    List<DemandeAnnulation> findDemandesEnAttente();
    
    /**
     * Trouve toutes les demandes traitées par un admin
     * @param admin l'administrateur
     * @return liste des demandes
     */
    List<DemandeAnnulation> findByAdminTraitant(Utilisateur admin);
    
    /**
     * Compte le nombre de demandes par statut
     * @param statut le statut
     * @return nombre de demandes
     */
    long countByStatut(DemandeAnnulation.StatutDemande statut);
}
