-- <PERSON><PERSON><PERSON> de mise à jour pour remplacer les données par des données tunisiennes
-- À exécuter sur la base de données existante

USE train_ticket_db;

-- Supprimer les données existantes (dans l'ordre des dépendances)
DELETE FROM billets;
DELETE FROM voyages;
DELETE FROM trajets;
DELETE FROM gares;

-- Réinitialiser les auto-increment
ALTER TABLE gares AUTO_INCREMENT = 1;
ALTER TABLE trajets AUTO_INCREMENT = 1;
ALTER TABLE voyages AUTO_INCREMENT = 1;
ALTER TABLE billets AUTO_INCREMENT = 1;

-- Insertion des gares tunisiennes
INSERT INTO gares (nom, ville, code_gare, adresse, latitude, longitude, active) VALUES
('Gare Centrale de Tunis', 'Tunis', 'TUN', 'Place Barcelone, Tunis', 36.8008, 10.1817, TRUE),
('Gare de S<PERSON>x', '<PERSON>fax', 'SFX', 'Avenue Habib Bourguiba, <PERSON>fax', 34.7406, 10.7603, TRUE),
('Gare de Sousse', 'Sousse', 'SOU', 'Boulevard Hassouna Ayachi, Sousse', 35.8256, 10.6369, TRUE),
('Gare de Bizerte', 'Bizerte', 'BIZ', 'Avenue Habib Bourguiba, Bizerte', 37.2744, 9.8739, TRUE),
('Gare de Gabès', 'Gabès', 'GAB', 'Avenue Farhat Hached, Gabès', 33.8815, 10.0982, TRUE),
('Gare de Monastir', 'Monastir', 'MON', 'Avenue de la République, Monastir', 35.7643, 10.8113, TRUE),
('Gare de Kairouan', 'Kairouan', 'KAI', 'Avenue de la République, Kairouan', 35.6781, 10.0963, TRUE),
('Gare de Mahdia', 'Mahdia', 'MAH', 'Avenue 7 Novembre, Mahdia', 35.5047, 11.0622, TRUE),
('Gare de Nabeul', 'Nabeul', 'NAB', 'Avenue Habib Bourguiba, Nabeul', 36.4561, 10.7376, TRUE),
('Gare de Gafsa', 'Gafsa', 'GAF', 'Avenue Habib Bourguiba, Gafsa', 34.4250, 8.7842, TRUE),
('Gare de Béja', 'Béja', 'BEJ', 'Avenue de la Liberté, Béja', 36.7256, 9.1817, TRUE),
('Gare de Jendouba', 'Jendouba', 'JEN', 'Avenue Habib Bourguiba, Jendouba', 36.5014, 8.7800, TRUE);

-- Insertion des trajets tunisiens
INSERT INTO trajets (numero_trajet, gare_depart_id, gare_arrivee_id, duree_minutes, prix_base, capacite_total, type, description) VALUES
-- Trajets principaux Nord-Sud
('TN001', 1, 2, 180, 25.50, 200, 'EXPRESS', 'Express Tunis - Sfax'),
('TN002', 2, 1, 180, 25.50, 200, 'EXPRESS', 'Express Sfax - Tunis'),
('TN003', 1, 3, 120, 18.00, 180, 'DIRECT', 'Train direct Tunis - Sousse'),
('TN004', 3, 1, 120, 18.00, 180, 'DIRECT', 'Train direct Sousse - Tunis'),
('TN005', 1, 5, 240, 32.00, 160, 'NORMAL', 'Train Tunis - Gabès'),
('TN006', 5, 1, 240, 32.00, 160, 'NORMAL', 'Train Gabès - Tunis'),

-- Trajets côtiers
('TN101', 3, 2, 90, 15.00, 150, 'NORMAL', 'Train Sousse - Sfax'),
('TN102', 2, 3, 90, 15.00, 150, 'NORMAL', 'Train Sfax - Sousse'),
('TN103', 3, 6, 45, 8.50, 140, 'NORMAL', 'Train Sousse - Monastir'),
('TN104', 6, 3, 45, 8.50, 140, 'NORMAL', 'Train Monastir - Sousse'),
('TN105', 6, 8, 60, 12.00, 120, 'NORMAL', 'Train Monastir - Mahdia'),
('TN106', 8, 6, 60, 12.00, 120, 'NORMAL', 'Train Mahdia - Monastir'),

-- Trajets vers le Nord
('TN201', 1, 4, 90, 14.00, 160, 'NORMAL', 'Train Tunis - Bizerte'),
('TN202', 4, 1, 90, 14.00, 160, 'NORMAL', 'Train Bizerte - Tunis'),
('TN203', 1, 9, 75, 12.50, 140, 'DIRECT', 'Train direct Tunis - Nabeul'),
('TN204', 9, 1, 75, 12.50, 140, 'DIRECT', 'Train direct Nabeul - Tunis'),

-- Trajets vers l'intérieur
('TN301', 1, 7, 150, 22.00, 140, 'NORMAL', 'Train Tunis - Kairouan'),
('TN302', 7, 1, 150, 22.00, 140, 'NORMAL', 'Train Kairouan - Tunis'),
('TN303', 2, 10, 120, 18.50, 120, 'NORMAL', 'Train Sfax - Gafsa'),
('TN304', 10, 2, 120, 18.50, 120, 'NORMAL', 'Train Gafsa - Sfax'),

-- Trajets vers l'Ouest
('TN401', 1, 11, 180, 26.00, 130, 'NORMAL', 'Train Tunis - Béja'),
('TN402', 11, 1, 180, 26.00, 130, 'NORMAL', 'Train Béja - Tunis'),
('TN403', 11, 12, 90, 15.50, 110, 'NORMAL', 'Train Béja - Jendouba'),
('TN404', 12, 11, 90, 15.50, 110, 'NORMAL', 'Train Jendouba - Béja'),

-- Trajets régionaux Sud
('TN501', 2, 5, 90, 16.00, 120, 'NORMAL', 'Train Sfax - Gabès'),
('TN502', 5, 2, 90, 16.00, 120, 'NORMAL', 'Train Gabès - Sfax'),
('TN503', 5, 10, 150, 24.00, 100, 'NORMAL', 'Train Gabès - Gafsa'),
('TN504', 10, 5, 150, 24.00, 100, 'NORMAL', 'Train Gafsa - Gabès');

-- Insertion de voyages pour les prochains jours
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES
-- Express Tunis - Sfax (TN001) - Aujourd'hui et demain
(1, '2025-05-27 06:00:00', '2025-05-27 09:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(1, '2025-05-27 10:00:00', '2025-05-27 13:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(1, '2025-05-27 15:00:00', '2025-05-27 18:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(1, '2025-05-28 06:00:00', '2025-05-28 09:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(1, '2025-05-28 10:00:00', '2025-05-28 13:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(1, '2025-05-28 15:00:00', '2025-05-28 18:00:00', 200, 20, 60, 120, 'PROGRAMME'),

-- Express Sfax - Tunis (TN002)
(2, '2025-05-27 07:30:00', '2025-05-27 10:30:00', 200, 20, 60, 120, 'PROGRAMME'),
(2, '2025-05-27 14:00:00', '2025-05-27 17:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(2, '2025-05-27 19:00:00', '2025-05-27 22:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(2, '2025-05-28 07:30:00', '2025-05-28 10:30:00', 200, 20, 60, 120, 'PROGRAMME'),
(2, '2025-05-28 14:00:00', '2025-05-28 17:00:00', 200, 20, 60, 120, 'PROGRAMME'),
(2, '2025-05-28 19:00:00', '2025-05-28 22:00:00', 200, 20, 60, 120, 'PROGRAMME'),

-- Direct Tunis - Sousse (TN003)
(3, '2025-05-27 08:00:00', '2025-05-27 10:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-05-27 12:00:00', '2025-05-27 14:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-05-27 16:30:00', '2025-05-27 18:30:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-05-28 08:00:00', '2025-05-28 10:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-05-28 12:00:00', '2025-05-28 14:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(3, '2025-05-28 16:30:00', '2025-05-28 18:30:00', 180, 18, 54, 108, 'PROGRAMME'),

-- Direct Sousse - Tunis (TN004)
(4, '2025-05-27 09:15:00', '2025-05-27 11:15:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-05-27 13:45:00', '2025-05-27 15:45:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-05-27 18:00:00', '2025-05-27 20:00:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-05-28 09:15:00', '2025-05-28 11:15:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-05-28 13:45:00', '2025-05-28 15:45:00', 180, 18, 54, 108, 'PROGRAMME'),
(4, '2025-05-28 18:00:00', '2025-05-28 20:00:00', 180, 18, 54, 108, 'PROGRAMME'),

-- Tunis - Bizerte (TN201)
(13, '2025-05-27 07:00:00', '2025-05-27 08:30:00', 160, 16, 48, 96, 'PROGRAMME'),
(13, '2025-05-27 17:30:00', '2025-05-27 19:00:00', 160, 16, 48, 96, 'PROGRAMME'),
(13, '2025-05-28 07:00:00', '2025-05-28 08:30:00', 160, 16, 48, 96, 'PROGRAMME'),
(13, '2025-05-28 17:30:00', '2025-05-28 19:00:00', 160, 16, 48, 96, 'PROGRAMME'),

-- Tunis - Nabeul (TN203)
(15, '2025-05-27 08:30:00', '2025-05-27 09:45:00', 140, 14, 42, 84, 'PROGRAMME'),
(15, '2025-05-27 16:00:00', '2025-05-27 17:15:00', 140, 14, 42, 84, 'PROGRAMME'),
(15, '2025-05-28 08:30:00', '2025-05-28 09:45:00', 140, 14, 42, 84, 'PROGRAMME'),
(15, '2025-05-28 16:00:00', '2025-05-28 17:15:00', 140, 14, 42, 84, 'PROGRAMME'),

-- Sousse - Monastir (TN103)
(9, '2025-05-27 09:00:00', '2025-05-27 09:45:00', 140, 14, 42, 84, 'PROGRAMME'),
(9, '2025-05-27 14:30:00', '2025-05-27 15:15:00', 140, 14, 42, 84, 'PROGRAMME'),
(9, '2025-05-27 18:45:00', '2025-05-27 19:30:00', 140, 14, 42, 84, 'PROGRAMME'),
(9, '2025-05-28 09:00:00', '2025-05-28 09:45:00', 140, 14, 42, 84, 'PROGRAMME'),
(9, '2025-05-28 14:30:00', '2025-05-28 15:15:00', 140, 14, 42, 84, 'PROGRAMME'),
(9, '2025-05-28 18:45:00', '2025-05-28 19:30:00', 140, 14, 42, 84, 'PROGRAMME');

-- Affichage des résultats
SELECT 'Gares tunisiennes ajoutées:' as message;
SELECT id, nom, ville, code_gare FROM gares ORDER BY id;

SELECT 'Trajets tunisiens ajoutés:' as message;
SELECT id, numero_trajet, 
       (SELECT CONCAT(ville, ' (', nom, ')') FROM gares WHERE id = gare_depart_id) as depart,
       (SELECT CONCAT(ville, ' (', nom, ')') FROM gares WHERE id = gare_arrivee_id) as arrivee,
       CONCAT(duree_minutes, ' min') as duree,
       CONCAT(prix_base, ' TND') as prix,
       type
FROM trajets ORDER BY numero_trajet;

SELECT 'Voyages programmés:' as message;
SELECT COUNT(*) as nombre_voyages FROM voyages WHERE statut = 'PROGRAMME';
