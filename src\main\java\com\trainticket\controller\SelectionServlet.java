package com.trainticket.controller;

import com.trainticket.model.*;
import com.trainticket.service.RechercheService;
import com.trainticket.service.impl.RechercheServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

/**
 * Servlet pour gérer la sélection des préférences et options de voyage
 */
public class SelectionServlet extends HttpServlet {

    private static final Logger logger = LoggerFactory.getLogger(SelectionServlet.class);
    private RechercheService rechercheService;

    @Override
    public void init() throws ServletException {
        super.init();
        this.rechercheService = new RechercheServiceImpl();
        logger.info("SelectionServlet initialisé");
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String pathInfo = request.getPathInfo();

        if (pathInfo == null) {
            response.sendRedirect(request.getContextPath() + "/recherche/search");
            return;
        }

        switch (pathInfo) {
            case "/voyage":
                showVoyageSelection(request, response);
                break;
            case "/preferences":
                showPreferencesSelection(request, response);
                break;
            case "/return-trip":
                showReturnTripSelection(request, response);
                break;
            case "/summary":
                showBookingSummary(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String pathInfo = request.getPathInfo();

        if (pathInfo == null) {
            response.sendRedirect(request.getContextPath() + "/recherche/search");
            return;
        }

        switch (pathInfo) {
            case "/select-voyage":
                handleVoyageSelection(request, response);
                break;
            case "/select-preferences":
                handlePreferencesSelection(request, response);
                break;
            case "/select-return":
                handleReturnTripSelection(request, response);
                break;
            case "/confirm-booking":
                handleBookingConfirmation(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    /**
     * Affiche la page de sélection d'un voyage spécifique
     */
    private void showVoyageSelection(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String voyageIdStr = request.getParameter("id");
        if (voyageIdStr == null) {
            response.sendRedirect(request.getContextPath() + "/recherche/search");
            return;
        }

        try {
            Long voyageId = Long.parseLong(voyageIdStr);
            Voyage voyage = rechercheService.getVoyageDetails(voyageId);

            if (voyage == null) {
                request.setAttribute("errorMessage", "Voyage non trouvé");
                response.sendRedirect(request.getContextPath() + "/recherche/search");
                return;
            }

            // Vérifier l'authentification pour la réservation
            HttpSession session = request.getSession(false);
            boolean isAuthenticated = (session != null && session.getAttribute("utilisateur") != null);

            if (!isAuthenticated) {
                // Sauvegarder les paramètres pour redirection après connexion
                session = request.getSession(true);
                session.setAttribute("redirectAfterLogin",
                    request.getContextPath() + "/selection/voyage?id=" + voyageId);
                response.sendRedirect(request.getContextPath() + "/auth/login");
                return;
            }

            request.setAttribute("voyage", voyage);
            request.setAttribute("classes", Voyage.ClasseBillet.values());

            request.getRequestDispatcher("/WEB-INF/views/selection/voyage-selection.jsp")
                   .forward(request, response);

        } catch (NumberFormatException e) {
            logger.warn("ID de voyage invalide: {}", voyageIdStr);
            response.sendRedirect(request.getContextPath() + "/recherche/search");
        } catch (Exception e) {
            logger.error("Erreur lors de la sélection du voyage", e);
            request.setAttribute("errorMessage", "Erreur lors du chargement du voyage");
            request.getRequestDispatcher("/WEB-INF/views/error/500.jsp").forward(request, response);
        }
    }

    /**
     * Affiche la page de sélection des préférences
     */
    private void showPreferencesSelection(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("selectedVoyage") == null) {
            response.sendRedirect(request.getContextPath() + "/recherche/search");
            return;
        }

        request.getRequestDispatcher("/WEB-INF/views/selection/preferences-selection.jsp")
               .forward(request, response);
    }

    /**
     * Affiche la page de sélection du voyage de retour
     */
    private void showReturnTripSelection(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("selectedVoyage") == null) {
            response.sendRedirect(request.getContextPath() + "/recherche/search");
            return;
        }

        try {
            // Récupérer le voyage sélectionné pour proposer le retour
            Voyage voyageAller = (Voyage) session.getAttribute("selectedVoyage");
            String villeDepart = voyageAller.getTrajet().getGareArrivee().getVille();
            String villeArrivee = voyageAller.getTrajet().getGareDepart().getVille();

            // Proposer des dates de retour (à partir du lendemain)
            LocalDate dateRetourMin = voyageAller.getDateHeureDepart().toLocalDate().plusDays(1);

            // Rechercher les voyages de retour disponibles
            List<Voyage> voyagesRetour = rechercheService.rechercherVoyagesDirects(
                villeDepart, villeArrivee, dateRetourMin);

            request.setAttribute("voyageAller", voyageAller);
            request.setAttribute("voyagesRetour", voyagesRetour);
            request.setAttribute("villeDepart", villeDepart);
            request.setAttribute("villeArrivee", villeArrivee);
            request.setAttribute("dateRetourMin", dateRetourMin);

            request.getRequestDispatcher("/WEB-INF/views/selection/return-trip-selection.jsp")
                   .forward(request, response);

        } catch (Exception e) {
            logger.error("Erreur lors de la proposition de voyage de retour", e);
            request.setAttribute("errorMessage", "Erreur lors du chargement des voyages de retour");
            request.getRequestDispatcher("/WEB-INF/views/error/500.jsp").forward(request, response);
        }
    }

    /**
     * Affiche le résumé de la réservation
     */
    private void showBookingSummary(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("selectedVoyage") == null) {
            response.sendRedirect(request.getContextPath() + "/recherche/search");
            return;
        }

        request.getRequestDispatcher("/WEB-INF/views/selection/booking-summary.jsp")
               .forward(request, response);
    }

    /**
     * Traite la sélection d'un voyage
     */
    private void handleVoyageSelection(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String voyageIdStr = request.getParameter("voyageId");
        String classeStr = request.getParameter("classe");
        String nombrePassagersStr = request.getParameter("nombrePassagers");

        try {
            Long voyageId = Long.parseLong(voyageIdStr);
            Voyage.ClasseBillet classe = Voyage.ClasseBillet.valueOf(classeStr);
            int nombrePassagers = Integer.parseInt(nombrePassagersStr);

            Voyage voyage = rechercheService.getVoyageDetails(voyageId);

            // Vérifier la disponibilité
            if (!voyage.hasPlacesDisponibles(classe) || !hasEnoughSeats(voyage, classe, nombrePassagers)) {
                request.setAttribute("errorMessage", "Nombre de places insuffisant pour cette classe");
                showVoyageSelection(request, response);
                return;
            }

            // Sauvegarder la sélection en session
            HttpSession session = request.getSession(true);
            session.setAttribute("selectedVoyage", voyage);
            session.setAttribute("selectedClasse", classe);
            session.setAttribute("nombrePassagers", nombrePassagers);

            // Rediriger vers la sélection des préférences
            response.sendRedirect(request.getContextPath() + "/selection/preferences");

        } catch (Exception e) {
            logger.error("Erreur lors de la sélection du voyage", e);
            request.setAttribute("errorMessage", "Erreur lors de la sélection");
            showVoyageSelection(request, response);
        }
    }

    /**
     * Traite la sélection des préférences
     */
    private void handlePreferencesSelection(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            // Récupérer les préférences du formulaire
            Preference preferences = new Preference();

            String preferenceFenetre = request.getParameter("preferenceFenetre");
            if (preferenceFenetre != null) {
                preferences.setPreferenceFenetre(Preference.PreferenceFenetre.valueOf(preferenceFenetre));
            }

            preferences.setWagonNonFumeur("on".equals(request.getParameter("wagonNonFumeur")));
            preferences.setEspaceFamille("on".equals(request.getParameter("espaceFamille")));
            preferences.setAccesHandicape("on".equals(request.getParameter("accesHandicape")));
            preferences.setWifiRequis("on".equals(request.getParameter("wifiRequis")));
            preferences.setPriseElectrique("on".equals(request.getParameter("priseElectrique")));
            preferences.setCommentairesSpeciaux(request.getParameter("commentairesSpeciaux"));

            // Sauvegarder les préférences en session
            HttpSession session = request.getSession(true);
            session.setAttribute("selectedPreferences", preferences);

            // Proposer un voyage de retour
            response.sendRedirect(request.getContextPath() + "/selection/return-trip");

        } catch (Exception e) {
            logger.error("Erreur lors de la sélection des préférences", e);
            request.setAttribute("errorMessage", "Erreur lors de la sélection des préférences");
            showPreferencesSelection(request, response);
        }
    }

    /**
     * Traite la sélection du voyage de retour
     */
    private void handleReturnTripSelection(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String action = request.getParameter("action");
        HttpSession session = request.getSession(true);

        if ("skip".equals(action)) {
            // L'utilisateur ne veut pas de voyage de retour
            session.removeAttribute("selectedReturnVoyage");
        } else if ("select".equals(action)) {
            // L'utilisateur sélectionne un voyage de retour
            String voyageRetourIdStr = request.getParameter("voyageRetourId");
            String classeRetourStr = request.getParameter("classeRetour");

            try {
                Long voyageRetourId = Long.parseLong(voyageRetourIdStr);
                Voyage.ClasseBillet classeRetour = Voyage.ClasseBillet.valueOf(classeRetourStr);

                Voyage voyageRetour = rechercheService.getVoyageDetails(voyageRetourId);

                session.setAttribute("selectedReturnVoyage", voyageRetour);
                session.setAttribute("selectedReturnClasse", classeRetour);

            } catch (Exception e) {
                logger.error("Erreur lors de la sélection du voyage de retour", e);
                request.setAttribute("errorMessage", "Erreur lors de la sélection du voyage de retour");
                showReturnTripSelection(request, response);
                return;
            }
        }

        // Rediriger vers le résumé
        response.sendRedirect(request.getContextPath() + "/selection/summary");
    }

    /**
     * Traite la confirmation de réservation
     */
    private void handleBookingConfirmation(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("selectedVoyage") == null) {
            response.sendRedirect(request.getContextPath() + "/recherche/search");
            return;
        }

        // Rediriger vers le paiement
        response.sendRedirect(request.getContextPath() + "/paiement/checkout");
    }

    /**
     * Vérifie si le voyage a suffisamment de places pour la classe et le nombre de passagers
     */
    private boolean hasEnoughSeats(Voyage voyage, Voyage.ClasseBillet classe, int nombrePassagers) {
        switch (classe) {
            case PREMIERE:
                return voyage.getPlacesPremiereClasse() >= nombrePassagers;
            case DEUXIEME:
                return voyage.getPlacesDeuxiemeClasse() >= nombrePassagers;
            case ECONOMIQUE:
                return voyage.getPlacesEconomique() >= nombrePassagers;
            default:
                return false;
        }
    }
}
