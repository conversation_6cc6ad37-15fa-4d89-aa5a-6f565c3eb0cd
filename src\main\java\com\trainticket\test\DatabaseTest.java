package com.trainticket.test;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

public class DatabaseTest {
    
    public static void main(String[] args) {
        String url = "********************************************************************************************************";
        String username = "root";
        String password = "123";
        
        try {
            // Test de connexion directe à MySQL
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection connection = DriverManager.getConnection(url, username, password);
            
            System.out.println("✅ Connexion à MySQL réussie !");
            
            // Test de requête simple
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery("SELECT COUNT(*) as count FROM utilisateurs");
            
            if (resultSet.next()) {
                int count = resultSet.getInt("count");
                System.out.println("✅ Nombre d'utilisateurs dans la base : " + count);
            }
            
            // Test d'insertion simple
            String insertSQL = "INSERT INTO utilisateurs (email, mot_de_passe, nom, prenom, role, statut, date_creation) " +
                             "VALUES ('<EMAIL>', 'password123', 'Test', 'User', 'USER', 'ACTIF', NOW())";
            
            int rowsAffected = statement.executeUpdate(insertSQL);
            System.out.println("✅ Insertion réussie, lignes affectées : " + rowsAffected);
            
            // Nettoyage
            statement.executeUpdate("DELETE FROM utilisateurs WHERE email = '<EMAIL>'");
            System.out.println("✅ Nettoyage effectué");
            
            connection.close();
            System.out.println("✅ Test de base de données terminé avec succès !");
            
        } catch (Exception e) {
            System.err.println("❌ Erreur lors du test de base de données :");
            e.printStackTrace();
        }
    }
}
