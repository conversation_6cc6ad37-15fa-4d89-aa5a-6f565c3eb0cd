package com.trainticket.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trainticket.dao.*;
import com.trainticket.dao.impl.*;
import com.trainticket.model.*;
import com.trainticket.service.AuthService;
import com.trainticket.service.impl.AuthServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AdminServlet extends HttpServlet {

    private static final Logger logger = LoggerFactory.getLogger(AdminServlet.class);

    private UtilisateurDAO utilisateurDAO;
    private GareDAO gareDAO;
    private TrajetDAO trajetDAO;
    private VoyageDAO voyageDAO;
    private BilletDAO billetDAO;
    private AuthService authService;
    private ObjectMapper objectMapper;

    @Override
    public void init() throws ServletException {
        super.init();
        this.utilisateurDAO = new UtilisateurDAOImpl();
        this.gareDAO = new GareDAOImpl();
        this.trajetDAO = new TrajetDAOImpl();
        this.voyageDAO = new VoyageDAOImpl();
        this.billetDAO = new BilletDAOImpl();
        this.authService = new AuthServiceImpl();
        this.objectMapper = new ObjectMapper();
        logger.info("AdminServlet initialisé");
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Vérifier l'authentification et les droits admin
        if (!isAdminAuthenticated(request)) {
            response.sendRedirect(request.getContextPath() + "/auth/login");
            return;
        }

        String pathInfo = request.getPathInfo();

        if (pathInfo == null) {
            response.sendRedirect(request.getContextPath() + "/admin/dashboard");
            return;
        }

        switch (pathInfo) {
            case "/dashboard":
                showDashboard(request, response);
                break;
            case "/manage-users":
                showManageUsers(request, response);
                break;
            case "/manage-gares":
                showManageGares(request, response);
                break;
            case "/manage-trajets":
                showManageTrajets(request, response);
                break;
            case "/manage-voyages":
                showManageVoyages(request, response);
                break;
            case "/reports":
                showReports(request, response);
                break;
            case "/settings":
                showSettings(request, response);
                break;
            // API endpoints
            case "/api/stats":
                getStatsAPI(request, response);
                break;
            case "/api/users":
                getUsersAPI(request, response);
                break;
            case "/api/revenue":
                getRevenueAPI(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Vérifier l'authentification et les droits admin
        if (!isAdminAuthenticated(request)) {
            response.sendRedirect(request.getContextPath() + "/auth/login");
            return;
        }

        String pathInfo = request.getPathInfo();

        if (pathInfo == null) {
            response.sendRedirect(request.getContextPath() + "/admin/dashboard");
            return;
        }

        switch (pathInfo) {
            case "/create-gare":
                handleCreateGare(request, response);
                break;
            case "/update-gare":
                handleUpdateGare(request, response);
                break;
            case "/delete-gare":
                handleDeleteGare(request, response);
                break;
            case "/create-trajet":
                handleCreateTrajet(request, response);
                break;
            case "/update-trajet":
                handleUpdateTrajet(request, response);
                break;
            case "/delete-trajet":
                handleDeleteTrajet(request, response);
                break;
            case "/create-voyage":
                handleCreateVoyage(request, response);
                break;
            case "/update-voyage":
                handleUpdateVoyage(request, response);
                break;
            case "/delete-voyage":
                handleDeleteVoyage(request, response);
                break;
            case "/toggle-user-status":
                handleToggleUserStatus(request, response);
                break;
            case "/update-voyage-status":
                handleUpdateVoyageStatus(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    private void showDashboard(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            // Statistiques générales
            long totalUtilisateurs = utilisateurDAO.count();
            long totalGares = gareDAO.count();
            long totalTrajets = trajetDAO.count();
            long totalVoyages = voyageDAO.count();
            long totalBillets = billetDAO.count();

            // Statistiques financières
            Double chiffreAffairesTotal = billetDAO.calculateTotalRevenue();
            Double chiffreAffairesMois = billetDAO.calculateMonthRevenue();
            Double chiffreAffairesJour = billetDAO.calculateTodayRevenue();

            // Statistiques par statut
            long utilisateursActifs = utilisateurDAO.countByStatut(Utilisateur.StatutCompte.ACTIF);
            long voyagesProgrammes = voyageDAO.countByStatut(Voyage.StatutVoyage.PROGRAMME);
            long billetsAchetes = billetDAO.countByStatut(Billet.StatutBillet.ACHETE);

            // Données récentes
            List<Billet> billetsRecents = billetDAO.findRecentBillets(10);
            List<Utilisateur> utilisateursRecents = utilisateurDAO.findRecentUsers(7);

            // Préparer les données pour la vue
            request.setAttribute("totalUtilisateurs", totalUtilisateurs);
            request.setAttribute("totalGares", totalGares);
            request.setAttribute("totalTrajets", totalTrajets);
            request.setAttribute("totalVoyages", totalVoyages);
            request.setAttribute("totalBillets", totalBillets);

            request.setAttribute("chiffreAffairesTotal", chiffreAffairesTotal);
            request.setAttribute("chiffreAffairesMois", chiffreAffairesMois);
            request.setAttribute("chiffreAffairesJour", chiffreAffairesJour);

            request.setAttribute("utilisateursActifs", utilisateursActifs);
            request.setAttribute("voyagesProgrammes", voyagesProgrammes);
            request.setAttribute("billetsAchetes", billetsAchetes);

            request.setAttribute("billetsRecents", billetsRecents);
            request.setAttribute("utilisateursRecents", utilisateursRecents);

            request.getRequestDispatcher("/WEB-INF/views/admin/dashboard.jsp").forward(request, response);

        } catch (Exception e) {
            logger.error("Erreur lors du chargement du dashboard admin", e);
            request.setAttribute("errorMessage", "Erreur lors du chargement du dashboard");
            request.getRequestDispatcher("/WEB-INF/views/admin/dashboard.jsp").forward(request, response);
        }
    }

    private void showManageUsers(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            String searchTerm = request.getParameter("search");
            String roleFilter = request.getParameter("role");
            String statutFilter = request.getParameter("statut");

            List<Utilisateur> utilisateurs;

            if (searchTerm != null || roleFilter != null || statutFilter != null) {
                // Recherche avancée
                Utilisateur.Role role = roleFilter != null && !roleFilter.isEmpty() ?
                    Utilisateur.Role.valueOf(roleFilter) : null;
                Utilisateur.StatutCompte statut = statutFilter != null && !statutFilter.isEmpty() ?
                    Utilisateur.StatutCompte.valueOf(statutFilter) : null;

                utilisateurs = utilisateurDAO.advancedSearch(null, searchTerm, role, statut);
            } else {
                utilisateurs = utilisateurDAO.findAll();
            }

            request.setAttribute("utilisateurs", utilisateurs);
            request.setAttribute("searchTerm", searchTerm);
            request.setAttribute("roleFilter", roleFilter);
            request.setAttribute("statutFilter", statutFilter);

            request.getRequestDispatcher("/WEB-INF/views/admin/manage-users.jsp").forward(request, response);

        } catch (Exception e) {
            logger.error("Erreur lors du chargement de la gestion des utilisateurs", e);
            request.setAttribute("errorMessage", "Erreur lors du chargement");
            request.getRequestDispatcher("/WEB-INF/views/admin/manage-users.jsp").forward(request, response);
        }
    }

    private void showManageGares(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            List<Gare> gares = gareDAO.findAll();
            request.setAttribute("gares", gares);

            request.getRequestDispatcher("/WEB-INF/views/admin/manage-gares.jsp").forward(request, response);

        } catch (Exception e) {
            logger.error("Erreur lors du chargement de la gestion des gares", e);
            request.setAttribute("errorMessage", "Erreur lors du chargement");
            request.getRequestDispatcher("/WEB-INF/views/admin/manage-gares.jsp").forward(request, response);
        }
    }

    private void showManageTrajets(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            List<Trajet> trajets = trajetDAO.findAll();
            List<Gare> gares = gareDAO.findActiveGares();

            request.setAttribute("trajets", trajets);
            request.setAttribute("gares", gares);

            request.getRequestDispatcher("/WEB-INF/views/admin/manage-trajets.jsp").forward(request, response);

        } catch (Exception e) {
            logger.error("Erreur lors du chargement de la gestion des trajets", e);
            request.setAttribute("errorMessage", "Erreur lors du chargement");
            request.getRequestDispatcher("/WEB-INF/views/admin/manage-trajets.jsp").forward(request, response);
        }
    }

    private void showManageVoyages(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            List<Voyage> voyages = voyageDAO.findAll();
            List<Trajet> trajets = trajetDAO.findActiveTrajets();

            request.setAttribute("voyages", voyages);
            request.setAttribute("trajets", trajets);

            request.getRequestDispatcher("/WEB-INF/views/admin/manage-voyages.jsp").forward(request, response);

        } catch (Exception e) {
            logger.error("Erreur lors du chargement de la gestion des voyages", e);
            request.setAttribute("errorMessage", "Erreur lors du chargement");
            request.getRequestDispatcher("/WEB-INF/views/admin/manage-voyages.jsp").forward(request, response);
        }
    }

    private void showReports(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            // Rapports financiers
            Double revenueTotal = billetDAO.calculateTotalRevenue();
            Double revenueMois = billetDAO.calculateMonthRevenue();
            Double revenueJour = billetDAO.calculateTodayRevenue();

            // Statistiques des voyages
            List<Voyage> voyagesPopulaires = voyageDAO.findMostBookedVoyages(10);
            List<Trajet> trajetsPopulaires = trajetDAO.findMostPopularTrajets(10);

            // Statistiques des utilisateurs
            long totalUtilisateurs = utilisateurDAO.count();
            long utilisateursActifs = utilisateurDAO.countByStatut(Utilisateur.StatutCompte.ACTIF);

            request.setAttribute("revenueTotal", revenueTotal);
            request.setAttribute("revenueMois", revenueMois);
            request.setAttribute("revenueJour", revenueJour);
            request.setAttribute("voyagesPopulaires", voyagesPopulaires);
            request.setAttribute("trajetsPopulaires", trajetsPopulaires);
            request.setAttribute("totalUtilisateurs", totalUtilisateurs);
            request.setAttribute("utilisateursActifs", utilisateursActifs);

            request.getRequestDispatcher("/WEB-INF/views/admin/reports.jsp").forward(request, response);

        } catch (Exception e) {
            logger.error("Erreur lors du chargement des rapports", e);
            request.setAttribute("errorMessage", "Erreur lors du chargement des rapports");
            request.getRequestDispatcher("/WEB-INF/views/admin/reports.jsp").forward(request, response);
        }
    }

    private void showSettings(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        request.getRequestDispatcher("/WEB-INF/views/admin/settings.jsp").forward(request, response);
    }

    // Handlers pour les actions CRUD

    private void handleCreateGare(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            String nom = request.getParameter("nom");
            String ville = request.getParameter("ville");
            String codeGare = request.getParameter("codeGare");
            String adresse = request.getParameter("adresse");

            Gare gare = new Gare(nom, ville, codeGare);
            gare.setAdresse(adresse);

            String latitudeStr = request.getParameter("latitude");
            String longitudeStr = request.getParameter("longitude");

            if (latitudeStr != null && !latitudeStr.trim().isEmpty()) {
                gare.setLatitude(Double.parseDouble(latitudeStr));
            }

            if (longitudeStr != null && !longitudeStr.trim().isEmpty()) {
                gare.setLongitude(Double.parseDouble(longitudeStr));
            }

            gareDAO.save(gare);

            logger.info("Nouvelle gare créée: {} ({})", nom, ville);

            HttpSession session = request.getSession();
            session.setAttribute("successMessage", "Gare créée avec succès");

        } catch (Exception e) {
            logger.error("Erreur lors de la création de la gare", e);
            HttpSession session = request.getSession();
            session.setAttribute("errorMessage", "Erreur lors de la création: " + e.getMessage());
        }

        response.sendRedirect(request.getContextPath() + "/admin/manage-gares");
    }

    private void handleToggleUserStatus(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try {
            Long userId = Long.parseLong(request.getParameter("userId"));
            String status = request.getParameter("status");

            Utilisateur.StatutCompte nouveauStatut = Utilisateur.StatutCompte.valueOf(status);

            if (nouveauStatut == Utilisateur.StatutCompte.BLOQUE) {
                authService.blockUser(userId);
            } else if (nouveauStatut == Utilisateur.StatutCompte.ACTIF) {
                authService.unblockUser(userId);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "Statut utilisateur mis à jour");

            objectMapper.writeValue(response.getWriter(), result);

        } catch (Exception e) {
            logger.error("Erreur lors du changement de statut utilisateur", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Erreur lors de la mise à jour");

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            objectMapper.writeValue(response.getWriter(), result);
        }
    }

    // API endpoints

    private void getStatsAPI(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try {
            Map<String, Object> stats = new HashMap<>();

            stats.put("totalUtilisateurs", utilisateurDAO.count());
            stats.put("totalGares", gareDAO.count());
            stats.put("totalTrajets", trajetDAO.count());
            stats.put("totalVoyages", voyageDAO.count());
            stats.put("totalBillets", billetDAO.count());
            stats.put("chiffreAffaires", billetDAO.calculateTotalRevenue());

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", stats);

            objectMapper.writeValue(response.getWriter(), result);

        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des statistiques", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Erreur lors de la récupération");

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            objectMapper.writeValue(response.getWriter(), result);
        }
    }

    private void getUsersAPI(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try {
            List<Utilisateur> utilisateurs = utilisateurDAO.findAll();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", utilisateurs);

            objectMapper.writeValue(response.getWriter(), result);

        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des utilisateurs", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Erreur lors de la récupération");

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            objectMapper.writeValue(response.getWriter(), result);
        }
    }

    private void getRevenueAPI(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try {
            Map<String, Object> revenue = new HashMap<>();
            revenue.put("total", billetDAO.calculateTotalRevenue());
            revenue.put("mois", billetDAO.calculateMonthRevenue());
            revenue.put("jour", billetDAO.calculateTodayRevenue());

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", revenue);

            objectMapper.writeValue(response.getWriter(), result);

        } catch (Exception e) {
            logger.error("Erreur lors de la récupération du chiffre d'affaires", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Erreur lors de la récupération");

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            objectMapper.writeValue(response.getWriter(), result);
        }
    }

    // Méthodes utilitaires

    private boolean isAdminAuthenticated(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("utilisateur") == null) {
            return false;
        }

        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");
        return utilisateur.getRole() == Utilisateur.Role.ADMIN &&
               utilisateur.getStatut() == Utilisateur.StatutCompte.ACTIF;
    }

    // Méthodes pour les autres handlers (à implémenter selon les besoins)
    private void handleUpdateGare(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // Implémentation similaire à handleCreateGare
        response.sendRedirect(request.getContextPath() + "/admin/manage-gares");
    }

    private void handleDeleteGare(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // Implémentation de suppression
        response.sendRedirect(request.getContextPath() + "/admin/manage-gares");
    }

    private void handleCreateTrajet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // Implémentation de création de trajet
        response.sendRedirect(request.getContextPath() + "/admin/manage-trajets");
    }

    private void handleUpdateTrajet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // Implémentation de mise à jour de trajet
        response.sendRedirect(request.getContextPath() + "/admin/manage-trajets");
    }

    private void handleDeleteTrajet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // Implémentation de suppression de trajet
        response.sendRedirect(request.getContextPath() + "/admin/manage-trajets");
    }

    private void handleCreateVoyage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // Implémentation de création de voyage
        response.sendRedirect(request.getContextPath() + "/admin/manage-voyages");
    }

    private void handleUpdateVoyage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // Implémentation de mise à jour de voyage
        response.sendRedirect(request.getContextPath() + "/admin/manage-voyages");
    }

    private void handleDeleteVoyage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // Implémentation de suppression de voyage
        response.sendRedirect(request.getContextPath() + "/admin/manage-voyages");
    }

    private void handleUpdateVoyageStatus(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // Implémentation de mise à jour du statut de voyage
        response.sendRedirect(request.getContextPath() + "/admin/manage-voyages");
    }
}
