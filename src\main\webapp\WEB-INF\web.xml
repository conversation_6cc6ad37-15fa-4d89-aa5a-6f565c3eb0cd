<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee
         http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
         version="4.0">

    <display-name>Train Ticket Management System</display-name>
    <description>Application JEE de gestion d'achat de tickets de train</description>

    <!-- Page d'accueil par défaut -->
    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>

    <!-- Servlet par défaut pour les ressources statiques -->
    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>/resources/*</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>/assets/*</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>*.css</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>*.js</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>*.png</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>*.jpg</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>*.jpeg</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>*.gif</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>*.ico</url-pattern>
    </servlet-mapping>

    <!-- Servlet d'authentification -->
    <servlet>
        <servlet-name>AuthServlet</servlet-name>
        <servlet-class>com.trainticket.controller.AuthServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AuthServlet</servlet-name>
        <url-pattern>/auth/*</url-pattern>
    </servlet-mapping>

    <!-- Servlet de recherche -->
    <servlet>
        <servlet-name>RechercheServlet</servlet-name>
        <servlet-class>com.trainticket.controller.RechercheServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RechercheServlet</servlet-name>
        <url-pattern>/recherche/*</url-pattern>
    </servlet-mapping>

    <!-- Servlet de réservation -->
    <servlet>
        <servlet-name>ReservationServlet</servlet-name>
        <servlet-class>com.trainticket.controller.ReservationServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ReservationServlet</servlet-name>
        <url-pattern>/reservation/*</url-pattern>
    </servlet-mapping>

    <!-- Servlet de paiement -->
    <servlet>
        <servlet-name>PaiementServlet</servlet-name>
        <servlet-class>com.trainticket.controller.PaiementServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PaiementServlet</servlet-name>
        <url-pattern>/paiement/*</url-pattern>
    </servlet-mapping>

    <!-- Servlet utilisateur -->
    <servlet>
        <servlet-name>UserServlet</servlet-name>
        <servlet-class>com.trainticket.controller.UserServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>UserServlet</servlet-name>
        <url-pattern>/user/*</url-pattern>
    </servlet-mapping>

    <!-- Servlet administrateur -->
    <servlet>
        <servlet-name>AdminServlet</servlet-name>
        <servlet-class>com.trainticket.controller.AdminServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AdminServlet</servlet-name>
        <url-pattern>/admin/*</url-pattern>
    </servlet-mapping>

    <!-- Servlet de la page d'accueil -->
    <servlet>
        <servlet-name>HomeServlet</servlet-name>
        <servlet-class>com.trainticket.servlet.HomeServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>HomeServlet</servlet-name>
        <url-pattern>/home</url-pattern>
    </servlet-mapping>

    <!-- Configuration des sessions -->
    <session-config>
        <session-timeout>30</session-timeout>
        <cookie-config>
            <http-only>true</http-only>
            <secure>false</secure>
        </cookie-config>
    </session-config>

    <!-- Pages d'erreur personnalisées -->
    <error-page>
        <error-code>404</error-code>
        <location>/WEB-INF/views/error/404.jsp</location>
    </error-page>

    <error-page>
        <error-code>500</error-code>
        <location>/WEB-INF/views/error/500.jsp</location>
    </error-page>

    <error-page>
        <error-code>403</error-code>
        <location>/WEB-INF/views/error/403.jsp</location>
    </error-page>

    <error-page>
        <exception-type>java.lang.Exception</exception-type>
        <location>/WEB-INF/views/error/500.jsp</location>
    </error-page>

    <!-- Configuration MIME types -->
    <mime-mapping>
        <extension>css</extension>
        <mime-type>text/css</mime-type>
    </mime-mapping>

    <mime-mapping>
        <extension>js</extension>
        <mime-type>application/javascript</mime-type>
    </mime-mapping>

</web-app>
