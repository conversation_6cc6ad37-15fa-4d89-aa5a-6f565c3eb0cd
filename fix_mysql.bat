@echo off
echo ========================================
echo   CORRECTION MYSQL POUR TRAIN TICKET
echo ========================================
echo.

echo 🔍 Test des mots de passe MySQL...
echo.

REM Test 1: Mot de passe vide
echo Test 1: Connexion sans mot de passe...
mysql -u root -e "SELECT 'Connexion OK' as status;" 2>nul
if %errorlevel% == 0 (
    echo ✅ MySQL fonctionne SANS mot de passe
    set MYSQL_PASSWORD=
    goto :update_config
)

REM Test 2: Mot de passe "root"
echo Test 2: Connexion avec mot de passe 'root'...
mysql -u root -proot -e "SELECT 'Connexion OK' as status;" 2>nul
if %errorlevel% == 0 (
    echo ✅ MySQL fonctionne avec mot de passe 'root'
    set MYSQL_PASSWORD=123
    goto :update_config
)

REM Test 3: Mot de passe vide avec -p
echo Test 3: Connexion avec -p (mot de passe vide)...
echo. | mysql -u root -p -e "SELECT 'Connexion OK' as status;" 2>nul
if %errorlevel% == 0 (
    echo ✅ MySQL fonctionne avec mot de passe vide (-p)
    set MYSQL_PASSWORD=
    goto :update_config
)

REM Test 4: Réinitialiser le mot de passe
echo Test 4: Tentative de réinitialisation...
echo ❌ Aucune connexion MySQL réussie
echo.
echo 🔧 SOLUTIONS:
echo 1. Redémarrez XAMPP/WAMP
echo 2. Ou exécutez cette commande pour réinitialiser:
echo    mysqladmin -u root password "123"
echo 3. Ou modifiez manuellement le fichier de config
goto :manual_fix

:update_config
echo.
echo 🔄 Création de la base de données...
if "%MYSQL_PASSWORD%"=="" (
    mysql -u root -e "CREATE DATABASE IF NOT EXISTS train_ticket_db; USE train_ticket_db; CREATE TABLE IF NOT EXISTS utilisateurs (id BIGINT AUTO_INCREMENT PRIMARY KEY, email VARCHAR(255) UNIQUE NOT NULL, mot_de_passe VARCHAR(255) NOT NULL, nom VARCHAR(100) NOT NULL, prenom VARCHAR(100) NOT NULL, telephone VARCHAR(20), adresse TEXT, role ENUM('USER', 'ADMIN') DEFAULT 'USER', statut ENUM('ACTIF', 'BLOQUE', 'SUSPENDU') DEFAULT 'ACTIF', date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP, derniere_connexion TIMESTAMP NULL);"
) else (
    mysql -u root -p%MYSQL_PASSWORD% -e "CREATE DATABASE IF NOT EXISTS train_ticket_db; USE train_ticket_db; CREATE TABLE IF NOT EXISTS utilisateurs (id BIGINT AUTO_INCREMENT PRIMARY KEY, email VARCHAR(255) UNIQUE NOT NULL, mot_de_passe VARCHAR(255) NOT NULL, nom VARCHAR(100) NOT NULL, prenom VARCHAR(100) NOT NULL, telephone VARCHAR(20), adresse TEXT, role ENUM('USER', 'ADMIN') DEFAULT 'USER', statut ENUM('ACTIF', 'BLOQUE', 'SUSPENDU') DEFAULT 'ACTIF', date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP, derniere_connexion TIMESTAMP NULL);"
)

echo ✅ Base de données créée avec succès!
echo.
echo 📝 Mot de passe MySQL détecté: "%MYSQL_PASSWORD%"
echo 🔄 Mise à jour de la configuration Java...
goto :end

:manual_fix
echo.
echo 📝 CORRECTION MANUELLE:
echo 1. Ouvrez XAMPP Control Panel
echo 2. Cliquez sur "Admin" à côté de MySQL
echo 3. Ou définissez un mot de passe avec:
echo    mysqladmin -u root password "123"
echo.

:end
echo ========================================
echo Configuration terminée!
echo Vous pouvez maintenant lancer: mvnw.cmd tomcat7:run
echo ========================================
pause
