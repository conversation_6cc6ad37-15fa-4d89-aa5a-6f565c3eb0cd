package com.trainticket.servlet;

import com.trainticket.dao.BilletDAO;
import com.trainticket.dao.impl.BilletDAOImpl;
import com.trainticket.model.Billet;
import com.trainticket.model.Utilisateur;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@WebServlet("/user/tickets")
public class UserTicketsServlet extends HttpServlet {

    private final BilletDAO billetDAO;

    public UserTicketsServlet() {
        this.billetDAO = new BilletDAOImpl();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("utilisateur") == null) {
            response.sendRedirect(request.getContextPath() + "/auth/login");
            return;
        }

        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");

        try {
            // Récupérer tous les billets de l'utilisateur
            List<Billet> tousBillets = billetDAO.findByUtilisateur(utilisateur);

            // Séparer les billets actifs et l'historique
            LocalDateTime maintenant = LocalDateTime.now();
            
            List<Billet> billetsActifs = tousBillets.stream()
                .filter(billet -> billet.getStatut() == Billet.StatutBillet.ACHETE && 
                                 billet.getVoyage().getDateHeureDepart().isAfter(maintenant))
                .collect(Collectors.toList());

            List<Billet> billetsHistorique = tousBillets.stream()
                .filter(billet -> billet.getStatut() == Billet.StatutBillet.UTILISE || 
                                 billet.getStatut() == Billet.StatutBillet.ANNULE ||
                                 (billet.getStatut() == Billet.StatutBillet.ACHETE && 
                                  billet.getVoyage().getDateHeureDepart().isBefore(maintenant)))
                .collect(Collectors.toList());

            // Trier par date de voyage (plus récent en premier)
            billetsActifs.sort((b1, b2) -> b1.getVoyage().getDateHeureDepart()
                                          .compareTo(b2.getVoyage().getDateHeureDepart()));
            
            billetsHistorique.sort((b1, b2) -> b2.getVoyage().getDateHeureDepart()
                                              .compareTo(b1.getVoyage().getDateHeureDepart()));

            request.setAttribute("billetsActifs", billetsActifs);
            request.setAttribute("billetsHistorique", billetsHistorique);

            request.getRequestDispatcher("/WEB-INF/views/user/tickets.jsp").forward(request, response);

        } catch (Exception e) {
            System.err.println("Erreur lors de la récupération des billets: " + e.getMessage());
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
}
