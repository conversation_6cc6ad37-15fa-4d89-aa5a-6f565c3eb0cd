<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test de Recherche</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        .error {
            color: red;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .success {
            color: green;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
    </style>
</head>
<body>
    <h1>Test de Recherche de Voyages</h1>
    
    <form id="searchForm">
        <div class="form-group">
            <label for="villeDepart">Ville de départ:</label>
            <select id="villeDepart" name="villeDepart" required>
                <option value="">Sélectionnez une ville</option>
                <option value="Tunis">Tunis</option>
                <option value="Sfax">Sfax</option>
                <option value="Sousse">Sousse</option>
                <option value="Bizerte">Bizerte</option>
                <option value="Gabès">Gabès</option>
                <option value="Kairouan">Kairouan</option>
                <option value="Béja">Béja</option>
                <option value="Le Kef">Le Kef</option>
                <option value="Tozeur">Tozeur</option>
                <option value="Gafsa">Gafsa</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="villeArrivee">Ville d'arrivée:</label>
            <select id="villeArrivee" name="villeArrivee" required>
                <option value="">Sélectionnez une ville</option>
                <option value="Tunis">Tunis</option>
                <option value="Sfax">Sfax</option>
                <option value="Sousse">Sousse</option>
                <option value="Bizerte">Bizerte</option>
                <option value="Gabès">Gabès</option>
                <option value="Kairouan">Kairouan</option>
                <option value="Béja">Béja</option>
                <option value="Le Kef">Le Kef</option>
                <option value="Tozeur">Tozeur</option>
                <option value="Gafsa">Gafsa</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="dateVoyage">Date de voyage:</label>
            <input type="date" id="dateVoyage" name="dateVoyage" required>
        </div>
        
        <div class="form-group">
            <label for="nombrePassagers">Nombre de passagers:</label>
            <select id="nombrePassagers" name="nombrePassagers">
                <option value="1">1 passager</option>
                <option value="2">2 passagers</option>
                <option value="3">3 passagers</option>
                <option value="4">4 passagers</option>
                <option value="5">5 passagers</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="voyagesDirects" name="voyagesDirects">
                Voyages directs uniquement
            </label>
        </div>
        
        <button type="submit">Rechercher des voyages</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        // Définir la date par défaut à demain
        document.getElementById('dateVoyage').valueAsDate = new Date(Date.now() + 24*60*60*1000);
        
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            
            // Afficher un message de chargement
            resultDiv.innerHTML = '<div class="result">Recherche en cours...</div>';
            
            // Envoyer la requête
            fetch('http://localhost:8090/train-ticket/recherche/search', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (response.ok) {
                    return response.text();
                } else {
                    throw new Error('Erreur HTTP: ' + response.status);
                }
            })
            .then(html => {
                // Extraire le contenu principal de la réponse
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const content = doc.querySelector('.container') || doc.body;
                
                resultDiv.innerHTML = '<div class="result success"><h3>Recherche réussie!</h3>' + 
                                    content.innerHTML + '</div>';
            })
            .catch(error => {
                console.error('Erreur:', error);
                resultDiv.innerHTML = '<div class="result error"><h3>Erreur lors de la recherche</h3>' + 
                                    '<p>' + error.message + '</p></div>';
            });
        });
    </script>
</body>
</html>
