/**
 * Train Ticket Management System - JavaScript principal
 */

// Configuration globale
const TrainTicket = {
    baseUrl: window.location.origin + '/train-ticket',

    // Utilitaires
    utils: {
        // Formater une date
        formatDate: function(date) {
            if (!(date instanceof Date)) {
                date = new Date(date);
            }
            return date.toLocaleDateString('fr-FR', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        },

        // Formater une heure
        formatTime: function(date) {
            if (!(date instanceof Date)) {
                date = new Date(date);
            }
            return date.toLocaleTimeString('fr-FR', {
                hour: '2-digit',
                minute: '2-digit'
            });
        },

        // Formater un prix
        formatPrice: function(price) {
            return new Intl.NumberFormat('fr-FR', {
                style: 'currency',
                currency: 'MAD'
            }).format(price);
        },

        // Afficher une notification
        showNotification: function(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            const container = document.querySelector('.container') || document.body;
            container.insertBefore(alertDiv, container.firstChild);

            // Auto-dismiss après 5 secondes
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        },

        // Confirmer une action
        confirm: function(message, callback) {
            if (confirm(message)) {
                callback();
            }
        },

        // Valider un email
        isValidEmail: function(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },

        // Valider un mot de passe
        isValidPassword: function(password) {
            // Au moins 8 caractères, une majuscule, une minuscule, un chiffre et un caractère spécial
            const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
            return passwordRegex.test(password);
        }
    },

    // Gestion des formulaires
    forms: {
        // Validation en temps réel
        setupValidation: function() {
            const forms = document.querySelectorAll('form[data-validate="true"]');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    if (!form.checkValidity()) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                    form.classList.add('was-validated');
                });

                // Validation des champs individuels
                const inputs = form.querySelectorAll('input, select, textarea');
                inputs.forEach(input => {
                    input.addEventListener('blur', function() {
                        this.classList.add('was-validated');
                    });
                });
            });
        },

        // Soumission AJAX
        submitAjax: function(form, callback) {
            const formData = new FormData(form);

            fetch(form.action, {
                method: form.method || 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (callback) callback(data);
            })
            .catch(error => {
                console.error('Erreur:', error);
                TrainTicket.utils.showNotification('Une erreur est survenue', 'danger');
            });
        }
    },

    // Gestion de la recherche
    search: {
        // Recherche de voyages
        searchVoyages: function(formData) {
            const loadingBtn = document.querySelector('#searchBtn');
            const originalText = loadingBtn.innerHTML;

            loadingBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Recherche...';
            loadingBtn.disabled = true;

            fetch('/train-ticket/recherche/search', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                const searchResults = document.getElementById('searchResults');
                if (searchResults) {
                    searchResults.innerHTML = html;
                    searchResults.scrollIntoView({ behavior: 'smooth' });
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                TrainTicket.utils.showNotification('Erreur lors de la recherche', 'danger');
            })
            .finally(() => {
                loadingBtn.innerHTML = originalText;
                loadingBtn.disabled = false;
            });
        }
    },

    // Gestion des réservations
    reservation: {
        // Sélectionner un voyage
        selectVoyage: function(voyageId) {
            window.location.href = `/train-ticket/reservation/select?voyageId=${voyageId}`;
        },

        // Confirmer une réservation
        confirmReservation: function(form) {
            TrainTicket.utils.confirm('Confirmer cette réservation ?', function() {
                form.submit();
            });
        },

        // Annuler une réservation
        cancelReservation: function(billetId) {
            TrainTicket.utils.confirm('Êtes-vous sûr de vouloir annuler cette réservation ?', function() {
                fetch(`/train-ticket/user/cancel-reservation`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `billetId=${billetId}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        TrainTicket.utils.showNotification('Réservation annulée avec succès', 'success');
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        TrainTicket.utils.showNotification(data.message || 'Erreur lors de l\'annulation', 'danger');
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    TrainTicket.utils.showNotification('Erreur lors de l\'annulation', 'danger');
                });
            });
        }
    },

    // Gestion de l'administration
    admin: {
        // Supprimer un élément
        deleteItem: function(url, itemName) {
            TrainTicket.utils.confirm(`Êtes-vous sûr de vouloir supprimer ${itemName} ?`, function() {
                fetch(url, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        TrainTicket.utils.showNotification('Élément supprimé avec succès', 'success');
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        TrainTicket.utils.showNotification(data.message || 'Erreur lors de la suppression', 'danger');
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    TrainTicket.utils.showNotification('Erreur lors de la suppression', 'danger');
                });
            });
        },

        // Basculer le statut d'un utilisateur
        toggleUserStatus: function(userId, currentStatus) {
            const newStatus = currentStatus === 'ACTIF' ? 'BLOQUE' : 'ACTIF';
            const action = newStatus === 'ACTIF' ? 'débloquer' : 'bloquer';

            TrainTicket.utils.confirm(`Êtes-vous sûr de vouloir ${action} cet utilisateur ?`, function() {
                fetch('/train-ticket/admin/toggle-user-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `userId=${userId}&status=${newStatus}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        TrainTicket.utils.showNotification(`Utilisateur ${action} avec succès`, 'success');
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        TrainTicket.utils.showNotification(data.message || 'Erreur lors de l\'opération', 'danger');
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    TrainTicket.utils.showNotification('Erreur lors de l\'opération', 'danger');
                });
            });
        }
    }
};

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    // Configuration des formulaires
    TrainTicket.forms.setupValidation();

    // Configuration des tooltips Bootstrap
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Configuration des popovers Bootstrap
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Gestion du formulaire de recherche
    const searchForm = document.getElementById('searchForm');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            TrainTicket.search.searchVoyages(formData);
        });
    }

    // Auto-dismiss des alertes
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.parentNode) {
                alert.classList.remove('show');
                setTimeout(() => alert.remove(), 150);
            }
        }, 5000);
    });

    // Smooth scrolling pour les liens d'ancrage
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // Animation des éléments au scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, observerOptions);

    const animatedElements = document.querySelectorAll('.card, .feature-icon, .voyage-card');
    animatedElements.forEach(el => observer.observe(el));
});

// Exposition globale
window.TrainTicket = TrainTicket;
