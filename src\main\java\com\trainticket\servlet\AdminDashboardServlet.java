package com.trainticket.servlet;

import com.trainticket.controller.AdminController;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Servlet pour le tableau de bord administrateur
 * Charge les données dynamiques et redirige vers dashboard.jsp
 */
@WebServlet(name = "AdminDashboardServlet", urlPatterns = {"/admin/dashboard", "/admin"})
public class AdminDashboardServlet extends HttpServlet {
    
    private AdminController adminController;
    
    @Override
    public void init() throws ServletException {
        super.init();
        this.adminController = new AdminController();
        System.out.println("AdminDashboardServlet initialisé");
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        try {
            // Vérifier les droits d'administration (simulation)
            // Dans un vrai projet, on vérifierait le rôle de l'utilisateur connecté
            
            // Charger les données dynamiques pour le tableau de bord
            adminController.loadDashboardData(request);
            
            // Rediriger vers la page du tableau de bord
            request.getRequestDispatcher("/WEB-INF/views/admin/dashboard.jsp").forward(request, response);
            
        } catch (Exception e) {
            System.err.println("Erreur lors du traitement du tableau de bord admin: " + e.getMessage());
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, 
                             "Erreur lors du chargement du tableau de bord");
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        doGet(request, response);
    }
}
