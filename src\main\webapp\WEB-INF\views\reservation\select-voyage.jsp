<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirmation de réservation - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="${pageContext.request.contextPath}/resources/css/style.css" rel="stylesheet">

    <style>
        .voyage-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 0.5rem;
        }

        .step.active .step-number {
            background: #007bff;
            color: white;
        }

        .step.completed .step-number {
            background: #28a745;
            color: white;
        }

        .class-option {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .class-option:hover {
            border-color: #007bff;
            box-shadow: 0 5px 15px rgba(0,123,255,0.1);
        }

        .class-option.selected {
            border-color: #007bff;
            background: rgba(0,123,255,0.05);
        }

        .price-breakdown {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
        }

        .passenger-form {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <jsp:include page="../common/navbar.jsp" />

    <div class="container my-5">
        <!-- Indicateur d'étapes -->
        <div class="step-indicator">
            <div class="step completed">
                <div class="step-number">1</div>
                <span>Sélection du voyage</span>
            </div>
            <div class="step active">
                <div class="step-number">2</div>
                <span>Détails de réservation</span>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <span>Paiement</span>
            </div>
            <div class="step">
                <div class="step-number">4</div>
                <span>Confirmation</span>
            </div>
        </div>

        <!-- Résumé du voyage -->
        <div class="voyage-summary">
            <div class="row align-items-center">
                <div class="col-md-3 text-center">
                    <h4><fmt:formatDate value="${voyage.dateHeureDepart}" pattern="HH:mm" /></h4>
                    <p class="mb-0">${voyage.trajet.gareDepart.ville}</p>
                    <small><fmt:formatDate value="${voyage.dateHeureDepart}" pattern="dd/MM/yyyy" /></small>
                </div>
                <div class="col-md-3 text-center">
                    <i class="fas fa-arrow-right fa-2x mb-2"></i>
                    <p class="mb-0">
                        <fmt:formatNumber value="${voyage.trajet.dureeMinutes / 60}" maxFractionDigits="0" />h
                        <fmt:formatNumber value="${voyage.trajet.dureeMinutes % 60}" maxFractionDigits="0" />min
                    </p>
                    <span class="badge bg-light text-dark">${voyage.trajet.type}</span>
                </div>
                <div class="col-md-3 text-center">
                    <h4><fmt:formatDate value="${voyage.dateHeureArrivee}" pattern="HH:mm" /></h4>
                    <p class="mb-0">${voyage.trajet.gareArrivee.ville}</p>
                    <small><fmt:formatDate value="${voyage.dateHeureArrivee}" pattern="dd/MM/yyyy" /></small>
                </div>
                <div class="col-md-3 text-center">
                    <h5>Train ${voyage.trajet.numeroTrajet}</h5>
                    <p class="mb-0">
                        <i class="fas fa-users"></i>
                        ${voyage.placesDisponibles} places disponibles
                    </p>
                </div>
            </div>
        </div>

        <form id="reservationForm" action="${pageContext.request.contextPath}/reservation/confirm" method="post">
            <input type="hidden" name="voyageId" value="${voyage.id}">

            <div class="row">
                <!-- Sélection de classe et options -->
                <div class="col-lg-8">
                    <!-- Sélection de la classe -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chair"></i> Choisissez votre classe</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="class-option" data-class="ECONOMIQUE" data-price="${voyage.trajet.prixBase}">
                                        <div class="text-center">
                                            <i class="fas fa-chair fa-2x text-secondary mb-2"></i>
                                            <h6>Économique</h6>
                                            <p class="text-muted small">Sièges standards, climatisation</p>
                                            <h5 class="text-primary">
                                                <fmt:formatNumber value="${voyage.trajet.prixBase}" type="currency" currencySymbol="DT" />
                                            </h5>
                                        </div>
                                        <input type="radio" name="classe" value="ECONOMIQUE" class="d-none" checked>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="class-option" data-class="DEUXIEME" data-price="${voyage.trajet.prixBase * 1.5}">
                                        <div class="text-center">
                                            <i class="fas fa-couch fa-2x text-warning mb-2"></i>
                                            <h6>2ème Classe</h6>
                                            <p class="text-muted small">Sièges confortables, climatisation</p>
                                            <h5 class="text-warning">
                                                <fmt:formatNumber value="${voyage.trajet.prixBase * 1.5}" type="currency" currencySymbol="DH" />
                                            </h5>
                                        </div>
                                        <input type="radio" name="classe" value="DEUXIEME" class="d-none">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="class-option" data-class="PREMIERE" data-price="${voyage.trajet.prixBase * 2}">
                                        <div class="text-center">
                                            <i class="fas fa-crown fa-2x text-danger mb-2"></i>
                                            <h6>1ère Classe</h6>
                                            <p class="text-muted small">Sièges premium, service VIP</p>
                                            <h5 class="text-danger">
                                                <fmt:formatNumber value="${voyage.trajet.prixBase * 2}" type="currency" currencySymbol="DH" />
                                            </h5>
                                        </div>
                                        <input type="radio" name="classe" value="PREMIERE" class="d-none">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informations passager -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-user"></i> Informations du passager</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="nom" class="form-label">Nom *</label>
                                        <input type="text" class="form-control" id="nom" name="nom"
                                               value="${utilisateur.nom}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="prenom" class="form-label">Prénom *</label>
                                        <input type="text" class="form-control" id="prenom" name="prenom"
                                               value="${utilisateur.prenom}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email *</label>
                                        <input type="email" class="form-control" id="email" name="email"
                                               value="${utilisateur.email}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="telephone" class="form-label">Téléphone *</label>
                                        <input type="tel" class="form-control" id="telephone" name="telephone"
                                               value="${utilisateur.telephone}" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Préférences de voyage -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-heart"></i> Préférences de voyage</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Position du siège</h6>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="radio" id="fenetre" name="preferenceFenetre" value="FENETRE">
                                        <label class="form-check-label" for="fenetre">
                                            <i class="fas fa-window-maximize text-info"></i> Côté fenêtre
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="radio" id="couloir" name="preferenceFenetre" value="COULOIR">
                                        <label class="form-check-label" for="couloir">
                                            <i class="fas fa-walking text-primary"></i> Côté couloir
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="radio" id="indifferent" name="preferenceFenetre" value="INDIFFERENT" checked>
                                        <label class="form-check-label" for="indifferent">
                                            <i class="fas fa-minus text-muted"></i> Indifférent
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Options spéciales</h6>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="espaceFamille" name="espaceFamille" value="true">
                                        <label class="form-check-label" for="espaceFamille">
                                            <i class="fas fa-users text-success"></i> Espace famille
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="wagonNonFumeur" name="wagonNonFumeur" value="true" checked>
                                        <label class="form-check-label" for="wagonNonFumeur">
                                            <i class="fas fa-ban text-danger"></i> Wagon non-fumeur
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="accesHandicape" name="accesHandicape" value="true">
                                        <label class="form-check-label" for="accesHandicape">
                                            <i class="fas fa-wheelchair text-info"></i> Accès handicapé
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Options supplémentaires -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-plus-circle"></i> Services additionnels</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="wifi" name="wifiRequis" value="true">
                                        <label class="form-check-label" for="wifi">
                                            <i class="fas fa-wifi text-info"></i> WiFi premium (+5 DH)
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="priseElectrique" name="priseElectrique" value="true">
                                        <label class="form-check-label" for="priseElectrique">
                                            <i class="fas fa-plug text-warning"></i> Prise électrique (+3 DH)
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="repas" name="repas" value="true">
                                        <label class="form-check-label" for="repas">
                                            <i class="fas fa-utensils text-primary"></i> Repas à bord (+15 DH)
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="assurance" name="assurance" value="true">
                                        <label class="form-check-label" for="assurance">
                                            <i class="fas fa-shield-alt text-success"></i> Assurance voyage (+10 DH)
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <label for="commentairesSpeciaux" class="form-label">Commentaires spéciaux</label>
                                    <textarea class="form-control" id="commentairesSpeciaux" name="commentairesSpeciaux" rows="2"
                                            placeholder="Demandes particulières, allergies, etc."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Résumé et prix -->
                <div class="col-lg-4">
                    <div class="price-breakdown sticky-top">
                        <h5 class="mb-3"><i class="fas fa-receipt"></i> Résumé de la réservation</h5>

                        <div class="d-flex justify-content-between mb-2">
                            <span>Billet (<span id="selectedClass">Économique</span>)</span>
                            <span id="basePrice"><fmt:formatNumber value="${voyage.trajet.prixBase}" type="currency" currencySymbol="DT" /></span>
                        </div>

                        <div id="optionsPrice" style="display: none;">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Options supplémentaires</span>
                                <span id="optionsPriceValue">0 DT</span>
                            </div>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between mb-3">
                            <strong>Total</strong>
                            <strong id="totalPrice"><fmt:formatNumber value="${voyage.trajet.prixBase}" type="currency" currencySymbol="DT" /></strong>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-credit-card"></i> Procéder au paiement
                            </button>
                            <a href="${pageContext.request.contextPath}/recherche/results" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Retour aux résultats
                            </a>
                        </div>

                        <div class="mt-3 text-center">
                            <small class="text-muted">
                                <i class="fas fa-lock"></i> Paiement sécurisé SSL
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Footer -->
    <jsp:include page="../common/footer.jsp" />

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/main.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const classOptions = document.querySelectorAll('.class-option');
            const optionsCheckboxes = document.querySelectorAll('input[name="options"]');

            // Prix de base par classe
            const classPrices = {
                'ECONOMIQUE': ${voyage.trajet.prixBase},
                'AFFAIRES': ${voyage.trajet.prixBase * 1.5},
                'PREMIERE': ${voyage.trajet.prixBase * 2}
            };

            // Prix des options
            const optionPrices = {
                'repas': 15,
                'wifi': 5,
                'assurance': 10,
                'priorite': 8
            };

            let selectedClass = 'ECONOMIQUE';
            let selectedOptions = [];

            // Gestion de la sélection de classe
            classOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Retirer la sélection précédente
                    classOptions.forEach(opt => opt.classList.remove('selected'));

                    // Ajouter la sélection actuelle
                    this.classList.add('selected');

                    // Mettre à jour la classe sélectionnée
                    selectedClass = this.dataset.class;
                    this.querySelector('input[type="radio"]').checked = true;

                    // Mettre à jour l'affichage
                    updatePriceDisplay();
                });
            });

            // Gestion des options
            optionsCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    if (this.checked) {
                        selectedOptions.push(this.value);
                    } else {
                        selectedOptions = selectedOptions.filter(opt => opt !== this.value);
                    }
                    updatePriceDisplay();
                });
            });

            function updatePriceDisplay() {
                // Mettre à jour le nom de la classe
                const classNames = {
                    'ECONOMIQUE': 'Économique',
                    'AFFAIRES': 'Affaires',
                    'PREMIERE': 'Première'
                };
                document.getElementById('selectedClass').textContent = classNames[selectedClass];

                // Calculer le prix de base
                const basePrice = classPrices[selectedClass];
                document.getElementById('basePrice').textContent = basePrice.toFixed(2) + ' DT';

                // Calculer le prix des options
                const optionsPrice = selectedOptions.reduce((total, option) => {
                    return total + (optionPrices[option] || 0);
                }, 0);

                // Afficher/masquer la section options
                const optionsPriceDiv = document.getElementById('optionsPrice');
                if (optionsPrice > 0) {
                    optionsPriceDiv.style.display = 'block';
                    document.getElementById('optionsPriceValue').textContent = optionsPrice.toFixed(2) + ' DT';
                } else {
                    optionsPriceDiv.style.display = 'none';
                }

                // Calculer et afficher le prix total
                const totalPrice = basePrice + optionsPrice;
                document.getElementById('totalPrice').textContent = totalPrice.toFixed(2) + ' DT';
            }

            // Initialiser l'affichage
            classOptions[0].classList.add('selected');
            updatePriceDisplay();

            // Validation du formulaire
            document.getElementById('reservationForm').addEventListener('submit', function(e) {
                const requiredFields = this.querySelectorAll('input[required]');
                let isValid = true;

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.classList.add('is-invalid');
                        isValid = false;
                    } else {
                        field.classList.remove('is-invalid');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    alert('Veuillez remplir tous les champs obligatoires.');
                }
            });
        });
    </script>
</body>
</html>
