<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Préférences de Voyage - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .preference-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .preference-card:hover {
            border-color: #007bff;
            box-shadow: 0 2px 10px rgba(0,123,255,0.1);
        }
        .preference-card.selected {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .window-option {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .window-option:hover {
            border-color: #007bff;
        }
        .window-option.selected {
            border-color: #007bff;
            background-color: #e7f3ff;
        }
    </style>
</head>
<body>
    <%@ include file="../common/header.jsp" %>

    <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="${pageContext.request.contextPath}/">Accueil</a></li>
                <li class="breadcrumb-item"><a href="${pageContext.request.contextPath}/recherche/search">Recherche</a></li>
                <li class="breadcrumb-item"><a href="javascript:history.back()">Sélection voyage</a></li>
                <li class="breadcrumb-item active">Préférences</li>
            </ol>
        </nav>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-cog"></i> Vos préférences de voyage</h4>
                        <small>Personnalisez votre expérience de voyage</small>
                    </div>
                    <div class="card-body">
                        <form id="preferencesForm" method="post" action="${pageContext.request.contextPath}/selection/select-preferences">
                            
                            <!-- Préférence fenêtre -->
                            <div class="mb-4">
                                <h6 class="mb-3"><i class="fas fa-window-maximize"></i> Préférence de siège</h6>
                                <div class="row">
                                    <div class="col-md-4 mb-2">
                                        <div class="window-option p-3 text-center" data-preference="FENETRE">
                                            <input type="radio" name="preferenceFenetre" value="FENETRE" class="d-none">
                                            <i class="fas fa-window-maximize fa-2x text-primary mb-2"></i>
                                            <h6>Côté fenêtre</h6>
                                            <small class="text-muted">Vue sur le paysage</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <div class="window-option p-3 text-center" data-preference="COULOIR">
                                            <input type="radio" name="preferenceFenetre" value="COULOIR" class="d-none">
                                            <i class="fas fa-walking fa-2x text-success mb-2"></i>
                                            <h6>Côté couloir</h6>
                                            <small class="text-muted">Facilité de mouvement</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <div class="window-option p-3 text-center" data-preference="INDIFFERENT">
                                            <input type="radio" name="preferenceFenetre" value="INDIFFERENT" class="d-none" checked>
                                            <i class="fas fa-question fa-2x text-secondary mb-2"></i>
                                            <h6>Indifférent</h6>
                                            <small class="text-muted">Pas de préférence</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <!-- Options de confort -->
                            <div class="mb-4">
                                <h6 class="mb-3"><i class="fas fa-heart"></i> Options de confort</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="preference-card p-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="wagonNonFumeur" id="wagonNonFumeur" checked>
                                                <label class="form-check-label" for="wagonNonFumeur">
                                                    <i class="fas fa-ban text-danger"></i> <strong>Wagon non-fumeur</strong>
                                                    <br><small class="text-muted">Environnement sans fumée</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="preference-card p-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="espaceFamille" id="espaceFamille">
                                                <label class="form-check-label" for="espaceFamille">
                                                    <i class="fas fa-users text-info"></i> <strong>Espace famille</strong>
                                                    <br><small class="text-muted">Zone dédiée aux familles</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="preference-card p-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="accesHandicape" id="accesHandicape">
                                                <label class="form-check-label" for="accesHandicape">
                                                    <i class="fas fa-wheelchair text-primary"></i> <strong>Accès handicapé</strong>
                                                    <br><small class="text-muted">Siège adapté PMR</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="preference-card p-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="wifiRequis" id="wifiRequis">
                                                <label class="form-check-label" for="wifiRequis">
                                                    <i class="fas fa-wifi text-success"></i> <strong>WiFi requis</strong>
                                                    <br><small class="text-muted">Connexion internet</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="preference-card p-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="priseElectrique" id="priseElectrique">
                                                <label class="form-check-label" for="priseElectrique">
                                                    <i class="fas fa-plug text-warning"></i> <strong>Prise électrique</strong>
                                                    <br><small class="text-muted">Pour charger vos appareils</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <!-- Commentaires spéciaux -->
                            <div class="mb-4">
                                <h6 class="mb-3"><i class="fas fa-comment"></i> Demandes spéciales</h6>
                                <div class="form-floating">
                                    <textarea class="form-control" name="commentairesSpeciaux" id="commentairesSpeciaux" 
                                              style="height: 100px" placeholder="Vos demandes spéciales..."></textarea>
                                    <label for="commentairesSpeciaux">Commentaires ou demandes spéciales (optionnel)</label>
                                </div>
                                <small class="text-muted">
                                    Exemple : allergie alimentaire, assistance particulière, groupe scolaire, etc.
                                </small>
                            </div>

                            <!-- Boutons de navigation -->
                            <div class="d-flex justify-content-between mt-4">
                                <a href="javascript:history.back()" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left"></i> Retour
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    Continuer <i class="fas fa-arrow-right"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Résumé du voyage -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-clipboard-list"></i> Résumé de votre voyage</h6>
                    </div>
                    <div class="card-body">
                        <c:if test="${not empty sessionScope.selectedVoyage}">
                            <div class="mb-3">
                                <strong>Trajet :</strong><br>
                                <small class="text-muted">
                                    ${sessionScope.selectedVoyage.trajet.gareDepart.ville} → 
                                    ${sessionScope.selectedVoyage.trajet.gareArrivee.ville}
                                </small>
                            </div>
                            <div class="mb-3">
                                <strong>Date :</strong><br>
                                <small class="text-muted">
                                    <fmt:formatDate value="${sessionScope.selectedVoyage.dateHeureDepart}" pattern="dd/MM/yyyy à HH:mm"/>
                                </small>
                            </div>
                            <div class="mb-3">
                                <strong>Classe :</strong><br>
                                <small class="text-muted">
                                    <c:choose>
                                        <c:when test="${sessionScope.selectedClasse == 'PREMIERE'}">1ère Classe</c:when>
                                        <c:when test="${sessionScope.selectedClasse == 'DEUXIEME'}">2ème Classe</c:when>
                                        <c:otherwise>Économique</c:otherwise>
                                    </c:choose>
                                </small>
                            </div>
                            <div class="mb-3">
                                <strong>Passagers :</strong><br>
                                <small class="text-muted">${sessionScope.nombrePassagers} personne(s)</small>
                            </div>
                        </c:if>
                    </div>
                </div>

                <!-- Conseils -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-lightbulb"></i> Conseils</h6>
                    </div>
                    <div class="card-body">
                        <div class="small">
                            <p><strong>💡 Astuce :</strong> Les préférences sont des demandes, nous ferons de notre mieux pour les respecter selon la disponibilité.</p>
                            <p><strong>🎯 Recommandation :</strong> Pour les longs trajets, nous recommandons une place côté fenêtre.</p>
                            <p><strong>👨‍👩‍👧‍👦 Famille :</strong> L'espace famille offre plus de place pour les bagages et les enfants.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include file="../common/footer.jsp" %>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Gestion des options de fenêtre
            const windowOptions = document.querySelectorAll('.window-option');
            
            // Sélectionner "Indifférent" par défaut
            document.querySelector('.window-option[data-preference="INDIFFERENT"]').classList.add('selected');

            windowOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Désélectionner toutes les options
                    windowOptions.forEach(opt => {
                        opt.classList.remove('selected');
                        opt.querySelector('input[type="radio"]').checked = false;
                    });

                    // Sélectionner l'option cliquée
                    this.classList.add('selected');
                    this.querySelector('input[type="radio"]').checked = true;
                });
            });

            // Gestion des cartes de préférences
            const preferenceCards = document.querySelectorAll('.preference-card');
            
            preferenceCards.forEach(card => {
                const checkbox = card.querySelector('input[type="checkbox"]');
                
                card.addEventListener('click', function(e) {
                    if (e.target.type !== 'checkbox') {
                        checkbox.checked = !checkbox.checked;
                    }
                    
                    if (checkbox.checked) {
                        this.classList.add('selected');
                    } else {
                        this.classList.remove('selected');
                    }
                });

                // État initial
                if (checkbox.checked) {
                    card.classList.add('selected');
                }
            });

            // Animation des cartes au survol
            preferenceCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
