<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demande Soumise - Train Ticket</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="${pageContext.request.contextPath}/resources/css/style.css" rel="stylesheet">
</head>
<body>
    <%@ include file="../common/navbar.jsp" %>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                        </div>
                        
                        <h2 class="text-success mb-3">Demande soumise avec succès !</h2>
                        
                        <div class="alert alert-success">
                            <p class="mb-0">${success}</p>
                        </div>

                        <c:if test="${not empty demande}">
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="mb-0">Récapitulatif de votre demande</h5>
                                </div>
                                <div class="card-body text-start">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>Billet concerné:</strong> ${demande.billet.numeroBillet}</p>
                                            <p><strong>Voyage:</strong> 
                                               ${demande.billet.voyage.trajet.gareDepart.ville} → 
                                               ${demande.billet.voyage.trajet.gareArrivee.ville}
                                            </p>
                                            <p><strong>Date du voyage:</strong> 
                                               <fmt:formatDate value="${demande.billet.voyage.dateHeureDepart}" 
                                                             pattern="dd/MM/yyyy HH:mm"/>
                                            </p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Date de la demande:</strong> 
                                               <fmt:formatDate value="${demande.dateDemande}" 
                                                             pattern="dd/MM/yyyy HH:mm"/>
                                            </p>
                                            <p><strong>Statut:</strong> 
                                               <span class="badge bg-warning">En attente</span>
                                            </p>
                                            <p><strong>Montant estimé du remboursement:</strong> 
                                               <span class="text-success fw-bold">
                                                   <fmt:formatNumber value="${demande.montantRemboursement}" 
                                                                   type="currency" currencySymbol="DH"/>
                                               </span>
                                            </p>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <strong>Motif de l'annulation:</strong>
                                        <p class="border p-3 bg-light mt-2">${demande.motifAnnulation}</p>
                                    </div>
                                </div>
                            </div>
                        </c:if>

                        <div class="alert alert-info mt-4">
                            <h6><i class="fas fa-info-circle"></i> Prochaines étapes</h6>
                            <ul class="text-start mb-0">
                                <li>Votre demande sera examinée par notre équipe dans un délai de 48h</li>
                                <li>Vous recevrez un email de confirmation de la décision</li>
                                <li>En cas d'approbation, le remboursement sera effectué sous 5-7 jours ouvrables</li>
                                <li>Vous pouvez suivre l'état de votre demande dans votre espace personnel</li>
                            </ul>
                        </div>

                        <div class="mt-4">
                            <a href="${pageContext.request.contextPath}/user/annulation/list" 
                               class="btn btn-primary me-3">
                                <i class="fas fa-list"></i> Voir mes demandes
                            </a>
                            <a href="${pageContext.request.contextPath}/user/profile" 
                               class="btn btn-outline-secondary">
                                <i class="fas fa-user"></i> Mon profil
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include file="../common/footer.jsp" %>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
