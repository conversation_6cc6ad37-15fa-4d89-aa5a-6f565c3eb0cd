-- Script pour charger les voyages avec des dates variées
-- Partie 2: Insertion des voyages

USE train_ticket_db;

-- ========================================
-- INSERTION DES VOYAGES (Prochains 30 jours)
-- ========================================

-- Voyages pour aujourd'hui et les prochains jours
-- Trajets principaux avec plusieurs horaires par jour

-- TUNIS - SOUSSE (plusieurs départs par jour)
INSERT INTO voyages (trajet_id, date_heure_depart, date_heure_arrivee, places_disponibles, places_premiere_classe, places_deuxieme_classe, places_economique, statut) VALUES
-- Aujourd'hui
(1, CONCAT(CURDATE(), ' 06:00:00'), CONCAT(CURDATE(), ' 08:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(CURDATE(), ' 09:30:00'), CONCAT(CURDATE(), ' 11:30:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(CURDATE(), ' 13:00:00'), CONCAT(CURDATE(), ' 15:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(CURDATE(), ' 16:30:00'), CONCAT(CURDATE(), ' 18:30:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(CURDATE(), ' 19:00:00'), CONCAT(CURDATE(), ' 21:00:00'), 200, 20, 60, 120, 'PROGRAMME'),

-- Demain
(1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 06:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 08:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 09:30:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 11:30:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 13:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 15:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 16:30:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 18:30:00'), 200, 20, 60, 120, 'PROGRAMME'),
(1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 19:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 21:00:00'), 200, 20, 60, 120, 'PROGRAMME'),

-- SOUSSE - TUNIS (retour)
(11, CONCAT(CURDATE(), ' 07:00:00'), CONCAT(CURDATE(), ' 09:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(11, CONCAT(CURDATE(), ' 10:30:00'), CONCAT(CURDATE(), ' 12:30:00'), 200, 20, 60, 120, 'PROGRAMME'),
(11, CONCAT(CURDATE(), ' 14:00:00'), CONCAT(CURDATE(), ' 16:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(11, CONCAT(CURDATE(), ' 17:30:00'), CONCAT(CURDATE(), ' 19:30:00'), 200, 20, 60, 120, 'PROGRAMME'),
(11, CONCAT(CURDATE(), ' 20:00:00'), CONCAT(CURDATE(), ' 22:00:00'), 200, 20, 60, 120, 'PROGRAMME'),

-- TUNIS - SFAX (2 départs par jour)
(2, CONCAT(CURDATE(), ' 08:00:00'), CONCAT(CURDATE(), ' 11:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(2, CONCAT(CURDATE(), ' 15:00:00'), CONCAT(CURDATE(), ' 18:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(2, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 08:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 11:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(2, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 15:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 18:00:00'), 200, 20, 60, 120, 'PROGRAMME'),

-- SFAX - TUNIS (retour)
(14, CONCAT(CURDATE(), ' 09:00:00'), CONCAT(CURDATE(), ' 12:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(14, CONCAT(CURDATE(), ' 16:00:00'), CONCAT(CURDATE(), ' 19:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(14, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 09:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 12:00:00'), 200, 20, 60, 120, 'PROGRAMME'),
(14, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 16:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 19:00:00'), 200, 20, 60, 120, 'PROGRAMME'),

-- TUNIS - GABÈS (1 départ par jour)
(3, CONCAT(CURDATE(), ' 07:30:00'), CONCAT(CURDATE(), ' 12:30:00'), 180, 18, 54, 108, 'PROGRAMME'),
(3, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 07:30:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 12:30:00'), 180, 18, 54, 108, 'PROGRAMME'),
(3, CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 07:30:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 12:30:00'), 180, 18, 54, 108, 'PROGRAMME'),

-- GABÈS - TUNIS (retour)
(18, CONCAT(CURDATE(), ' 14:00:00'), CONCAT(CURDATE(), ' 19:00:00'), 180, 18, 54, 108, 'PROGRAMME'),
(18, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 14:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 19:00:00'), 180, 18, 54, 108, 'PROGRAMME'),
(18, CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 14:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 19:00:00'), 180, 18, 54, 108, 'PROGRAMME'),

-- SOUSSE - SFAX
(9, CONCAT(CURDATE(), ' 10:00:00'), CONCAT(CURDATE(), ' 11:30:00'), 180, 18, 54, 108, 'PROGRAMME'),
(9, CONCAT(CURDATE(), ' 15:30:00'), CONCAT(CURDATE(), ' 17:00:00'), 180, 18, 54, 108, 'PROGRAMME'),
(9, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 10:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 11:30:00'), 180, 18, 54, 108, 'PROGRAMME'),
(9, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 15:30:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 17:00:00'), 180, 18, 54, 108, 'PROGRAMME'),

-- SFAX - SOUSSE (retour)
(15, CONCAT(CURDATE(), ' 12:00:00'), CONCAT(CURDATE(), ' 13:30:00'), 180, 18, 54, 108, 'PROGRAMME'),
(15, CONCAT(CURDATE(), ' 17:30:00'), CONCAT(CURDATE(), ' 19:00:00'), 180, 18, 54, 108, 'PROGRAMME'),
(15, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 12:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 13:30:00'), 180, 18, 54, 108, 'PROGRAMME'),
(15, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 17:30:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 19:00:00'), 180, 18, 54, 108, 'PROGRAMME'),

-- TUNIS - BIZERTE
(4, CONCAT(CURDATE(), ' 08:30:00'), CONCAT(CURDATE(), ' 10:00:00'), 150, 15, 45, 90, 'PROGRAMME'),
(4, CONCAT(CURDATE(), ' 14:00:00'), CONCAT(CURDATE(), ' 15:30:00'), 150, 15, 45, 90, 'PROGRAMME'),
(4, CONCAT(CURDATE(), ' 18:00:00'), CONCAT(CURDATE(), ' 19:30:00'), 150, 15, 45, 90, 'PROGRAMME'),

-- BIZERTE - TUNIS (retour)
(32, CONCAT(CURDATE(), ' 09:30:00'), CONCAT(CURDATE(), ' 11:00:00'), 150, 15, 45, 90, 'PROGRAMME'),
(32, CONCAT(CURDATE(), ' 15:00:00'), CONCAT(CURDATE(), ' 16:30:00'), 150, 15, 45, 90, 'PROGRAMME'),
(32, CONCAT(CURDATE(), ' 19:30:00'), CONCAT(CURDATE(), ' 21:00:00'), 150, 15, 45, 90, 'PROGRAMME'),

-- SOUSSE - MONASTIR
(10, CONCAT(CURDATE(), ' 09:00:00'), CONCAT(CURDATE(), ' 09:45:00'), 150, 15, 45, 90, 'PROGRAMME'),
(10, CONCAT(CURDATE(), ' 12:00:00'), CONCAT(CURDATE(), ' 12:45:00'), 150, 15, 45, 90, 'PROGRAMME'),
(10, CONCAT(CURDATE(), ' 16:00:00'), CONCAT(CURDATE(), ' 16:45:00'), 150, 15, 45, 90, 'PROGRAMME'),
(10, CONCAT(CURDATE(), ' 19:00:00'), CONCAT(CURDATE(), ' 19:45:00'), 150, 15, 45, 90, 'PROGRAMME'),

-- SOUSSE - MAHDIA
(11, CONCAT(CURDATE(), ' 11:00:00'), CONCAT(CURDATE(), ' 12:00:00'), 150, 15, 45, 90, 'PROGRAMME'),
(11, CONCAT(CURDATE(), ' 17:00:00'), CONCAT(CURDATE(), ' 18:00:00'), 150, 15, 45, 90, 'PROGRAMME'),

-- SFAX - GABÈS
(13, CONCAT(CURDATE(), ' 13:00:00'), CONCAT(CURDATE(), ' 15:00:00'), 180, 18, 54, 108, 'PROGRAMME'),
(13, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 13:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 15:00:00'), 180, 18, 54, 108, 'PROGRAMME'),

-- GABÈS - SFAX (retour)
(19, CONCAT(CURDATE(), ' 16:00:00'), CONCAT(CURDATE(), ' 18:00:00'), 180, 18, 54, 108, 'PROGRAMME'),
(19, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 16:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 18:00:00'), 180, 18, 54, 108, 'PROGRAMME'),

-- SFAX - GAFSA
(14, CONCAT(CURDATE(), ' 11:00:00'), CONCAT(CURDATE(), ' 13:30:00'), 150, 15, 45, 90, 'PROGRAMME'),
(14, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 11:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 13:30:00'), 150, 15, 45, 90, 'PROGRAMME'),

-- GABÈS - GAFSA
(16, CONCAT(CURDATE(), ' 10:30:00'), CONCAT(CURDATE(), ' 12:00:00'), 150, 15, 45, 90, 'PROGRAMME'),
(16, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 10:30:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 12:00:00'), 150, 15, 45, 90, 'PROGRAMME'),

-- GABÈS - TOZEUR
(17, CONCAT(CURDATE(), ' 14:30:00'), CONCAT(CURDATE(), ' 16:30:00'), 120, 12, 36, 72, 'PROGRAMME'),
(17, CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 14:30:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 2 DAY), ' 16:30:00'), 120, 12, 36, 72, 'PROGRAMME'),

-- GAFSA - TOZEUR
(20, CONCAT(CURDATE(), ' 15:00:00'), CONCAT(CURDATE(), ' 16:00:00'), 120, 12, 36, 72, 'PROGRAMME'),
(20, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 15:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 16:00:00'), 120, 12, 36, 72, 'PROGRAMME'),

-- TUNIS - KAIROUAN
(5, CONCAT(CURDATE(), ' 09:00:00'), CONCAT(CURDATE(), ' 11:30:00'), 180, 18, 54, 108, 'PROGRAMME'),
(5, CONCAT(CURDATE(), ' 15:00:00'), CONCAT(CURDATE(), ' 17:30:00'), 180, 18, 54, 108, 'PROGRAMME'),

-- TUNIS - BÉJA
(6, CONCAT(CURDATE(), ' 10:00:00'), CONCAT(CURDATE(), ' 12:00:00'), 150, 15, 45, 90, 'PROGRAMME'),
(6, CONCAT(CURDATE(), ' 16:00:00'), CONCAT(CURDATE(), ' 18:00:00'), 150, 15, 45, 90, 'PROGRAMME'),

-- TUNIS - JENDOUBA
(7, CONCAT(CURDATE(), ' 08:00:00'), CONCAT(CURDATE(), ' 11:00:00'), 150, 15, 45, 90, 'PROGRAMME'),
(7, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 08:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 11:00:00'), 150, 15, 45, 90, 'PROGRAMME'),

-- JENDOUBA - TUNIS (retour)
(33, CONCAT(CURDATE(), ' 12:00:00'), CONCAT(CURDATE(), ' 15:00:00'), 150, 15, 45, 90, 'PROGRAMME'),
(33, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 12:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 15:00:00'), 150, 15, 45, 90, 'PROGRAMME');

SELECT 'Voyages insérés avec succès!' as message;
SELECT COUNT(*) as nombre_voyages FROM voyages;
SELECT COUNT(*) as voyages_aujourd_hui FROM voyages WHERE DATE(date_heure_depart) = CURDATE();
SELECT COUNT(*) as voyages_demain FROM voyages WHERE DATE(date_heure_depart) = DATE_ADD(CURDATE(), INTERVAL 1 DAY);
